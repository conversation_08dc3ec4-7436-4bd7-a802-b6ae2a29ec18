{"version": 3, "sources": ["../../immer/src/utils/errors.ts", "../../immer/src/utils/common.ts", "../../immer/src/utils/plugins.ts", "../../immer/src/core/scope.ts", "../../immer/src/core/finalize.ts", "../../immer/src/core/proxy.ts", "../../immer/src/core/immerClass.ts", "../../immer/src/core/current.ts", "../../immer/src/plugins/es5.ts", "../../immer/src/plugins/patches.ts", "../../immer/src/plugins/mapset.ts", "../../immer/src/plugins/all.ts", "../../immer/src/immer.ts", "../../immer/src/utils/env.ts", "../../reselect/es/defaultMemoize.js", "../../reselect/es/index.js", "../../redux-thunk/es/index.js", "../../@reduxjs/toolkit/src/index.ts", "../../@reduxjs/toolkit/src/createDraftSafeSelector.ts", "../../@reduxjs/toolkit/src/configureStore.ts", "../../@reduxjs/toolkit/src/devtoolsExtension.ts", "../../@reduxjs/toolkit/src/isPlainObject.ts", "../../@reduxjs/toolkit/src/getDefaultMiddleware.ts", "../../@reduxjs/toolkit/src/tsHelpers.ts", "../../@reduxjs/toolkit/src/createAction.ts", "../../@reduxjs/toolkit/src/actionCreatorInvariantMiddleware.ts", "../../@reduxjs/toolkit/src/utils.ts", "../../@reduxjs/toolkit/src/immutableStateInvariantMiddleware.ts", "../../@reduxjs/toolkit/src/serializableStateInvariantMiddleware.ts", "../../@reduxjs/toolkit/src/createReducer.ts", "../../@reduxjs/toolkit/src/mapBuilders.ts", "../../@reduxjs/toolkit/src/createSlice.ts", "../../@reduxjs/toolkit/src/entities/entity_state.ts", "../../@reduxjs/toolkit/src/entities/state_selectors.ts", "../../@reduxjs/toolkit/src/entities/state_adapter.ts", "../../@reduxjs/toolkit/src/entities/utils.ts", "../../@reduxjs/toolkit/src/entities/unsorted_state_adapter.ts", "../../@reduxjs/toolkit/src/entities/sorted_state_adapter.ts", "../../@reduxjs/toolkit/src/entities/create_adapter.ts", "../../@reduxjs/toolkit/src/nanoid.ts", "../../@reduxjs/toolkit/src/createAsyncThunk.ts", "../../@reduxjs/toolkit/src/matchers.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/utils.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/exceptions.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/task.ts", "../../@reduxjs/toolkit/src/listenerMiddleware/index.ts", "../../@reduxjs/toolkit/src/autoBatchEnhancer.ts", "../../@reduxjs/toolkit/dist/redux-toolkit.esm.js"], "sourcesContent": ["const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n", "// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n", "// Cache implementation based on <PERSON>'s `lru-memoize`:\n// https://github.com/erikras/lru-memoize\nvar NOT_FOUND = 'NOT_FOUND';\n\nfunction createSingletonCache(equals) {\n  var entry;\n  return {\n    get: function get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n\n      return NOT_FOUND;\n    },\n    put: function put(key, value) {\n      entry = {\n        key: key,\n        value: value\n      };\n    },\n    getEntries: function getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear: function clear() {\n      entry = undefined;\n    }\n  };\n}\n\nfunction createLruCache(maxSize, equals) {\n  var entries = [];\n\n  function get(key) {\n    var cacheIndex = entries.findIndex(function (entry) {\n      return equals(key, entry.key);\n    }); // We found a cached entry\n\n    if (cacheIndex > -1) {\n      var entry = entries[cacheIndex]; // Cached entry not at top of cache, move it to the top\n\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n\n      return entry.value;\n    } // No entry found in cache, return sentinel\n\n\n    return NOT_FOUND;\n  }\n\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      // TODO Is unshift slow?\n      entries.unshift({\n        key: key,\n        value: value\n      });\n\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n\n  function getEntries() {\n    return entries;\n  }\n\n  function clear() {\n    entries = [];\n  }\n\n  return {\n    get: get,\n    put: put,\n    getEntries: getEntries,\n    clear: clear\n  };\n}\n\nexport var defaultEqualityCheck = function defaultEqualityCheck(a, b) {\n  return a === b;\n};\nexport function createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    } // Do this in a for loop (and not a `forEach` or an `every`) so we can determine equality as fast as possible.\n\n\n    var length = prev.length;\n\n    for (var i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n\n    return true;\n  };\n}\n// defaultMemoize now supports a configurable cache size with LRU behavior,\n// and optional comparison of the result value with existing values\nexport function defaultMemoize(func, equalityCheckOrOptions) {\n  var providedOptions = typeof equalityCheckOrOptions === 'object' ? equalityCheckOrOptions : {\n    equalityCheck: equalityCheckOrOptions\n  };\n  var _providedOptions$equa = providedOptions.equalityCheck,\n      equalityCheck = _providedOptions$equa === void 0 ? defaultEqualityCheck : _providedOptions$equa,\n      _providedOptions$maxS = providedOptions.maxSize,\n      maxSize = _providedOptions$maxS === void 0 ? 1 : _providedOptions$maxS,\n      resultEqualityCheck = providedOptions.resultEqualityCheck;\n  var comparator = createCacheKeyComparator(equalityCheck);\n  var cache = maxSize === 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator); // we reference arguments instead of spreading them for performance reasons\n\n  function memoized() {\n    var value = cache.get(arguments);\n\n    if (value === NOT_FOUND) {\n      // @ts-ignore\n      value = func.apply(null, arguments);\n\n      if (resultEqualityCheck) {\n        var entries = cache.getEntries();\n        var matchingEntry = entries.find(function (entry) {\n          return resultEqualityCheck(entry.value, value);\n        });\n\n        if (matchingEntry) {\n          value = matchingEntry.value;\n        }\n      }\n\n      cache.put(arguments, value);\n    }\n\n    return value;\n  }\n\n  memoized.clearCache = function () {\n    return cache.clear();\n  };\n\n  return memoized;\n}", "import { defaultMemoize, defaultEqualityCheck } from './defaultMemoize';\nexport { defaultMemoize, defaultEqualityCheck };\n\nfunction getDependencies(funcs) {\n  var dependencies = Array.isArray(funcs[0]) ? funcs[0] : funcs;\n\n  if (!dependencies.every(function (dep) {\n    return typeof dep === 'function';\n  })) {\n    var dependencyTypes = dependencies.map(function (dep) {\n      return typeof dep === 'function' ? \"function \" + (dep.name || 'unnamed') + \"()\" : typeof dep;\n    }).join(', ');\n    throw new Error(\"createSelector expects all input-selectors to be functions, but received the following types: [\" + dependencyTypes + \"]\");\n  }\n\n  return dependencies;\n}\n\nexport function createSelectorCreator(memoize) {\n  for (var _len = arguments.length, memoizeOptionsFromArgs = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    memoizeOptionsFromArgs[_key - 1] = arguments[_key];\n  }\n\n  var createSelector = function createSelector() {\n    for (var _len2 = arguments.length, funcs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      funcs[_key2] = arguments[_key2];\n    }\n\n    var _recomputations = 0;\n\n    var _lastResult; // Due to the intricacies of rest params, we can't do an optional arg after `...funcs`.\n    // So, start by declaring the default value here.\n    // (And yes, the words 'memoize' and 'options' appear too many times in this next sequence.)\n\n\n    var directlyPassedOptions = {\n      memoizeOptions: undefined\n    }; // Normally, the result func or \"output selector\" is the last arg\n\n    var resultFunc = funcs.pop(); // If the result func is actually an _object_, assume it's our options object\n\n    if (typeof resultFunc === 'object') {\n      directlyPassedOptions = resultFunc; // and pop the real result func off\n\n      resultFunc = funcs.pop();\n    }\n\n    if (typeof resultFunc !== 'function') {\n      throw new Error(\"createSelector expects an output function after the inputs, but received: [\" + typeof resultFunc + \"]\");\n    } // Determine which set of options we're using. Prefer options passed directly,\n    // but fall back to options given to createSelectorCreator.\n\n\n    var _directlyPassedOption = directlyPassedOptions,\n        _directlyPassedOption2 = _directlyPassedOption.memoizeOptions,\n        memoizeOptions = _directlyPassedOption2 === void 0 ? memoizeOptionsFromArgs : _directlyPassedOption2; // Simplifying assumption: it's unlikely that the first options arg of the provided memoizer\n    // is an array. In most libs I've looked at, it's an equality function or options object.\n    // Based on that, if `memoizeOptions` _is_ an array, we assume it's a full\n    // user-provided array of options. Otherwise, it must be just the _first_ arg, and so\n    // we wrap it in an array so we can apply it.\n\n    var finalMemoizeOptions = Array.isArray(memoizeOptions) ? memoizeOptions : [memoizeOptions];\n    var dependencies = getDependencies(funcs);\n    var memoizedResultFunc = memoize.apply(void 0, [function recomputationWrapper() {\n      _recomputations++; // apply arguments instead of spreading for performance.\n\n      return resultFunc.apply(null, arguments);\n    }].concat(finalMemoizeOptions)); // If a selector is called with the exact same arguments we don't need to traverse our dependencies again.\n\n    var selector = memoize(function dependenciesChecker() {\n      var params = [];\n      var length = dependencies.length;\n\n      for (var i = 0; i < length; i++) {\n        // apply arguments instead of spreading and mutate a local list of params for performance.\n        // @ts-ignore\n        params.push(dependencies[i].apply(null, arguments));\n      } // apply arguments instead of spreading for performance.\n\n\n      _lastResult = memoizedResultFunc.apply(null, params);\n      return _lastResult;\n    });\n    Object.assign(selector, {\n      resultFunc: resultFunc,\n      memoizedResultFunc: memoizedResultFunc,\n      dependencies: dependencies,\n      lastResult: function lastResult() {\n        return _lastResult;\n      },\n      recomputations: function recomputations() {\n        return _recomputations;\n      },\n      resetRecomputations: function resetRecomputations() {\n        return _recomputations = 0;\n      }\n    });\n    return selector;\n  }; // @ts-ignore\n\n\n  return createSelector;\n}\nexport var createSelector = /* #__PURE__ */createSelectorCreator(defaultMemoize);\n// Manual definition of state and output arguments\nexport var createStructuredSelector = function createStructuredSelector(selectors, selectorCreator) {\n  if (selectorCreator === void 0) {\n    selectorCreator = createSelector;\n  }\n\n  if (typeof selectors !== 'object') {\n    throw new Error('createStructuredSelector expects first argument to be an object ' + (\"where each property is a selector, instead received a \" + typeof selectors));\n  }\n\n  var objectKeys = Object.keys(selectors);\n  var resultSelector = selectorCreator( // @ts-ignore\n  objectKeys.map(function (key) {\n    return selectors[key];\n  }), function () {\n    for (var _len3 = arguments.length, values = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      values[_key3] = arguments[_key3];\n    }\n\n    return values.reduce(function (composition, value, index) {\n      composition[objectKeys[index]] = value;\n      return composition;\n    }, {});\n  });\n  return resultSelector;\n};", "/** A function that accepts a potential \"extra argument\" value to be injected later,\r\n * and returns an instance of the thunk middleware that uses that value\r\n */\nfunction createThunkMiddleware(extraArgument) {\n  // Standard Redux middleware definition pattern:\n  // See: https://redux.js.org/tutorials/fundamentals/part-4-store#writing-custom-middleware\n  var middleware = function middleware(_ref) {\n    var dispatch = _ref.dispatch,\n        getState = _ref.getState;\n    return function (next) {\n      return function (action) {\n        // The thunk middleware looks for any functions that were passed to `store.dispatch`.\n        // If this \"action\" is really a function, call it and return the result.\n        if (typeof action === 'function') {\n          // Inject the store's `dispatch` and `getState` methods, as well as any \"extra arg\"\n          return action(dispatch, getState, extraArgument);\n        } // Otherwise, pass the action down the middleware chain as usual\n\n\n        return next(action);\n      };\n    };\n  };\n\n  return middleware;\n}\n\nvar thunk = createThunkMiddleware(); // Attach the factory function so users can create a customized version\n// with whatever \"extra arg\" they want to inject into their thunks\n\nthunk.withExtraArgument = createThunkMiddleware;\nexport default thunk;", "import { enableES5 } from 'immer'\r\nexport * from 'redux'\r\nexport {\r\n  default as createNextState,\r\n  current,\r\n  freeze,\r\n  original,\r\n  isDraft,\r\n} from 'immer'\r\nexport type { Draft } from 'immer'\r\nexport { createSelector } from 'reselect'\r\nexport type {\r\n  Selector,\r\n  OutputParametricSelector,\r\n  OutputSelector,\r\n  ParametricSelector,\r\n} from 'reselect'\r\nexport { createDraftSafeSelector } from './createDraftSafeSelector'\r\nexport type { ThunkAction, ThunkDispatch, ThunkMiddleware } from 'redux-thunk'\r\n\r\n// We deliberately enable Immer's ES5 support, on the grounds that\r\n// we assume RTK will be used with React Native and other Proxy-less\r\n// environments.  In addition, that's how Immer 4 behaved, and since\r\n// we want to ship this in an RTK minor, we should keep the same behavior.\r\nenableES5()\r\n\r\nexport {\r\n  // js\r\n  configureStore,\r\n} from './configureStore'\r\nexport type {\r\n  // types\r\n  ConfigureEnhancersCallback,\r\n  ConfigureStoreOptions,\r\n  EnhancedStore,\r\n} from './configureStore'\r\nexport type { DevToolsEnhancerOptions } from './devtoolsExtension'\r\nexport {\r\n  // js\r\n  createAction,\r\n  getType,\r\n  isAction,\r\n  isActionCreator,\r\n  isFSA as isFluxStandardAction,\r\n} from './createAction'\r\nexport type {\r\n  // types\r\n  PayloadAction,\r\n  PayloadActionCreator,\r\n  ActionCreatorWithNonInferrablePayload,\r\n  ActionCreatorWithOptionalPayload,\r\n  ActionCreatorWithPayload,\r\n  ActionCreatorWithoutPayload,\r\n  ActionCreatorWithPreparedPayload,\r\n  PrepareAction,\r\n} from './createAction'\r\nexport {\r\n  // js\r\n  createReducer,\r\n} from './createReducer'\r\nexport type {\r\n  // types\r\n  Actions,\r\n  CaseReducer,\r\n  CaseReducers,\r\n} from './createReducer'\r\nexport {\r\n  // js\r\n  createSlice,\r\n} from './createSlice'\r\n\r\nexport type {\r\n  // types\r\n  CreateSliceOptions,\r\n  Slice,\r\n  CaseReducerActions,\r\n  SliceCaseReducers,\r\n  ValidateSliceCaseReducers,\r\n  CaseReducerWithPrepare,\r\n  SliceActionCreator,\r\n} from './createSlice'\r\nexport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware'\r\nexport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware'\r\nexport {\r\n  // js\r\n  createImmutableStateInvariantMiddleware,\r\n  isImmutableDefault,\r\n} from './immutableStateInvariantMiddleware'\r\nexport type {\r\n  // types\r\n  ImmutableStateInvariantMiddlewareOptions,\r\n} from './immutableStateInvariantMiddleware'\r\nexport {\r\n  // js\r\n  createSerializableStateInvariantMiddleware,\r\n  findNonSerializableValue,\r\n  isPlain,\r\n} from './serializableStateInvariantMiddleware'\r\nexport type {\r\n  // types\r\n  SerializableStateInvariantMiddlewareOptions,\r\n} from './serializableStateInvariantMiddleware'\r\nexport {\r\n  // js\r\n  getDefaultMiddleware,\r\n} from './getDefaultMiddleware'\r\nexport type {\r\n  // types\r\n  ActionReducerMapBuilder,\r\n} from './mapBuilders'\r\nexport { MiddlewareArray, EnhancerArray } from './utils'\r\n\r\nexport { createEntityAdapter } from './entities/create_adapter'\r\nexport type {\r\n  Dictionary,\r\n  EntityState,\r\n  EntityAdapter,\r\n  EntitySelectors,\r\n  EntityStateAdapter,\r\n  EntityId,\r\n  Update,\r\n  IdSelector,\r\n  Comparer,\r\n} from './entities/models'\r\n\r\nexport {\r\n  createAsyncThunk,\r\n  unwrapResult,\r\n  miniSerializeError,\r\n} from './createAsyncThunk'\r\nexport type {\r\n  AsyncThunk,\r\n  AsyncThunkOptions,\r\n  AsyncThunkAction,\r\n  AsyncThunkPayloadCreatorReturnValue,\r\n  AsyncThunkPayloadCreator,\r\n  SerializedError,\r\n} from './createAsyncThunk'\r\n\r\nexport {\r\n  // js\r\n  isAllOf,\r\n  isAnyOf,\r\n  isPending,\r\n  isRejected,\r\n  isFulfilled,\r\n  isAsyncThunkAction,\r\n  isRejectedWithValue,\r\n} from './matchers'\r\nexport type {\r\n  // types\r\n  ActionMatchingAllOf,\r\n  ActionMatchingAnyOf,\r\n} from './matchers'\r\n\r\nexport { nanoid } from './nanoid'\r\n\r\nexport { default as isPlainObject } from './isPlainObject'\r\n\r\nexport type {\r\n  ListenerEffect,\r\n  ListenerMiddleware,\r\n  ListenerEffectAPI,\r\n  ListenerMiddlewareInstance,\r\n  CreateListenerMiddlewareOptions,\r\n  ListenerErrorHandler,\r\n  TypedStartListening,\r\n  TypedAddListener,\r\n  TypedStopListening,\r\n  TypedRemoveListener,\r\n  UnsubscribeListener,\r\n  UnsubscribeListenerOptions,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  ForkedTaskAPI,\r\n  AsyncTaskExecutor,\r\n  SyncTaskExecutor,\r\n  TaskCancelled,\r\n  TaskRejected,\r\n  TaskResolved,\r\n  TaskResult,\r\n} from './listenerMiddleware/index'\r\nexport type { AnyListenerPredicate } from './listenerMiddleware/types'\r\n\r\nexport {\r\n  createListenerMiddleware,\r\n  addListener,\r\n  removeListener,\r\n  clearAllListeners,\r\n  TaskAbortError,\r\n} from './listenerMiddleware/index'\r\n\r\nexport {\r\n  SHOULD_AUTOBATCH,\r\n  prepareAutoBatched,\r\n  autoBatchEnhancer,\r\n} from './autoBatchEnhancer'\r\nexport type { AutoBatchOptions } from './autoBatchEnhancer'\r\n\r\nexport type { ExtractDispatchExtensions as TSHelpersExtractDispatchExtensions } from './tsHelpers'\r\n", "import { current, isDraft } from 'immer'\r\nimport { createSelector } from 'reselect'\r\n\r\n/**\r\n * \"Draft-Safe\" version of `reselect`'s `createSelector`:\r\n * If an `immer`-drafted object is passed into the resulting selector's first argument,\r\n * the selector will act on the current draft value, instead of returning a cached value\r\n * that might be possibly outdated if the draft has been modified since.\r\n * @public\r\n */\r\nexport const createDraftSafeSelector: typeof createSelector = (\r\n  ...args: unknown[]\r\n) => {\r\n  const selector = (createSelector as any)(...args)\r\n  const wrappedSelector = (value: unknown, ...rest: unknown[]) =>\r\n    selector(isDraft(value) ? current(value) : value, ...rest)\r\n  return wrappedSelector as any\r\n}\r\n", "import type {\r\n  Reducer,\r\n  ReducersMapObject,\r\n  Middleware,\r\n  Action,\r\n  AnyAction,\r\n  StoreEnhancer,\r\n  Store,\r\n  Dispatch,\r\n  PreloadedState,\r\n  CombinedState,\r\n} from 'redux'\r\nimport { createStore, compose, applyMiddleware, combineReducers } from 'redux'\r\nimport type { DevToolsEnhancerOptions as DevToolsOptions } from './devtoolsExtension'\r\nimport { composeWithDevTools } from './devtoolsExtension'\r\n\r\nimport isPlainObject from './isPlainObject'\r\nimport type {\r\n  ThunkMiddlewareFor,\r\n  CurriedGetDefaultMiddleware,\r\n} from './getDefaultMiddleware'\r\nimport { curryGetDefaultMiddleware } from './getDefaultMiddleware'\r\nimport type {\r\n  NoInfer,\r\n  ExtractDispatchExtensions,\r\n  ExtractStoreExtensions,\r\n  ExtractStateExtensions,\r\n} from './tsHelpers'\r\nimport { EnhancerArray } from './utils'\r\n\r\nconst IS_PRODUCTION = process.env.NODE_ENV === 'production'\r\n\r\n/**\r\n * Callback function type, to be used in `ConfigureStoreOptions.enhancers`\r\n *\r\n * @public\r\n */\r\nexport type ConfigureEnhancersCallback<E extends Enhancers = Enhancers> = (\r\n  defaultEnhancers: EnhancerArray<[StoreEnhancer<{}, {}>]>\r\n) => E\r\n\r\n/**\r\n * Options for `configureStore()`.\r\n *\r\n * @public\r\n */\r\nexport interface ConfigureStoreOptions<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>,\r\n  E extends Enhancers = Enhancers\r\n> {\r\n  /**\r\n   * A single reducer function that will be used as the root reducer, or an\r\n   * object of slice reducers that will be passed to `combineReducers()`.\r\n   */\r\n  reducer: Reducer<S, A> | ReducersMapObject<S, A>\r\n\r\n  /**\r\n   * An array of Redux middleware to install. If not supplied, defaults to\r\n   * the set of middleware returned by `getDefaultMiddleware()`.\r\n   *\r\n   * @example `middleware: (gDM) => gDM().concat(logger, apiMiddleware, yourCustomMiddleware)`\r\n   * @see https://redux-toolkit.js.org/api/getDefaultMiddleware#intended-usage\r\n   */\r\n  middleware?: ((getDefaultMiddleware: CurriedGetDefaultMiddleware<S>) => M) | M\r\n\r\n  /**\r\n   * Whether to enable Redux DevTools integration. Defaults to `true`.\r\n   *\r\n   * Additional configuration can be done by passing Redux DevTools options\r\n   */\r\n  devTools?: boolean | DevToolsOptions\r\n\r\n  /**\r\n   * The initial state, same as Redux's createStore.\r\n   * You may optionally specify it to hydrate the state\r\n   * from the server in universal apps, or to restore a previously serialized\r\n   * user session. If you use `combineReducers()` to produce the root reducer\r\n   * function (either directly or indirectly by passing an object as `reducer`),\r\n   * this must be an object with the same shape as the reducer map keys.\r\n   */\r\n  /*\r\n  Not 100% correct but the best approximation we can get:\r\n  - if S is a `CombinedState` applying a second `CombinedState` on it does not change anything.\r\n  - if it is not, there could be two cases:\r\n    - `ReducersMapObject<S, A>` is being passed in. In this case, we will call `combineReducers` on it and `CombinedState<S>` is correct\r\n    - `Reducer<S, A>` is being passed in. In this case, actually `CombinedState<S>` is wrong and `S` would be correct.\r\n    As we cannot distinguish between those two cases without adding another generic parameter,\r\n    we just make the pragmatic assumption that the latter almost never happens.\r\n  */\r\n  preloadedState?: PreloadedState<CombinedState<NoInfer<S>>>\r\n\r\n  /**\r\n   * The store enhancers to apply. See Redux's `createStore()`.\r\n   * All enhancers will be included before the DevTools Extension enhancer.\r\n   * If you need to customize the order of enhancers, supply a callback\r\n   * function that will receive the original array (ie, `[applyMiddleware]`),\r\n   * and should return a new array (such as `[applyMiddleware, offline]`).\r\n   * If you only need to add middleware, you can use the `middleware` parameter instead.\r\n   */\r\n  enhancers?: E | ConfigureEnhancersCallback<E>\r\n}\r\n\r\ntype Middlewares<S> = ReadonlyArray<Middleware<{}, S>>\r\n\r\ntype Enhancers = ReadonlyArray<StoreEnhancer>\r\n\r\nexport interface ToolkitStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>\r\n> extends Store<S, A> {\r\n  /**\r\n   * The `dispatch` method of your store, enhanced by all its middlewares.\r\n   *\r\n   * @inheritdoc\r\n   */\r\n  dispatch: ExtractDispatchExtensions<M> & Dispatch<A>\r\n}\r\n\r\n/**\r\n * A Redux store returned by `configureStore()`. Supports dispatching\r\n * side-effectful _thunks_ in addition to plain actions.\r\n *\r\n * @public\r\n */\r\nexport type EnhancedStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>,\r\n  E extends Enhancers = Enhancers\r\n> = ToolkitStore<S & ExtractStateExtensions<E>, A, M> &\r\n  ExtractStoreExtensions<E>\r\n\r\n/**\r\n * A friendly abstraction over the standard Redux `createStore()` function.\r\n *\r\n * @param options The store configuration.\r\n * @returns A configured Redux store.\r\n *\r\n * @public\r\n */\r\nexport function configureStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = [ThunkMiddlewareFor<S>],\r\n  E extends Enhancers = [StoreEnhancer]\r\n>(options: ConfigureStoreOptions<S, A, M, E>): EnhancedStore<S, A, M, E> {\r\n  const curriedGetDefaultMiddleware = curryGetDefaultMiddleware<S>()\r\n\r\n  const {\r\n    reducer = undefined,\r\n    middleware = curriedGetDefaultMiddleware(),\r\n    devTools = true,\r\n    preloadedState = undefined,\r\n    enhancers = undefined,\r\n  } = options || {}\r\n\r\n  let rootReducer: Reducer<S, A>\r\n\r\n  if (typeof reducer === 'function') {\r\n    rootReducer = reducer\r\n  } else if (isPlainObject(reducer)) {\r\n    rootReducer = combineReducers(reducer) as unknown as Reducer<S, A>\r\n  } else {\r\n    throw new Error(\r\n      '\"reducer\" is a required argument, and must be a function or an object of functions that can be passed to combineReducers'\r\n    )\r\n  }\r\n\r\n  let finalMiddleware = middleware\r\n  if (typeof finalMiddleware === 'function') {\r\n    finalMiddleware = finalMiddleware(curriedGetDefaultMiddleware)\r\n\r\n    if (!IS_PRODUCTION && !Array.isArray(finalMiddleware)) {\r\n      throw new Error(\r\n        'when using a middleware builder function, an array of middleware must be returned'\r\n      )\r\n    }\r\n  }\r\n  if (\r\n    !IS_PRODUCTION &&\r\n    finalMiddleware.some((item: any) => typeof item !== 'function')\r\n  ) {\r\n    throw new Error(\r\n      'each middleware provided to configureStore must be a function'\r\n    )\r\n  }\r\n\r\n  const middlewareEnhancer: StoreEnhancer = applyMiddleware(...finalMiddleware)\r\n\r\n  let finalCompose = compose\r\n\r\n  if (devTools) {\r\n    finalCompose = composeWithDevTools({\r\n      // Enable capture of stack traces for dispatched Redux actions\r\n      trace: !IS_PRODUCTION,\r\n      ...(typeof devTools === 'object' && devTools),\r\n    })\r\n  }\r\n\r\n  const defaultEnhancers = new EnhancerArray(middlewareEnhancer)\r\n  let storeEnhancers: Enhancers = defaultEnhancers\r\n\r\n  if (Array.isArray(enhancers)) {\r\n    storeEnhancers = [middlewareEnhancer, ...enhancers]\r\n  } else if (typeof enhancers === 'function') {\r\n    storeEnhancers = enhancers(defaultEnhancers)\r\n  }\r\n\r\n  const composedEnhancer = finalCompose(...storeEnhancers) as StoreEnhancer<any>\r\n\r\n  return createStore(rootReducer, preloadedState, composedEnhancer)\r\n}\r\n", "import type { Action, ActionCreator, StoreEnhancer } from 'redux'\r\nimport { compose } from 'redux'\r\n\r\n/**\r\n * @public\r\n */\r\nexport interface DevToolsEnhancerOptions {\r\n  /**\r\n   * the instance name to be showed on the monitor page. Default value is `document.title`.\r\n   * If not specified and there's no document title, it will consist of `tabId` and `instanceId`.\r\n   */\r\n  name?: string\r\n  /**\r\n   * action creators functions to be available in the Dispatcher.\r\n   */\r\n  actionCreators?: ActionCreator<any>[] | { [key: string]: ActionCreator<any> }\r\n  /**\r\n   * if more than one action is dispatched in the indicated interval, all new actions will be collected and sent at once.\r\n   * It is the joint between performance and speed. When set to `0`, all actions will be sent instantly.\r\n   * Set it to a higher value when experiencing perf issues (also `maxAge` to a lower value).\r\n   *\r\n   * @default 500 ms.\r\n   */\r\n  latency?: number\r\n  /**\r\n   * (> 1) - maximum allowed actions to be stored in the history tree. The oldest actions are removed once maxAge is reached. It's critical for performance.\r\n   *\r\n   * @default 50\r\n   */\r\n  maxAge?: number\r\n  /**\r\n   * Customizes how actions and state are serialized and deserialized. Can be a boolean or object. If given a boolean, the behavior is the same as if you\r\n   * were to pass an object and specify `options` as a boolean. Giving an object allows fine-grained customization using the `replacer` and `reviver`\r\n   * functions.\r\n   */\r\n  serialize?:\r\n    | boolean\r\n    | {\r\n        /**\r\n         * - `undefined` - will use regular `JSON.stringify` to send data (it's the fast mode).\r\n         * - `false` - will handle also circular references.\r\n         * - `true` - will handle also date, regex, undefined, error objects, symbols, maps, sets and functions.\r\n         * - object, which contains `date`, `regex`, `undefined`, `error`, `symbol`, `map`, `set` and `function` keys.\r\n         *   For each of them you can indicate if to include (by setting as `true`).\r\n         *   For `function` key you can also specify a custom function which handles serialization.\r\n         *   See [`jsan`](https://github.com/kolodny/jsan) for more details.\r\n         */\r\n        options?:\r\n          | undefined\r\n          | boolean\r\n          | {\r\n              date?: true\r\n              regex?: true\r\n              undefined?: true\r\n              error?: true\r\n              symbol?: true\r\n              map?: true\r\n              set?: true\r\n              function?: true | ((fn: (...args: any[]) => any) => string)\r\n            }\r\n        /**\r\n         * [JSON replacer function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter) used for both actions and states stringify.\r\n         * In addition, you can specify a data type by adding a [`__serializedType__`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/helpers/index.js#L4)\r\n         * key. So you can deserialize it back while importing or persisting data.\r\n         * Moreover, it will also [show a nice preview showing the provided custom type](https://cloud.githubusercontent.com/assets/7957859/21814330/a17d556a-d761-11e6-85ef-159dd12f36c5.png):\r\n         */\r\n        replacer?: (key: string, value: unknown) => any\r\n        /**\r\n         * [JSON `reviver` function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter)\r\n         * used for parsing the imported actions and states. See [`remotedev-serialize`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/immutable/serialize.js#L8-L41)\r\n         * as an example on how to serialize special data types and get them back.\r\n         */\r\n        reviver?: (key: string, value: unknown) => any\r\n        /**\r\n         * Automatically serialize/deserialize immutablejs via [remotedev-serialize](https://github.com/zalmoxisus/remotedev-serialize).\r\n         * Just pass the Immutable library. It will support all ImmutableJS structures. You can even export them into a file and get them back.\r\n         * The only exception is `Record` class, for which you should pass this in addition the references to your classes in `refs`.\r\n         */\r\n        immutable?: any\r\n        /**\r\n         * ImmutableJS `Record` classes used to make possible restore its instances back when importing, persisting...\r\n         */\r\n        refs?: any\r\n      }\r\n  /**\r\n   * function which takes `action` object and id number as arguments, and should return `action` object back.\r\n   */\r\n  actionSanitizer?: <A extends Action>(action: A, id: number) => A\r\n  /**\r\n   * function which takes `state` object and index as arguments, and should return `state` object back.\r\n   */\r\n  stateSanitizer?: <S>(state: S, index: number) => S\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsWhitelist` specified, `actionsBlacklist` is ignored.\r\n   * @deprecated Use actionsDenylist instead.\r\n   */\r\n  actionsBlacklist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsWhitelist` specified, `actionsBlacklist` is ignored.\r\n   * @deprecated Use actionsAllowlist instead.\r\n   */\r\n  actionsWhitelist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\r\n  actionsDenylist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\r\n  actionsAllowlist?: string | string[]\r\n  /**\r\n   * called for every action before sending, takes `state` and `action` object, and returns `true` in case it allows sending the current data to the monitor.\r\n   * Use it as a more advanced version of `actionsDenylist`/`actionsAllowlist` parameters.\r\n   */\r\n  predicate?: <S, A extends Action>(state: S, action: A) => boolean\r\n  /**\r\n   * if specified as `false`, it will not record the changes till clicking on `Start recording` button.\r\n   * Available only for Redux enhancer, for others use `autoPause`.\r\n   *\r\n   * @default true\r\n   */\r\n  shouldRecordChanges?: boolean\r\n  /**\r\n   * if specified, whenever clicking on `Pause recording` button and there are actions in the history log, will add this action type.\r\n   * If not specified, will commit when paused. Available only for Redux enhancer.\r\n   *\r\n   * @default \"@@PAUSED\"\"\r\n   */\r\n  pauseActionType?: string\r\n  /**\r\n   * auto pauses when the extension’s window is not opened, and so has zero impact on your app when not in use.\r\n   * Not available for Redux enhancer (as it already does it but storing the data to be sent).\r\n   *\r\n   * @default false\r\n   */\r\n  autoPause?: boolean\r\n  /**\r\n   * if specified as `true`, it will not allow any non-monitor actions to be dispatched till clicking on `Unlock changes` button.\r\n   * Available only for Redux enhancer.\r\n   *\r\n   * @default false\r\n   */\r\n  shouldStartLocked?: boolean\r\n  /**\r\n   * if set to `false`, will not recompute the states on hot reloading (or on replacing the reducers). Available only for Redux enhancer.\r\n   *\r\n   * @default true\r\n   */\r\n  shouldHotReload?: boolean\r\n  /**\r\n   * if specified as `true`, whenever there's an exception in reducers, the monitors will show the error message, and next actions will not be dispatched.\r\n   *\r\n   * @default false\r\n   */\r\n  shouldCatchErrors?: boolean\r\n  /**\r\n   * If you want to restrict the extension, specify the features you allow.\r\n   * If not specified, all of the features are enabled. When set as an object, only those included as `true` will be allowed.\r\n   * Note that except `true`/`false`, `import` and `export` can be set as `custom` (which is by default for Redux enhancer), meaning that the importing/exporting occurs on the client side.\r\n   * Otherwise, you'll get/set the data right from the monitor part.\r\n   */\r\n  features?: {\r\n    /**\r\n     * start/pause recording of dispatched actions\r\n     */\r\n    pause?: boolean\r\n    /**\r\n     * lock/unlock dispatching actions and side effects\r\n     */\r\n    lock?: boolean\r\n    /**\r\n     * persist states on page reloading\r\n     */\r\n    persist?: boolean\r\n    /**\r\n     * export history of actions in a file\r\n     */\r\n    export?: boolean | 'custom'\r\n    /**\r\n     * import history of actions from a file\r\n     */\r\n    import?: boolean | 'custom'\r\n    /**\r\n     * jump back and forth (time travelling)\r\n     */\r\n    jump?: boolean\r\n    /**\r\n     * skip (cancel) actions\r\n     */\r\n    skip?: boolean\r\n    /**\r\n     * drag and drop actions in the history list\r\n     */\r\n    reorder?: boolean\r\n    /**\r\n     * dispatch custom actions or action creators\r\n     */\r\n    dispatch?: boolean\r\n    /**\r\n     * generate tests for the selected actions\r\n     */\r\n    test?: boolean\r\n  }\r\n  /**\r\n   * Set to true or a stacktrace-returning function to record call stack traces for dispatched actions.\r\n   * Defaults to false.\r\n   */\r\n  trace?: boolean | (<A extends Action>(action: A) => string)\r\n  /**\r\n   * The maximum number of stack trace entries to record per action. Defaults to 10.\r\n   */\r\n  traceLimit?: number\r\n}\r\n\r\ntype Compose = typeof compose\r\n\r\ninterface ComposeWithDevTools {\r\n  (options: DevToolsEnhancerOptions): Compose\r\n  <StoreExt>(...funcs: StoreEnhancer<StoreExt>[]): StoreEnhancer<StoreExt>\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport const composeWithDevTools: ComposeWithDevTools =\r\n  typeof window !== 'undefined' &&\r\n  (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\r\n    ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\r\n    : function () {\r\n        if (arguments.length === 0) return undefined\r\n        if (typeof arguments[0] === 'object') return compose\r\n        return compose.apply(null, arguments as any as Function[])\r\n      }\r\n\r\n/**\r\n * @public\r\n */\r\nexport const devToolsEnhancer: {\r\n  (options: DevToolsEnhancerOptions): StoreEnhancer<any>\r\n} =\r\n  typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__\r\n    ? (window as any).__REDUX_DEVTOOLS_EXTENSION__\r\n    : function () {\r\n        return function (noop) {\r\n          return noop\r\n        }\r\n      }\r\n", "/**\r\n * Returns true if the passed value is \"plain\" object, i.e. an object whose\r\n * prototype is the root `Object.prototype`. This includes objects created\r\n * using object literals, but not for instance for class instances.\r\n *\r\n * @param {any} value The value to inspect.\r\n * @returns {boolean} True if the argument appears to be a plain object.\r\n *\r\n * @public\r\n */\r\nexport default function isPlainObject(value: unknown): value is object {\r\n  if (typeof value !== 'object' || value === null) return false\r\n\r\n  let proto = Object.getPrototypeOf(value)\r\n  if (proto === null) return true\r\n\r\n  let baseProto = proto\r\n  while (Object.getPrototypeOf(baseProto) !== null) {\r\n    baseProto = Object.getPrototypeOf(baseProto)\r\n  }\r\n\r\n  return proto === baseProto\r\n}\r\n", "import type { Middleware, AnyAction } from 'redux'\r\nimport type { ThunkMiddleware } from 'redux-thunk'\r\nimport thunkMiddleware from 'redux-thunk'\r\nimport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware'\r\nimport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware'\r\nimport type { ImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware'\r\n/* PROD_START_REMOVE_UMD */\r\nimport { createImmutableStateInvariantMiddleware } from './immutableStateInvariantMiddleware'\r\n/* PROD_STOP_REMOVE_UMD */\r\n\r\nimport type { SerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware'\r\nimport { createSerializableStateInvariantMiddleware } from './serializableStateInvariantMiddleware'\r\nimport type { ExcludeFromTuple } from './tsHelpers'\r\nimport { MiddlewareArray } from './utils'\r\n\r\nfunction isBoolean(x: any): x is boolean {\r\n  return typeof x === 'boolean'\r\n}\r\n\r\ninterface ThunkOptions<E = any> {\r\n  extraArgument: E\r\n}\r\n\r\ninterface GetDefaultMiddlewareOptions {\r\n  thunk?: boolean | ThunkOptions\r\n  immutableCheck?: boolean | ImmutableStateInvariantMiddlewareOptions\r\n  serializableCheck?: boolean | SerializableStateInvariantMiddlewareOptions\r\n  actionCreatorCheck?: boolean | ActionCreatorInvariantMiddlewareOptions\r\n}\r\n\r\nexport type ThunkMiddlewareFor<\r\n  S,\r\n  O extends GetDefaultMiddlewareOptions = {}\r\n> = O extends {\r\n  thunk: false\r\n}\r\n  ? never\r\n  : O extends { thunk: { extraArgument: infer E } }\r\n  ? ThunkMiddleware<S, AnyAction, E>\r\n  : ThunkMiddleware<S, AnyAction>\r\n\r\nexport type CurriedGetDefaultMiddleware<S = any> = <\r\n  O extends Partial<GetDefaultMiddlewareOptions> = {\r\n    thunk: true\r\n    immutableCheck: true\r\n    serializableCheck: true\r\n    actionCreatorCheck: true\r\n  }\r\n>(\r\n  options?: O\r\n) => MiddlewareArray<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>>\r\n\r\nexport function curryGetDefaultMiddleware<\r\n  S = any\r\n>(): CurriedGetDefaultMiddleware<S> {\r\n  return function curriedGetDefaultMiddleware(options) {\r\n    return getDefaultMiddleware(options)\r\n  }\r\n}\r\n\r\n/**\r\n * Returns any array containing the default middleware installed by\r\n * `configureStore()`. Useful if you want to configure your store with a custom\r\n * `middleware` array but still keep the default set.\r\n *\r\n * @return The default middleware used by `configureStore()`.\r\n *\r\n * @public\r\n *\r\n * @deprecated Prefer to use the callback notation for the `middleware` option in `configureStore`\r\n * to access a pre-typed `getDefaultMiddleware` instead.\r\n */\r\nexport function getDefaultMiddleware<\r\n  S = any,\r\n  O extends Partial<GetDefaultMiddlewareOptions> = {\r\n    thunk: true\r\n    immutableCheck: true\r\n    serializableCheck: true\r\n    actionCreatorCheck: true\r\n  }\r\n>(\r\n  options: O = {} as O\r\n): MiddlewareArray<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>> {\r\n  const {\r\n    thunk = true,\r\n    immutableCheck = true,\r\n    serializableCheck = true,\r\n    actionCreatorCheck = true,\r\n  } = options\r\n\r\n  let middlewareArray = new MiddlewareArray<Middleware[]>()\r\n\r\n  if (thunk) {\r\n    if (isBoolean(thunk)) {\r\n      middlewareArray.push(thunkMiddleware)\r\n    } else {\r\n      middlewareArray.push(\r\n        thunkMiddleware.withExtraArgument(thunk.extraArgument)\r\n      )\r\n    }\r\n  }\r\n\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    if (immutableCheck) {\r\n      /* PROD_START_REMOVE_UMD */\r\n      let immutableOptions: ImmutableStateInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(immutableCheck)) {\r\n        immutableOptions = immutableCheck\r\n      }\r\n\r\n      middlewareArray.unshift(\r\n        createImmutableStateInvariantMiddleware(immutableOptions)\r\n      )\r\n      /* PROD_STOP_REMOVE_UMD */\r\n    }\r\n\r\n    if (serializableCheck) {\r\n      let serializableOptions: SerializableStateInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(serializableCheck)) {\r\n        serializableOptions = serializableCheck\r\n      }\r\n\r\n      middlewareArray.push(\r\n        createSerializableStateInvariantMiddleware(serializableOptions)\r\n      )\r\n    }\r\n    if (actionCreatorCheck) {\r\n      let actionCreatorOptions: ActionCreatorInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(actionCreatorCheck)) {\r\n        actionCreatorOptions = actionCreatorCheck\r\n      }\r\n\r\n      middlewareArray.unshift(\r\n        createActionCreatorInvariantMiddleware(actionCreatorOptions)\r\n      )\r\n    }\r\n  }\r\n\r\n  return middlewareArray as any\r\n}\r\n", "import type { Middleware, StoreEnhancer } from 'redux'\r\nimport type { EnhancerArray, MiddlewareArray } from './utils'\r\n\r\n/**\r\n * return True if T is `any`, otherwise return False\r\n * taken from https://github.com/joonhocho/tsdef\r\n *\r\n * @internal\r\n */\r\nexport type IsAny<T, True, False = never> =\r\n  // test if we are going the left AND right path in the condition\r\n  true | false extends (T extends never ? true : false) ? True : False\r\n\r\n/**\r\n * return True if T is `unknown`, otherwise return False\r\n * taken from https://github.com/joonhocho/tsdef\r\n *\r\n * @internal\r\n */\r\nexport type IsUnknown<T, True, False = never> = unknown extends T\r\n  ? IsAny<T, False, True>\r\n  : False\r\n\r\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P]\r\n  ? True\r\n  : False\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IsEmptyObj<T, True, False = never> = T extends any\r\n  ? keyof T extends never\r\n    ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>>\r\n    : False\r\n  : never\r\n\r\n/**\r\n * returns True if TS version is above 3.5, False if below.\r\n * uses feature detection to detect TS version >= 3.5\r\n * * versions below 3.5 will return `{}` for unresolvable interference\r\n * * versions above will return `unknown`\r\n *\r\n * @internal\r\n */\r\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<\r\n  ReturnType<<T>() => T>,\r\n  0,\r\n  1\r\n>]\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<\r\n  IsUnknown<T, True, False>,\r\n  IsEmptyObj<T, True, IsUnknown<T, True, False>>\r\n>\r\n\r\n/**\r\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\r\n */\r\nexport type UnionToIntersection<U> = (\r\n  U extends any ? (k: U) => void : never\r\n) extends (k: infer I) => void\r\n  ? I\r\n  : never\r\n\r\n// Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\r\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [\r\n  infer Head,\r\n  ...infer Tail\r\n]\r\n  ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]>\r\n  : Acc\r\n\r\ntype ExtractDispatchFromMiddlewareTuple<\r\n  MiddlewareTuple extends any[],\r\n  Acc extends {}\r\n> = MiddlewareTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractDispatchFromMiddlewareTuple<\r\n      Tail,\r\n      Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractDispatchExtensions<M> = M extends MiddlewareArray<\r\n  infer MiddlewareTuple\r\n>\r\n  ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}>\r\n  : M extends ReadonlyArray<Middleware>\r\n  ? ExtractDispatchFromMiddlewareTuple<[...M], {}>\r\n  : never\r\n\r\ntype ExtractStoreExtensionsFromEnhancerTuple<\r\n  EnhancerTuple extends any[],\r\n  Acc extends {}\r\n> = EnhancerTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractStoreExtensionsFromEnhancerTuple<\r\n      Tail,\r\n      Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractStoreExtensions<E> = E extends EnhancerArray<\r\n  infer EnhancerTuple\r\n>\r\n  ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}>\r\n  : E extends ReadonlyArray<StoreEnhancer>\r\n  ? UnionToIntersection<\r\n      E[number] extends StoreEnhancer<infer Ext>\r\n        ? Ext extends {}\r\n          ? IsAny<Ext, {}, Ext>\r\n          : {}\r\n        : {}\r\n    >\r\n  : never\r\n\r\ntype ExtractStateExtensionsFromEnhancerTuple<\r\n  EnhancerTuple extends any[],\r\n  Acc extends {}\r\n> = EnhancerTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractStateExtensionsFromEnhancerTuple<\r\n      Tail,\r\n      Acc &\r\n        (Head extends StoreEnhancer<any, infer StateExt>\r\n          ? IsAny<StateExt, {}, StateExt>\r\n          : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractStateExtensions<E> = E extends EnhancerArray<\r\n  infer EnhancerTuple\r\n>\r\n  ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}>\r\n  : E extends ReadonlyArray<StoreEnhancer>\r\n  ? UnionToIntersection<\r\n      E[number] extends StoreEnhancer<any, infer StateExt>\r\n        ? StateExt extends {}\r\n          ? IsAny<StateExt, {}, StateExt>\r\n          : {}\r\n        : {}\r\n    >\r\n  : never\r\n\r\n/**\r\n * Helper type. Passes T out again, but boxes it in a way that it cannot\r\n * \"widen\" the type by accident if it is a generic that should be inferred\r\n * from elsewhere.\r\n *\r\n * @internal\r\n */\r\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\r\n\r\nexport type Omit<T, K extends keyof any> = Pick<T, Exclude<keyof T, K>>\r\n\r\nexport interface TypeGuard<T> {\r\n  (value: any): value is T\r\n}\r\n\r\nexport interface HasMatchFunction<T> {\r\n  match: TypeGuard<T>\r\n}\r\n\r\nexport const hasMatchFunction = <T>(\r\n  v: Matcher<T>\r\n): v is HasMatchFunction<T> => {\r\n  return v && typeof (v as HasMatchFunction<T>).match === 'function'\r\n}\r\n\r\n/** @public */\r\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>\r\n\r\n/** @public */\r\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<\r\n  infer T\r\n>\r\n  ? T\r\n  : never\r\n\r\nexport type Id<T> = { [K in keyof T]: T[K] } & {}\r\n", "import type { Action } from 'redux'\r\nimport type {\r\n  IsUnknownOrNonInferrable,\r\n  IfMaybeUndefined,\r\n  IfVoid,\r\n  IsAny,\r\n} from './tsHelpers'\r\nimport { hasMatchFunction } from './tsHelpers'\r\nimport isPlainObject from './isPlainObject'\r\n\r\n/**\r\n * An action with a string type and an associated payload. This is the\r\n * type of action returned by `createAction()` action creators.\r\n *\r\n * @template P The type of the action's payload.\r\n * @template T the type used for the action type.\r\n * @template M The type of the action's meta (optional)\r\n * @template E The type of the action's error (optional)\r\n *\r\n * @public\r\n */\r\nexport type PayloadAction<\r\n  P = void,\r\n  T extends string = string,\r\n  M = never,\r\n  E = never\r\n> = {\r\n  payload: P\r\n  type: T\r\n} & ([M] extends [never]\r\n  ? {}\r\n  : {\r\n      meta: M\r\n    }) &\r\n  ([E] extends [never]\r\n    ? {}\r\n    : {\r\n        error: E\r\n      })\r\n\r\n/**\r\n * A \"prepare\" method to be used as the second parameter of `createAction`.\r\n * Takes any number of arguments and returns a Flux Standard Action without\r\n * type (will be added later) that *must* contain a payload (might be undefined).\r\n *\r\n * @public\r\n */\r\nexport type PrepareAction<P> =\r\n  | ((...args: any[]) => { payload: P })\r\n  | ((...args: any[]) => { payload: P; meta: any })\r\n  | ((...args: any[]) => { payload: P; error: any })\r\n  | ((...args: any[]) => { payload: P; meta: any; error: any })\r\n\r\n/**\r\n * Internal version of `ActionCreatorWithPreparedPayload`. Not to be used externally.\r\n *\r\n * @internal\r\n */\r\nexport type _ActionCreatorWithPreparedPayload<\r\n  PA extends PrepareAction<any> | void,\r\n  T extends string = string\r\n> = PA extends PrepareAction<infer P>\r\n  ? ActionCreatorWithPreparedPayload<\r\n      Parameters<PA>,\r\n      P,\r\n      T,\r\n      ReturnType<PA> extends {\r\n        error: infer E\r\n      }\r\n        ? E\r\n        : never,\r\n      ReturnType<PA> extends {\r\n        meta: infer M\r\n      }\r\n        ? M\r\n        : never\r\n    >\r\n  : void\r\n\r\n/**\r\n * Basic type for all action creators.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n */\r\nexport interface BaseActionCreator<P, T extends string, M = never, E = never> {\r\n  type: T\r\n  match: (action: Action<unknown>) => action is PayloadAction<P, T, M, E>\r\n}\r\n\r\n/**\r\n * An action creator that takes multiple arguments that are passed\r\n * to a `PrepareAction` method to create the final Action.\r\n * @typeParam Args arguments for the action creator function\r\n * @typeParam P `payload` type\r\n * @typeParam T `type` name\r\n * @typeParam E optional `error` type\r\n * @typeParam M optional `meta` type\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithPreparedPayload<\r\n  Args extends unknown[],\r\n  P,\r\n  T extends string = string,\r\n  E = never,\r\n  M = never\r\n> extends BaseActionCreator<P, T, M, E> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with `Args` will return\r\n   * an Action with a payload of type `P` and (depending on the `PrepareAction`\r\n   * method used) a `meta`- and `error` property of types `M` and `E` respectively.\r\n   */\r\n  (...args: Args): PayloadAction<P, T, M, E>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that takes an optional payload of type `P`.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithOptionalPayload<P, T extends string = string>\r\n  extends BaseActionCreator<P, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `P`.\r\n   * Calling it without an argument will return a PayloadAction with a payload of `undefined`.\r\n   */\r\n  (payload?: P): PayloadAction<P, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that takes no payload.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithoutPayload<T extends string = string>\r\n  extends BaseActionCreator<undefined, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `undefined`\r\n   */\r\n  (noArgument: void): PayloadAction<undefined, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that requires a payload of type P.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithPayload<P, T extends string = string>\r\n  extends BaseActionCreator<P, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `P`\r\n   */\r\n  (payload: P): PayloadAction<P, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` whose `payload` type could not be inferred. Accepts everything as `payload`.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithNonInferrablePayload<\r\n  T extends string = string\r\n> extends BaseActionCreator<unknown, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload\r\n   * of exactly the type of the argument.\r\n   */\r\n  <PT extends unknown>(payload: PT): PayloadAction<PT, T>\r\n}\r\n\r\n/**\r\n * An action creator that produces actions with a `payload` attribute.\r\n *\r\n * @typeParam P the `payload` type\r\n * @typeParam T the `type` of the resulting action\r\n * @typeParam PA if the resulting action is preprocessed by a `prepare` method, the signature of said method.\r\n *\r\n * @public\r\n */\r\nexport type PayloadActionCreator<\r\n  P = void,\r\n  T extends string = string,\r\n  PA extends PrepareAction<P> | void = void\r\n> = IfPrepareActionMethodProvided<\r\n  PA,\r\n  _ActionCreatorWithPreparedPayload<PA, T>,\r\n  // else\r\n  IsAny<\r\n    P,\r\n    ActionCreatorWithPayload<any, T>,\r\n    IsUnknownOrNonInferrable<\r\n      P,\r\n      ActionCreatorWithNonInferrablePayload<T>,\r\n      // else\r\n      IfVoid<\r\n        P,\r\n        ActionCreatorWithoutPayload<T>,\r\n        // else\r\n        IfMaybeUndefined<\r\n          P,\r\n          ActionCreatorWithOptionalPayload<P, T>,\r\n          // else\r\n          ActionCreatorWithPayload<P, T>\r\n        >\r\n      >\r\n    >\r\n  >\r\n>\r\n\r\n/**\r\n * A utility function to create an action creator for the given action type\r\n * string. The action creator accepts a single argument, which will be included\r\n * in the action object as a field called payload. The action creator function\r\n * will also have its toString() overridden so that it returns the action type,\r\n * allowing it to be used in reducer logic that is looking for that action type.\r\n *\r\n * @param type The action type to use for created actions.\r\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\r\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\r\n *\r\n * @public\r\n */\r\nexport function createAction<P = void, T extends string = string>(\r\n  type: T\r\n): PayloadActionCreator<P, T>\r\n\r\n/**\r\n * A utility function to create an action creator for the given action type\r\n * string. The action creator accepts a single argument, which will be included\r\n * in the action object as a field called payload. The action creator function\r\n * will also have its toString() overridden so that it returns the action type,\r\n * allowing it to be used in reducer logic that is looking for that action type.\r\n *\r\n * @param type The action type to use for created actions.\r\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\r\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\r\n *\r\n * @public\r\n */\r\nexport function createAction<\r\n  PA extends PrepareAction<any>,\r\n  T extends string = string\r\n>(\r\n  type: T,\r\n  prepareAction: PA\r\n): PayloadActionCreator<ReturnType<PA>['payload'], T, PA>\r\n\r\nexport function createAction(type: string, prepareAction?: Function): any {\r\n  function actionCreator(...args: any[]) {\r\n    if (prepareAction) {\r\n      let prepared = prepareAction(...args)\r\n      if (!prepared) {\r\n        throw new Error('prepareAction did not return an object')\r\n      }\r\n\r\n      return {\r\n        type,\r\n        payload: prepared.payload,\r\n        ...('meta' in prepared && { meta: prepared.meta }),\r\n        ...('error' in prepared && { error: prepared.error }),\r\n      }\r\n    }\r\n    return { type, payload: args[0] }\r\n  }\r\n\r\n  actionCreator.toString = () => `${type}`\r\n\r\n  actionCreator.type = type\r\n\r\n  actionCreator.match = (action: Action<unknown>): action is PayloadAction =>\r\n    action.type === type\r\n\r\n  return actionCreator\r\n}\r\n\r\n/**\r\n * Returns true if value is a plain object with a `type` property.\r\n */\r\nexport function isAction(action: unknown): action is Action<unknown> {\r\n  return isPlainObject(action) && 'type' in action\r\n}\r\n\r\n/**\r\n * Returns true if value is an RTK-like action creator, with a static type property and match method.\r\n */\r\nexport function isActionCreator(\r\n  action: unknown\r\n): action is BaseActionCreator<unknown, string> & Function {\r\n  return (\r\n    typeof action === 'function' &&\r\n    'type' in action &&\r\n    // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\r\n    hasMatchFunction(action as any)\r\n  )\r\n}\r\n\r\n/**\r\n * Returns true if value is an action with a string type and valid Flux Standard Action keys.\r\n */\r\nexport function isFSA(action: unknown): action is {\r\n  type: string\r\n  payload?: unknown\r\n  error?: unknown\r\n  meta?: unknown\r\n} {\r\n  return (\r\n    isAction(action) &&\r\n    typeof action.type === 'string' &&\r\n    Object.keys(action).every(isValidKey)\r\n  )\r\n}\r\n\r\nfunction isValidKey(key: string) {\r\n  return ['type', 'payload', 'error', 'meta'].indexOf(key) > -1\r\n}\r\n\r\n/**\r\n * Returns the action type of the actions created by the passed\r\n * `createAction()`-generated action creator (arbitrary action creators\r\n * are not supported).\r\n *\r\n * @param action The action creator whose action type to get.\r\n * @returns The action type used by the action creator.\r\n *\r\n * @public\r\n */\r\nexport function getType<T extends string>(\r\n  actionCreator: PayloadActionCreator<any, T>\r\n): T {\r\n  return `${actionCreator}` as T\r\n}\r\n\r\n// helper types for more readable typings\r\n\r\ntype IfPrepareActionMethodProvided<\r\n  PA extends PrepareAction<any> | void,\r\n  True,\r\n  False\r\n> = PA extends (...args: any[]) => any ? True : False\r\n", "import type { Middleware } from 'redux'\r\nimport { isActionCreator as isRTKAction } from './createAction'\r\n\r\nexport interface ActionCreatorInvariantMiddlewareOptions {\r\n  /**\r\n   * The function to identify whether a value is an action creator.\r\n   * The default checks for a function with a static type property and match method.\r\n   */\r\n  isActionCreator?: (action: unknown) => action is Function & { type?: unknown }\r\n}\r\n\r\nexport function getMessage(type?: unknown) {\r\n  const splitType = type ? `${type}`.split('/') : []\r\n  const actionName = splitType[splitType.length - 1] || 'actionCreator'\r\n  return `Detected an action creator with type \"${\r\n    type || 'unknown'\r\n  }\" being dispatched. \r\nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`\r\n}\r\n\r\nexport function createActionCreatorInvariantMiddleware(\r\n  options: ActionCreatorInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n  const { isActionCreator = isRTKAction } = options\r\n  return () => (next) => (action) => {\r\n    if (isActionCreator(action)) {\r\n      console.warn(getMessage(action.type))\r\n    }\r\n    return next(action)\r\n  }\r\n}\r\n", "import createNextState, { isDraftable } from 'immer'\r\nimport type { Middleware, StoreEnhancer } from 'redux'\r\n\r\nexport function getTimeMeasureUtils(maxDelay: number, fnName: string) {\r\n  let elapsed = 0\r\n  return {\r\n    measureTime<T>(fn: () => T): T {\r\n      const started = Date.now()\r\n      try {\r\n        return fn()\r\n      } finally {\r\n        const finished = Date.now()\r\n        elapsed += finished - started\r\n      }\r\n    },\r\n    warnIfExceeded() {\r\n      if (elapsed > maxDelay) {\r\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \r\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\r\nIt is disabled in production builds, so you don't need to worry about that.`)\r\n      }\r\n    },\r\n  }\r\n}\r\n\r\nexport function delay(ms: number) {\r\n  return new Promise((resolve) => setTimeout(resolve, ms))\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport class MiddlewareArray<\r\n  Middlewares extends Middleware<any, any>[]\r\n> extends Array<Middlewares[number]> {\r\n  constructor(...items: Middlewares)\r\n  constructor(...args: any[]) {\r\n    super(...args)\r\n    Object.setPrototypeOf(this, MiddlewareArray.prototype)\r\n  }\r\n\r\n  static get [Symbol.species]() {\r\n    return MiddlewareArray as any\r\n  }\r\n\r\n  concat<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...Middlewares, ...AdditionalMiddlewares]>\r\n\r\n  concat<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    ...items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...Middlewares, ...AdditionalMiddlewares]>\r\n  concat(...arr: any[]) {\r\n    return super.concat.apply(this, arr)\r\n  }\r\n\r\n  prepend<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...AdditionalMiddlewares, ...Middlewares]>\r\n\r\n  prepend<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    ...items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...AdditionalMiddlewares, ...Middlewares]>\r\n\r\n  prepend(...arr: any[]) {\r\n    if (arr.length === 1 && Array.isArray(arr[0])) {\r\n      return new MiddlewareArray(...arr[0].concat(this))\r\n    }\r\n    return new MiddlewareArray(...arr.concat(this))\r\n  }\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport class EnhancerArray<\r\n  Enhancers extends StoreEnhancer<any, any>[]\r\n> extends Array<Enhancers[number]> {\r\n  constructor(...items: Enhancers)\r\n  constructor(...args: any[]) {\r\n    super(...args)\r\n    Object.setPrototypeOf(this, EnhancerArray.prototype)\r\n  }\r\n\r\n  static get [Symbol.species]() {\r\n    return EnhancerArray as any\r\n  }\r\n\r\n  concat<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    items: AdditionalEnhancers\r\n  ): EnhancerArray<[...Enhancers, ...AdditionalEnhancers]>\r\n\r\n  concat<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    ...items: AdditionalEnhancers\r\n  ): EnhancerArray<[...Enhancers, ...AdditionalEnhancers]>\r\n  concat(...arr: any[]) {\r\n    return super.concat.apply(this, arr)\r\n  }\r\n\r\n  prepend<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    items: AdditionalEnhancers\r\n  ): EnhancerArray<[...AdditionalEnhancers, ...Enhancers]>\r\n\r\n  prepend<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    ...items: AdditionalEnhancers\r\n  ): EnhancerArray<[...AdditionalEnhancers, ...Enhancers]>\r\n\r\n  prepend(...arr: any[]) {\r\n    if (arr.length === 1 && Array.isArray(arr[0])) {\r\n      return new EnhancerArray(...arr[0].concat(this))\r\n    }\r\n    return new EnhancerArray(...arr.concat(this))\r\n  }\r\n}\r\n\r\nexport function freezeDraftable<T>(val: T) {\r\n  return isDraftable(val) ? createNextState(val, () => {}) : val\r\n}\r\n", "import type { Middleware } from 'redux'\r\nimport { getTimeMeasureUtils } from './utils'\r\n\r\ntype EntryProcessor = (key: string, value: any) => any\r\n\r\nconst isProduction: boolean = process.env.NODE_ENV === 'production'\r\nconst prefix: string = 'Invariant failed'\r\n\r\n// Throw an error if the condition fails\r\n// Strip out error messages for production\r\n// > Not providing an inline default argument for message as the result is smaller\r\nfunction invariant(condition: any, message?: string) {\r\n  if (condition) {\r\n    return\r\n  }\r\n  // Condition not passed\r\n\r\n  // In production we strip the message but still throw\r\n  if (isProduction) {\r\n    throw new Error(prefix)\r\n  }\r\n\r\n  // When not in production we allow the message to pass through\r\n  // *This block will be removed in production builds*\r\n  throw new Error(`${prefix}: ${message || ''}`)\r\n}\r\n\r\nfunction stringify(\r\n  obj: any,\r\n  serializer?: EntryProcessor,\r\n  indent?: string | number,\r\n  decycler?: EntryProcessor\r\n): string {\r\n  return JSON.stringify(obj, getSerialize(serializer, decycler), indent)\r\n}\r\n\r\nfunction getSerialize(\r\n  serializer?: EntryProcessor,\r\n  decycler?: EntryProcessor\r\n): EntryProcessor {\r\n  let stack: any[] = [],\r\n    keys: any[] = []\r\n\r\n  if (!decycler)\r\n    decycler = function (_: string, value: any) {\r\n      if (stack[0] === value) return '[Circular ~]'\r\n      return (\r\n        '[Circular ~.' + keys.slice(0, stack.indexOf(value)).join('.') + ']'\r\n      )\r\n    }\r\n\r\n  return function (this: any, key: string, value: any) {\r\n    if (stack.length > 0) {\r\n      var thisPos = stack.indexOf(this)\r\n      ~thisPos ? stack.splice(thisPos + 1) : stack.push(this)\r\n      ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key)\r\n      if (~stack.indexOf(value)) value = decycler!.call(this, key, value)\r\n    } else stack.push(value)\r\n\r\n    return serializer == null ? value : serializer.call(this, key, value)\r\n  }\r\n}\r\n\r\n/**\r\n * The default `isImmutable` function.\r\n *\r\n * @public\r\n */\r\nexport function isImmutableDefault(value: unknown): boolean {\r\n  return typeof value !== 'object' || value == null || Object.isFrozen(value)\r\n}\r\n\r\nexport function trackForMutations(\r\n  isImmutable: IsImmutableFunc,\r\n  ignorePaths: IgnorePaths | undefined,\r\n  obj: any\r\n) {\r\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj)\r\n  return {\r\n    detectMutations() {\r\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj)\r\n    },\r\n  }\r\n}\r\n\r\ninterface TrackedProperty {\r\n  value: any\r\n  children: Record<string, any>\r\n}\r\n\r\nfunction trackProperties(\r\n  isImmutable: IsImmutableFunc,\r\n  ignorePaths: IgnorePaths = [],\r\n  obj: Record<string, any>,\r\n  path: string = '',\r\n  checkedObjects: Set<Record<string, any>> = new Set()\r\n) {\r\n  const tracked: Partial<TrackedProperty> = { value: obj }\r\n\r\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\r\n    checkedObjects.add(obj);\r\n    tracked.children = {}\r\n\r\n    for (const key in obj) {\r\n      const childPath = path ? path + '.' + key : key\r\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\r\n        continue\r\n      }\r\n\r\n      tracked.children[key] = trackProperties(\r\n        isImmutable,\r\n        ignorePaths,\r\n        obj[key],\r\n        childPath\r\n      )\r\n    }\r\n  }\r\n  return tracked as TrackedProperty\r\n}\r\n\r\ntype IgnorePaths = readonly (string | RegExp)[]\r\n\r\nfunction detectMutations(\r\n  isImmutable: IsImmutableFunc,\r\n  ignoredPaths: IgnorePaths = [],\r\n  trackedProperty: TrackedProperty,\r\n  obj: any,\r\n  sameParentRef: boolean = false,\r\n  path: string = ''\r\n): { wasMutated: boolean; path?: string } {\r\n  const prevObj = trackedProperty ? trackedProperty.value : undefined\r\n\r\n  const sameRef = prevObj === obj\r\n\r\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\r\n    return { wasMutated: true, path }\r\n  }\r\n\r\n  if (isImmutable(prevObj) || isImmutable(obj)) {\r\n    return { wasMutated: false }\r\n  }\r\n\r\n  // Gather all keys from prev (tracked) and after objs\r\n  const keysToDetect: Record<string, boolean> = {}\r\n  for (let key in trackedProperty.children) {\r\n    keysToDetect[key] = true\r\n  }\r\n  for (let key in obj) {\r\n    keysToDetect[key] = true\r\n  }\r\n\r\n  const hasIgnoredPaths = ignoredPaths.length > 0\r\n\r\n  for (let key in keysToDetect) {\r\n    const nestedPath = path ? path + '.' + key : key\r\n\r\n    if (hasIgnoredPaths) {\r\n      const hasMatches = ignoredPaths.some((ignored) => {\r\n        if (ignored instanceof RegExp) {\r\n          return ignored.test(nestedPath)\r\n        }\r\n        return nestedPath === ignored\r\n      })\r\n      if (hasMatches) {\r\n        continue\r\n      }\r\n    }\r\n\r\n    const result = detectMutations(\r\n      isImmutable,\r\n      ignoredPaths,\r\n      trackedProperty.children[key],\r\n      obj[key],\r\n      sameRef,\r\n      nestedPath\r\n    )\r\n\r\n    if (result.wasMutated) {\r\n      return result\r\n    }\r\n  }\r\n  return { wasMutated: false }\r\n}\r\n\r\ntype IsImmutableFunc = (value: any) => boolean\r\n\r\n/**\r\n * Options for `createImmutableStateInvariantMiddleware()`.\r\n *\r\n * @public\r\n */\r\nexport interface ImmutableStateInvariantMiddlewareOptions {\r\n  /**\r\n    Callback function to check if a value is considered to be immutable.\r\n    This function is applied recursively to every value contained in the state.\r\n    The default implementation will return true for primitive types \r\n    (like numbers, strings, booleans, null and undefined).\r\n   */\r\n  isImmutable?: IsImmutableFunc\r\n  /** \r\n    An array of dot-separated path strings that match named nodes from \r\n    the root state to ignore when checking for immutability.\r\n    Defaults to undefined\r\n   */\r\n  ignoredPaths?: IgnorePaths\r\n  /** Print a warning if checks take longer than N ms. Default: 32ms */\r\n  warnAfter?: number\r\n  // @deprecated. Use ignoredPaths\r\n  ignore?: string[]\r\n}\r\n\r\n/**\r\n * Creates a middleware that checks whether any state was mutated in between\r\n * dispatches or during a dispatch. If any mutations are detected, an error is\r\n * thrown.\r\n *\r\n * @param options Middleware options.\r\n *\r\n * @public\r\n */\r\nexport function createImmutableStateInvariantMiddleware(\r\n  options: ImmutableStateInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n\r\n  let {\r\n    isImmutable = isImmutableDefault,\r\n    ignoredPaths,\r\n    warnAfter = 32,\r\n    ignore,\r\n  } = options\r\n\r\n  // Alias ignore->ignoredPaths, but prefer ignoredPaths if present\r\n  ignoredPaths = ignoredPaths || ignore\r\n\r\n  const track = trackForMutations.bind(null, isImmutable, ignoredPaths)\r\n\r\n  return ({ getState }) => {\r\n    let state = getState()\r\n    let tracker = track(state)\r\n\r\n    let result\r\n    return (next) => (action) => {\r\n      const measureUtils = getTimeMeasureUtils(\r\n        warnAfter,\r\n        'ImmutableStateInvariantMiddleware'\r\n      )\r\n\r\n      measureUtils.measureTime(() => {\r\n        state = getState()\r\n\r\n        result = tracker.detectMutations()\r\n        // Track before potentially not meeting the invariant\r\n        tracker = track(state)\r\n\r\n        invariant(\r\n          !result.wasMutated,\r\n          `A state mutation was detected between dispatches, in the path '${\r\n            result.path || ''\r\n          }'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`\r\n        )\r\n      })\r\n\r\n      const dispatchedAction = next(action)\r\n\r\n      measureUtils.measureTime(() => {\r\n        state = getState()\r\n\r\n        result = tracker.detectMutations()\r\n        // Track before potentially not meeting the invariant\r\n        tracker = track(state)\r\n\r\n        result.wasMutated &&\r\n          invariant(\r\n            !result.wasMutated,\r\n            `A state mutation was detected inside a dispatch, in the path: ${\r\n              result.path || ''\r\n            }. Take a look at the reducer(s) handling the action ${stringify(\r\n              action\r\n            )}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`\r\n          )\r\n      })\r\n\r\n      measureUtils.warnIfExceeded()\r\n\r\n      return dispatchedAction\r\n    }\r\n  }\r\n}\r\n", "import isPlainObject from './isPlainObject'\r\nimport type { Middleware } from 'redux'\r\nimport { getTimeMeasureUtils } from './utils'\r\n\r\n/**\r\n * Returns true if the passed value is \"plain\", i.e. a value that is either\r\n * directly JSON-serializable (boolean, number, string, array, plain object)\r\n * or `undefined`.\r\n *\r\n * @param val The value to check.\r\n *\r\n * @public\r\n */\r\nexport function isPlain(val: any) {\r\n  const type = typeof val\r\n  return (\r\n    val == null ||\r\n    type === 'string' ||\r\n    type === 'boolean' ||\r\n    type === 'number' ||\r\n    Array.isArray(val) ||\r\n    isPlainObject(val)\r\n  )\r\n}\r\n\r\ninterface NonSerializableValue {\r\n  keyPath: string\r\n  value: unknown\r\n}\r\n\r\ntype IgnorePaths = readonly (string | RegExp)[]\r\n\r\n/**\r\n * @public\r\n */\r\nexport function findNonSerializableValue(\r\n  value: unknown,\r\n  path: string = '',\r\n  isSerializable: (value: unknown) => boolean = isPlain,\r\n  getEntries?: (value: unknown) => [string, any][],\r\n  ignoredPaths: IgnorePaths = [],\r\n  cache?: WeakSet<object>\r\n): NonSerializableValue | false {\r\n  let foundNestedSerializable: NonSerializableValue | false\r\n\r\n  if (!isSerializable(value)) {\r\n    return {\r\n      keyPath: path || '<root>',\r\n      value: value,\r\n    }\r\n  }\r\n\r\n  if (typeof value !== 'object' || value === null) {\r\n    return false\r\n  }\r\n\r\n  if (cache?.has(value)) return false\r\n\r\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value)\r\n\r\n  const hasIgnoredPaths = ignoredPaths.length > 0\r\n\r\n  for (const [key, nestedValue] of entries) {\r\n    const nestedPath = path ? path + '.' + key : key\r\n\r\n    if (hasIgnoredPaths) {\r\n      const hasMatches = ignoredPaths.some((ignored) => {\r\n        if (ignored instanceof RegExp) {\r\n          return ignored.test(nestedPath)\r\n        }\r\n        return nestedPath === ignored\r\n      })\r\n      if (hasMatches) {\r\n        continue\r\n      }\r\n    }\r\n\r\n    if (!isSerializable(nestedValue)) {\r\n      return {\r\n        keyPath: nestedPath,\r\n        value: nestedValue,\r\n      }\r\n    }\r\n\r\n    if (typeof nestedValue === 'object') {\r\n      foundNestedSerializable = findNonSerializableValue(\r\n        nestedValue,\r\n        nestedPath,\r\n        isSerializable,\r\n        getEntries,\r\n        ignoredPaths,\r\n        cache\r\n      )\r\n\r\n      if (foundNestedSerializable) {\r\n        return foundNestedSerializable\r\n      }\r\n    }\r\n  }\r\n\r\n  if (cache && isNestedFrozen(value)) cache.add(value)\r\n\r\n  return false\r\n}\r\n\r\nexport function isNestedFrozen(value: object) {\r\n  if (!Object.isFrozen(value)) return false\r\n\r\n  for (const nestedValue of Object.values(value)) {\r\n    if (typeof nestedValue !== 'object' || nestedValue === null) continue\r\n\r\n    if (!isNestedFrozen(nestedValue)) return false\r\n  }\r\n\r\n  return true\r\n}\r\n\r\n/**\r\n * Options for `createSerializableStateInvariantMiddleware()`.\r\n *\r\n * @public\r\n */\r\nexport interface SerializableStateInvariantMiddlewareOptions {\r\n  /**\r\n   * The function to check if a value is considered serializable. This\r\n   * function is applied recursively to every value contained in the\r\n   * state. Defaults to `isPlain()`.\r\n   */\r\n  isSerializable?: (value: any) => boolean\r\n  /**\r\n   * The function that will be used to retrieve entries from each\r\n   * value.  If unspecified, `Object.entries` will be used. Defaults\r\n   * to `undefined`.\r\n   */\r\n  getEntries?: (value: any) => [string, any][]\r\n\r\n  /**\r\n   * An array of action types to ignore when checking for serializability.\r\n   * Defaults to []\r\n   */\r\n  ignoredActions?: string[]\r\n\r\n  /**\r\n   * An array of dot-separated path strings or regular expressions to ignore\r\n   * when checking for serializability, Defaults to\r\n   * ['meta.arg', 'meta.baseQueryMeta']\r\n   */\r\n  ignoredActionPaths?: (string | RegExp)[]\r\n\r\n  /**\r\n   * An array of dot-separated path strings or regular expressions to ignore\r\n   * when checking for serializability, Defaults to []\r\n   */\r\n  ignoredPaths?: (string | RegExp)[]\r\n  /**\r\n   * Execution time warning threshold. If the middleware takes longer\r\n   * than `warnAfter` ms, a warning will be displayed in the console.\r\n   * Defaults to 32ms.\r\n   */\r\n  warnAfter?: number\r\n\r\n  /**\r\n   * Opt out of checking state. When set to `true`, other state-related params will be ignored.\r\n   */\r\n  ignoreState?: boolean\r\n\r\n  /**\r\n   * Opt out of checking actions. When set to `true`, other action-related params will be ignored.\r\n   */\r\n  ignoreActions?: boolean\r\n\r\n  /**\r\n   * Opt out of caching the results. The cache uses a WeakSet and speeds up repeated checking processes.\r\n   * The cache is automatically disabled if no browser support for WeakSet is present.\r\n   */\r\n  disableCache?: boolean\r\n}\r\n\r\n/**\r\n * Creates a middleware that, after every state change, checks if the new\r\n * state is serializable. If a non-serializable value is found within the\r\n * state, an error is printed to the console.\r\n *\r\n * @param options Middleware options.\r\n *\r\n * @public\r\n */\r\nexport function createSerializableStateInvariantMiddleware(\r\n  options: SerializableStateInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n  const {\r\n    isSerializable = isPlain,\r\n    getEntries,\r\n    ignoredActions = [],\r\n    ignoredActionPaths = ['meta.arg', 'meta.baseQueryMeta'],\r\n    ignoredPaths = [],\r\n    warnAfter = 32,\r\n    ignoreState = false,\r\n    ignoreActions = false,\r\n    disableCache = false,\r\n  } = options\r\n\r\n  const cache: WeakSet<object> | undefined =\r\n    !disableCache && WeakSet ? new WeakSet() : undefined\r\n\r\n  return (storeAPI) => (next) => (action) => {\r\n    const result = next(action)\r\n\r\n    const measureUtils = getTimeMeasureUtils(\r\n      warnAfter,\r\n      'SerializableStateInvariantMiddleware'\r\n    )\r\n\r\n    if (\r\n      !ignoreActions &&\r\n      !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)\r\n    ) {\r\n      measureUtils.measureTime(() => {\r\n        const foundActionNonSerializableValue = findNonSerializableValue(\r\n          action,\r\n          '',\r\n          isSerializable,\r\n          getEntries,\r\n          ignoredActionPaths,\r\n          cache\r\n        )\r\n\r\n        if (foundActionNonSerializableValue) {\r\n          const { keyPath, value } = foundActionNonSerializableValue\r\n\r\n          console.error(\r\n            `A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`,\r\n            value,\r\n            '\\nTake a look at the logic that dispatched this action: ',\r\n            action,\r\n            '\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)',\r\n            '\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)'\r\n          )\r\n        }\r\n      })\r\n    }\r\n\r\n    if (!ignoreState) {\r\n      measureUtils.measureTime(() => {\r\n        const state = storeAPI.getState()\r\n\r\n        const foundStateNonSerializableValue = findNonSerializableValue(\r\n          state,\r\n          '',\r\n          isSerializable,\r\n          getEntries,\r\n          ignoredPaths,\r\n          cache\r\n        )\r\n\r\n        if (foundStateNonSerializableValue) {\r\n          const { keyPath, value } = foundStateNonSerializableValue\r\n\r\n          console.error(\r\n            `A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`,\r\n            value,\r\n            `\r\nTake a look at the reducer(s) handling this action type: ${action.type}.\r\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`\r\n          )\r\n        }\r\n      })\r\n\r\n      measureUtils.warnIfExceeded()\r\n    }\r\n\r\n    return result\r\n  }\r\n}\r\n", "import type { Draft } from 'immer'\r\nimport createNextState, { isDraft, isDraftable } from 'immer'\r\nimport type { AnyAction, Action, Reducer } from 'redux'\r\nimport type { ActionReducerMapBuilder } from './mapBuilders'\r\nimport { executeReducerBuilderCallback } from './mapBuilders'\r\nimport type { NoInfer } from './tsHelpers'\r\nimport { freezeDraftable } from './utils'\r\n\r\n/**\r\n * Defines a mapping from action types to corresponding action object shapes.\r\n *\r\n * @deprecated This should not be used manually - it is only used for internal\r\n *             inference purposes and should not have any further value.\r\n *             It might be removed in the future.\r\n * @public\r\n */\r\nexport type Actions<T extends keyof any = string> = Record<T, Action>\r\n\r\n/**\r\n * @deprecated use `TypeGuard` instead\r\n */\r\nexport interface ActionMatcher<A extends AnyAction> {\r\n  (action: AnyAction): action is A\r\n}\r\n\r\nexport type ActionMatcherDescription<S, A extends AnyAction> = {\r\n  matcher: ActionMatcher<A>\r\n  reducer: CaseReducer<S, NoInfer<A>>\r\n}\r\n\r\nexport type ReadonlyActionMatcherDescriptionCollection<S> = ReadonlyArray<\r\n  ActionMatcherDescription<S, any>\r\n>\r\n\r\nexport type ActionMatcherDescriptionCollection<S> = Array<\r\n  ActionMatcherDescription<S, any>\r\n>\r\n\r\n/**\r\n * A *case reducer* is a reducer function for a specific action type. Case\r\n * reducers can be composed to full reducers using `createReducer()`.\r\n *\r\n * Unlike a normal Redux reducer, a case reducer is never called with an\r\n * `undefined` state to determine the initial state. Instead, the initial\r\n * state is explicitly specified as an argument to `createReducer()`.\r\n *\r\n * In addition, a case reducer can choose to mutate the passed-in `state`\r\n * value directly instead of returning a new state. This does not actually\r\n * cause the store state to be mutated directly; instead, thanks to\r\n * [immer](https://github.com/mweststrate/immer), the mutations are\r\n * translated to copy operations that result in a new state.\r\n *\r\n * @public\r\n */\r\nexport type CaseReducer<S = any, A extends Action = AnyAction> = (\r\n  state: Draft<S>,\r\n  action: A\r\n) => NoInfer<S> | void | Draft<NoInfer<S>>\r\n\r\n/**\r\n * A mapping from action types to case reducers for `createReducer()`.\r\n *\r\n * @deprecated This should not be used manually - it is only used\r\n *             for internal inference purposes and using it manually\r\n *             would lead to type erasure.\r\n *             It might be removed in the future.\r\n * @public\r\n */\r\nexport type CaseReducers<S, AS extends Actions> = {\r\n  [T in keyof AS]: AS[T] extends Action ? CaseReducer<S, AS[T]> : void\r\n}\r\n\r\nexport type NotFunction<T> = T extends Function ? never : T\r\n\r\nfunction isStateFunction<S>(x: unknown): x is () => S {\r\n  return typeof x === 'function'\r\n}\r\n\r\nexport type ReducerWithInitialState<S extends NotFunction<any>> = Reducer<S> & {\r\n  getInitialState: () => S\r\n}\r\n\r\nlet hasWarnedAboutObjectNotation = false\r\n\r\n/**\r\n * A utility function that allows defining a reducer as a mapping from action\r\n * type to *case reducer* functions that handle these action types. The\r\n * reducer's initial state is passed as the first argument.\r\n *\r\n * @remarks\r\n * The body of every case reducer is implicitly wrapped with a call to\r\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\r\n * This means that rather than returning a new state object, you can also\r\n * mutate the passed-in state object directly; these mutations will then be\r\n * automatically and efficiently translated into copies, giving you both\r\n * convenience and immutability.\r\n *\r\n * @overloadSummary\r\n * This overload accepts a callback function that receives a `builder` object as its argument.\r\n * That builder provides `addCase`, `addMatcher` and `addDefaultCase` functions that may be\r\n * called to define what actions this reducer will handle.\r\n *\r\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n * @param builderCallback - `(builder: Builder) => void` A callback that receives a *builder* object to define\r\n *   case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\r\n * @example\r\n```ts\r\nimport {\r\n  createAction,\r\n  createReducer,\r\n  AnyAction,\r\n  PayloadAction,\r\n} from \"@reduxjs/toolkit\";\r\n\r\nconst increment = createAction<number>(\"increment\");\r\nconst decrement = createAction<number>(\"decrement\");\r\n\r\nfunction isActionWithNumberPayload(\r\n  action: AnyAction\r\n): action is PayloadAction<number> {\r\n  return typeof action.payload === \"number\";\r\n}\r\n\r\nconst reducer = createReducer(\r\n  {\r\n    counter: 0,\r\n    sumOfNumberPayloads: 0,\r\n    unhandledActions: 0,\r\n  },\r\n  (builder) => {\r\n    builder\r\n      .addCase(increment, (state, action) => {\r\n        // action is inferred correctly here\r\n        state.counter += action.payload;\r\n      })\r\n      // You can chain calls, or have separate `builder.addCase()` lines each time\r\n      .addCase(decrement, (state, action) => {\r\n        state.counter -= action.payload;\r\n      })\r\n      // You can apply a \"matcher function\" to incoming actions\r\n      .addMatcher(isActionWithNumberPayload, (state, action) => {})\r\n      // and provide a default case if no other handlers matched\r\n      .addDefaultCase((state, action) => {});\r\n  }\r\n);\r\n```\r\n * @public\r\n */\r\nexport function createReducer<S extends NotFunction<any>>(\r\n  initialState: S | (() => S),\r\n  builderCallback: (builder: ActionReducerMapBuilder<S>) => void\r\n): ReducerWithInitialState<S>\r\n\r\n/**\r\n * A utility function that allows defining a reducer as a mapping from action\r\n * type to *case reducer* functions that handle these action types. The\r\n * reducer's initial state is passed as the first argument.\r\n *\r\n * The body of every case reducer is implicitly wrapped with a call to\r\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\r\n * This means that rather than returning a new state object, you can also\r\n * mutate the passed-in state object directly; these mutations will then be\r\n * automatically and efficiently translated into copies, giving you both\r\n * convenience and immutability.\r\n * \r\n * @overloadSummary\r\n * This overload accepts an object where the keys are string action types, and the values\r\n * are case reducer functions to handle those action types.\r\n *\r\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n * @param actionsMap - An object mapping from action types to _case reducers_, each of which handles one specific action type.\r\n * @param actionMatchers - An array of matcher definitions in the form `{matcher, reducer}`.\r\n *   All matching reducers will be executed in order, independently if a case reducer matched or not.\r\n * @param defaultCaseReducer - A \"default case\" reducer that is executed if no case reducer and no matcher\r\n *   reducer was executed for this action.\r\n *\r\n * @example\r\n```js\r\nconst counterReducer = createReducer(0, {\r\n  increment: (state, action) => state + action.payload,\r\n  decrement: (state, action) => state - action.payload\r\n})\r\n\r\n// Alternately, use a \"lazy initializer\" to provide the initial state\r\n// (works with either form of createReducer)\r\nconst initialState = () => 0\r\nconst counterReducer = createReducer(initialState, {\r\n  increment: (state, action) => state + action.payload,\r\n  decrement: (state, action) => state - action.payload\r\n})\r\n```\r\n \r\n * Action creators that were generated using [`createAction`](./createAction) may be used directly as the keys here, using computed property syntax:\r\n\r\n```js\r\nconst increment = createAction('increment')\r\nconst decrement = createAction('decrement')\r\n\r\nconst counterReducer = createReducer(0, {\r\n  [increment]: (state, action) => state + action.payload,\r\n  [decrement.type]: (state, action) => state - action.payload\r\n})\r\n```\r\n * @public\r\n */\r\nexport function createReducer<\r\n  S extends NotFunction<any>,\r\n  CR extends CaseReducers<S, any> = CaseReducers<S, any>\r\n>(\r\n  initialState: S | (() => S),\r\n  actionsMap: CR,\r\n  actionMatchers?: ActionMatcherDescriptionCollection<S>,\r\n  defaultCaseReducer?: CaseReducer<S>\r\n): ReducerWithInitialState<S>\r\n\r\nexport function createReducer<S extends NotFunction<any>>(\r\n  initialState: S | (() => S),\r\n  mapOrBuilderCallback:\r\n    | CaseReducers<S, any>\r\n    | ((builder: ActionReducerMapBuilder<S>) => void),\r\n  actionMatchers: ReadonlyActionMatcherDescriptionCollection<S> = [],\r\n  defaultCaseReducer?: CaseReducer<S>\r\n): ReducerWithInitialState<S> {\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    if (typeof mapOrBuilderCallback === 'object') {\r\n      if (!hasWarnedAboutObjectNotation) {\r\n        hasWarnedAboutObjectNotation = true\r\n        console.warn(\r\n          \"The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\"\r\n        )\r\n      }\r\n    }\r\n  }\r\n\r\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] =\r\n    typeof mapOrBuilderCallback === 'function'\r\n      ? executeReducerBuilderCallback(mapOrBuilderCallback)\r\n      : [mapOrBuilderCallback, actionMatchers, defaultCaseReducer]\r\n\r\n  // Ensure the initial state gets frozen either way (if draftable)\r\n  let getInitialState: () => S\r\n  if (isStateFunction(initialState)) {\r\n    getInitialState = () => freezeDraftable(initialState())\r\n  } else {\r\n    const frozenInitialState = freezeDraftable(initialState)\r\n    getInitialState = () => frozenInitialState\r\n  }\r\n\r\n  function reducer(state = getInitialState(), action: any): S {\r\n    let caseReducers = [\r\n      actionsMap[action.type],\r\n      ...finalActionMatchers\r\n        .filter(({ matcher }) => matcher(action))\r\n        .map(({ reducer }) => reducer),\r\n    ]\r\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\r\n      caseReducers = [finalDefaultCaseReducer]\r\n    }\r\n\r\n    return caseReducers.reduce((previousState, caseReducer): S => {\r\n      if (caseReducer) {\r\n        if (isDraft(previousState)) {\r\n          // If it's already a draft, we must already be inside a `createNextState` call,\r\n          // likely because this is being wrapped in `createReducer`, `createSlice`, or nested\r\n          // inside an existing draft. It's safe to just pass the draft to the mutator.\r\n          const draft = previousState as Draft<S> // We can assume this is already a draft\r\n          const result = caseReducer(draft, action)\r\n\r\n          if (result === undefined) {\r\n            return previousState\r\n          }\r\n\r\n          return result as S\r\n        } else if (!isDraftable(previousState)) {\r\n          // If state is not draftable (ex: a primitive, such as 0), we want to directly\r\n          // return the caseReducer func and not wrap it with produce.\r\n          const result = caseReducer(previousState as any, action)\r\n\r\n          if (result === undefined) {\r\n            if (previousState === null) {\r\n              return previousState\r\n            }\r\n            throw Error(\r\n              'A case reducer on a non-draftable value must not return undefined'\r\n            )\r\n          }\r\n\r\n          return result as S\r\n        } else {\r\n          // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\r\n          // than an Immutable<S>, and TypeScript cannot find out how to reconcile\r\n          // these two types.\r\n          return createNextState(previousState, (draft: Draft<S>) => {\r\n            return caseReducer(draft, action)\r\n          })\r\n        }\r\n      }\r\n\r\n      return previousState\r\n    }, state)\r\n  }\r\n\r\n  reducer.getInitialState = getInitialState\r\n\r\n  return reducer as ReducerWithInitialState<S>\r\n}\r\n", "import type { Action, AnyAction } from 'redux'\r\nimport type {\r\n  CaseReducer,\r\n  CaseReducers,\r\n  ActionMatcherDescriptionCollection,\r\n} from './createReducer'\r\nimport type { TypeGuard } from './tsHelpers'\r\n\r\nexport interface TypedActionCreator<Type extends string> {\r\n  (...args: any[]): Action<Type>\r\n  type: Type\r\n}\r\n\r\n/**\r\n * A builder for an action <-> reducer map.\r\n *\r\n * @public\r\n */\r\nexport interface ActionReducerMapBuilder<State> {\r\n  /**\r\n   * Adds a case reducer to handle a single exact action type.\r\n   * @remarks\r\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\r\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\r\n   * @param reducer - The actual case reducer function.\r\n   */\r\n  addCase<ActionCreator extends TypedActionCreator<string>>(\r\n    actionCreator: ActionCreator,\r\n    reducer: CaseReducer<State, ReturnType<ActionCreator>>\r\n  ): ActionReducerMapBuilder<State>\r\n  /**\r\n   * Adds a case reducer to handle a single exact action type.\r\n   * @remarks\r\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\r\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\r\n   * @param reducer - The actual case reducer function.\r\n   */\r\n  addCase<Type extends string, A extends Action<Type>>(\r\n    type: Type,\r\n    reducer: CaseReducer<State, A>\r\n  ): ActionReducerMapBuilder<State>\r\n\r\n  /**\r\n   * Allows you to match your incoming actions against your own filter function instead of only the `action.type` property.\r\n   * @remarks\r\n   * If multiple matcher reducers match, all of them will be executed in the order\r\n   * they were defined in - even if a case reducer already matched.\r\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\r\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\r\n   *   function\r\n   * @param reducer - The actual case reducer function.\r\n   *\r\n   * @example\r\n```ts\r\nimport {\r\n  createAction,\r\n  createReducer,\r\n  AsyncThunk,\r\n  AnyAction,\r\n} from \"@reduxjs/toolkit\";\r\n\r\ntype GenericAsyncThunk = AsyncThunk<unknown, unknown, any>;\r\n\r\ntype PendingAction = ReturnType<GenericAsyncThunk[\"pending\"]>;\r\ntype RejectedAction = ReturnType<GenericAsyncThunk[\"rejected\"]>;\r\ntype FulfilledAction = ReturnType<GenericAsyncThunk[\"fulfilled\"]>;\r\n\r\nconst initialState: Record<string, string> = {};\r\nconst resetAction = createAction(\"reset-tracked-loading-state\");\r\n\r\nfunction isPendingAction(action: AnyAction): action is PendingAction {\r\n  return action.type.endsWith(\"/pending\");\r\n}\r\n\r\nconst reducer = createReducer(initialState, (builder) => {\r\n  builder\r\n    .addCase(resetAction, () => initialState)\r\n    // matcher can be defined outside as a type predicate function\r\n    .addMatcher(isPendingAction, (state, action) => {\r\n      state[action.meta.requestId] = \"pending\";\r\n    })\r\n    .addMatcher(\r\n      // matcher can be defined inline as a type predicate function\r\n      (action): action is RejectedAction => action.type.endsWith(\"/rejected\"),\r\n      (state, action) => {\r\n        state[action.meta.requestId] = \"rejected\";\r\n      }\r\n    )\r\n    // matcher can just return boolean and the matcher can receive a generic argument\r\n    .addMatcher<FulfilledAction>(\r\n      (action) => action.type.endsWith(\"/fulfilled\"),\r\n      (state, action) => {\r\n        state[action.meta.requestId] = \"fulfilled\";\r\n      }\r\n    );\r\n});\r\n```\r\n   */\r\n  addMatcher<A>(\r\n    matcher: TypeGuard<A> | ((action: any) => boolean),\r\n    reducer: CaseReducer<State, A extends AnyAction ? A : A & AnyAction>\r\n  ): Omit<ActionReducerMapBuilder<State>, 'addCase'>\r\n\r\n  /**\r\n   * Adds a \"default case\" reducer that is executed if no case reducer and no matcher\r\n   * reducer was executed for this action.\r\n   * @param reducer - The fallback \"default case\" reducer function.\r\n   *\r\n   * @example\r\n```ts\r\nimport { createReducer } from '@reduxjs/toolkit'\r\nconst initialState = { otherActions: 0 }\r\nconst reducer = createReducer(initialState, builder => {\r\n  builder\r\n    // .addCase(...)\r\n    // .addMatcher(...)\r\n    .addDefaultCase((state, action) => {\r\n      state.otherActions++\r\n    })\r\n})\r\n```\r\n   */\r\n  addDefaultCase(reducer: CaseReducer<State, AnyAction>): {}\r\n}\r\n\r\nexport function executeReducerBuilderCallback<S>(\r\n  builderCallback: (builder: ActionReducerMapBuilder<S>) => void\r\n): [\r\n  CaseReducers<S, any>,\r\n  ActionMatcherDescriptionCollection<S>,\r\n  CaseReducer<S, AnyAction> | undefined\r\n] {\r\n  const actionsMap: CaseReducers<S, any> = {}\r\n  const actionMatchers: ActionMatcherDescriptionCollection<S> = []\r\n  let defaultCaseReducer: CaseReducer<S, AnyAction> | undefined\r\n  const builder = {\r\n    addCase(\r\n      typeOrActionCreator: string | TypedActionCreator<any>,\r\n      reducer: CaseReducer<S>\r\n    ) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        /*\r\n         to keep the definition by the user in line with actual behavior,\r\n         we enforce `addCase` to always be called before calling `addMatcher`\r\n         as matching cases take precedence over matchers\r\n         */\r\n        if (actionMatchers.length > 0) {\r\n          throw new Error(\r\n            '`builder.addCase` should only be called before calling `builder.addMatcher`'\r\n          )\r\n        }\r\n        if (defaultCaseReducer) {\r\n          throw new Error(\r\n            '`builder.addCase` should only be called before calling `builder.addDefaultCase`'\r\n          )\r\n        }\r\n      }\r\n      const type =\r\n        typeof typeOrActionCreator === 'string'\r\n          ? typeOrActionCreator\r\n          : typeOrActionCreator.type\r\n      if (!type) {\r\n        throw new Error(\r\n          '`builder.addCase` cannot be called with an empty action type'\r\n        )\r\n      }\r\n      if (type in actionsMap) {\r\n        throw new Error(\r\n          '`builder.addCase` cannot be called with two reducers for the same action type'\r\n        )\r\n      }\r\n      actionsMap[type] = reducer\r\n      return builder\r\n    },\r\n    addMatcher<A>(\r\n      matcher: TypeGuard<A>,\r\n      reducer: CaseReducer<S, A extends AnyAction ? A : A & AnyAction>\r\n    ) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        if (defaultCaseReducer) {\r\n          throw new Error(\r\n            '`builder.addMatcher` should only be called before calling `builder.addDefaultCase`'\r\n          )\r\n        }\r\n      }\r\n      actionMatchers.push({ matcher, reducer })\r\n      return builder\r\n    },\r\n    addDefaultCase(reducer: CaseReducer<S, AnyAction>) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        if (defaultCaseReducer) {\r\n          throw new Error('`builder.addDefaultCase` can only be called once')\r\n        }\r\n      }\r\n      defaultCaseReducer = reducer\r\n      return builder\r\n    },\r\n  }\r\n  builderCallback(builder)\r\n  return [actionsMap, actionMatchers, defaultCaseReducer]\r\n}\r\n", "import type { AnyAction, Reducer } from 'redux'\r\nimport { createNextState } from '.'\r\nimport type {\r\n  ActionCreatorWithoutPayload,\r\n  PayloadAction,\r\n  PayloadActionCreator,\r\n  PrepareAction,\r\n  _ActionCreatorWithPreparedPayload,\r\n} from './createAction'\r\nimport { createAction } from './createAction'\r\nimport type {\r\n  CaseReducer,\r\n  CaseReducers,\r\n  ReducerWithInitialState,\r\n} from './createReducer'\r\nimport { createReducer, NotFunction } from './createReducer'\r\nimport type { ActionReducerMapBuilder } from './mapBuilders'\r\nimport { executeReducerBuilderCallback } from './mapBuilders'\r\nimport type { NoInfer } from './tsHelpers'\r\nimport { freezeDraftable } from './utils'\r\n\r\nlet hasWarnedAboutObjectNotation = false\r\n\r\n/**\r\n * An action creator attached to a slice.\r\n *\r\n * @deprecated please use PayloadActionCreator directly\r\n *\r\n * @public\r\n */\r\nexport type SliceActionCreator<P> = PayloadActionCreator<P>\r\n\r\n/**\r\n * The return value of `createSlice`\r\n *\r\n * @public\r\n */\r\nexport interface Slice<\r\n  State = any,\r\n  CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>,\r\n  Name extends string = string\r\n> {\r\n  /**\r\n   * The slice name.\r\n   */\r\n  name: Name\r\n\r\n  /**\r\n   * The slice's reducer.\r\n   */\r\n  reducer: Reducer<State>\r\n\r\n  /**\r\n   * Action creators for the types of actions that are handled by the slice\r\n   * reducer.\r\n   */\r\n  actions: CaseReducerActions<CaseReducers, Name>\r\n\r\n  /**\r\n   * The individual case reducer functions that were passed in the `reducers` parameter.\r\n   * This enables reuse and testing if they were defined inline when calling `createSlice`.\r\n   */\r\n  caseReducers: SliceDefinedCaseReducers<CaseReducers>\r\n\r\n  /**\r\n   * Provides access to the initial state value given to the slice.\r\n   * If a lazy state initializer was provided, it will be called and a fresh value returned.\r\n   */\r\n  getInitialState: () => State\r\n}\r\n\r\n/**\r\n * Options for `createSlice()`.\r\n *\r\n * @public\r\n */\r\nexport interface CreateSliceOptions<\r\n  State = any,\r\n  CR extends SliceCaseReducers<State> = SliceCaseReducers<State>,\r\n  Name extends string = string\r\n> {\r\n  /**\r\n   * The slice's name. Used to namespace the generated action types.\r\n   */\r\n  name: Name\r\n\r\n  /**\r\n   * The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n   */\r\n  initialState: State | (() => State)\r\n\r\n  /**\r\n   * A mapping from action types to action-type-specific *case reducer*\r\n   * functions. For every action type, a matching action creator will be\r\n   * generated using `createAction()`.\r\n   */\r\n  reducers: ValidateSliceCaseReducers<State, CR>\r\n\r\n  /**\r\n   * A callback that receives a *builder* object to define\r\n   * case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\r\n   * \r\n   * Alternatively, a mapping from action types to action-type-specific *case reducer*\r\n   * functions. These reducers should have existing action types used\r\n   * as the keys, and action creators will _not_ be generated.\r\n   * \r\n   * @example\r\n```ts\r\nimport { createAction, createSlice, Action, AnyAction } from '@reduxjs/toolkit'\r\nconst incrementBy = createAction<number>('incrementBy')\r\nconst decrement = createAction('decrement')\r\n\r\ninterface RejectedAction extends Action {\r\n  error: Error\r\n}\r\n\r\nfunction isRejectedAction(action: AnyAction): action is RejectedAction {\r\n  return action.type.endsWith('rejected')\r\n}\r\n\r\ncreateSlice({\r\n  name: 'counter',\r\n  initialState: 0,\r\n  reducers: {},\r\n  extraReducers: builder => {\r\n    builder\r\n      .addCase(incrementBy, (state, action) => {\r\n        // action is inferred correctly here if using TS\r\n      })\r\n      // You can chain calls, or have separate `builder.addCase()` lines each time\r\n      .addCase(decrement, (state, action) => {})\r\n      // You can match a range of action types\r\n      .addMatcher(\r\n        isRejectedAction,\r\n        // `action` will be inferred as a RejectedAction due to isRejectedAction being defined as a type guard\r\n        (state, action) => {}\r\n      )\r\n      // and provide a default case if no other handlers matched\r\n      .addDefaultCase((state, action) => {})\r\n    }\r\n})\r\n```\r\n   */\r\n  extraReducers?:\r\n    | CaseReducers<NoInfer<State>, any>\r\n    | ((builder: ActionReducerMapBuilder<NoInfer<State>>) => void)\r\n}\r\n\r\n/**\r\n * A CaseReducer with a `prepare` method.\r\n *\r\n * @public\r\n */\r\nexport type CaseReducerWithPrepare<State, Action extends PayloadAction> = {\r\n  reducer: CaseReducer<State, Action>\r\n  prepare: PrepareAction<Action['payload']>\r\n}\r\n\r\n/**\r\n * The type describing a slice's `reducers` option.\r\n *\r\n * @public\r\n */\r\nexport type SliceCaseReducers<State> = {\r\n  [K: string]:\r\n    | CaseReducer<State, PayloadAction<any>>\r\n    | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>\r\n}\r\n\r\ntype SliceActionType<\r\n  SliceName extends string,\r\n  ActionName extends keyof any\r\n> = ActionName extends string | number ? `${SliceName}/${ActionName}` : string\r\n\r\n/**\r\n * Derives the slice's `actions` property from the `reducers` options\r\n *\r\n * @public\r\n */\r\nexport type CaseReducerActions<\r\n  CaseReducers extends SliceCaseReducers<any>,\r\n  SliceName extends string\r\n> = {\r\n  [Type in keyof CaseReducers]: CaseReducers[Type] extends { prepare: any }\r\n    ? ActionCreatorForCaseReducerWithPrepare<\r\n        CaseReducers[Type],\r\n        SliceActionType<SliceName, Type>\r\n      >\r\n    : ActionCreatorForCaseReducer<\r\n        CaseReducers[Type],\r\n        SliceActionType<SliceName, Type>\r\n      >\r\n}\r\n\r\n/**\r\n * Get a `PayloadActionCreator` type for a passed `CaseReducerWithPrepare`\r\n *\r\n * @internal\r\n */\r\ntype ActionCreatorForCaseReducerWithPrepare<\r\n  CR extends { prepare: any },\r\n  Type extends string\r\n> = _ActionCreatorWithPreparedPayload<CR['prepare'], Type>\r\n\r\n/**\r\n * Get a `PayloadActionCreator` type for a passed `CaseReducer`\r\n *\r\n * @internal\r\n */\r\ntype ActionCreatorForCaseReducer<CR, Type extends string> = CR extends (\r\n  state: any,\r\n  action: infer Action\r\n) => any\r\n  ? Action extends { payload: infer P }\r\n    ? PayloadActionCreator<P, Type>\r\n    : ActionCreatorWithoutPayload<Type>\r\n  : ActionCreatorWithoutPayload<Type>\r\n\r\n/**\r\n * Extracts the CaseReducers out of a `reducers` object, even if they are\r\n * tested into a `CaseReducerWithPrepare`.\r\n *\r\n * @internal\r\n */\r\ntype SliceDefinedCaseReducers<CaseReducers extends SliceCaseReducers<any>> = {\r\n  [Type in keyof CaseReducers]: CaseReducers[Type] extends {\r\n    reducer: infer Reducer\r\n  }\r\n    ? Reducer\r\n    : CaseReducers[Type]\r\n}\r\n\r\n/**\r\n * Used on a SliceCaseReducers object.\r\n * Ensures that if a CaseReducer is a `CaseReducerWithPrepare`, that\r\n * the `reducer` and the `prepare` function use the same type of `payload`.\r\n *\r\n * Might do additional such checks in the future.\r\n *\r\n * This type is only ever useful if you want to write your own wrapper around\r\n * `createSlice`. Please don't use it otherwise!\r\n *\r\n * @public\r\n */\r\nexport type ValidateSliceCaseReducers<\r\n  S,\r\n  ACR extends SliceCaseReducers<S>\r\n> = ACR &\r\n  {\r\n    [T in keyof ACR]: ACR[T] extends {\r\n      reducer(s: S, action?: infer A): any\r\n    }\r\n      ? {\r\n          prepare(...a: never[]): Omit<A, 'type'>\r\n        }\r\n      : {}\r\n  }\r\n\r\nfunction getType(slice: string, actionKey: string): string {\r\n  return `${slice}/${actionKey}`\r\n}\r\n\r\n/**\r\n * A function that accepts an initial state, an object full of reducer\r\n * functions, and a \"slice name\", and automatically generates\r\n * action creators and action types that correspond to the\r\n * reducers and state.\r\n *\r\n * The `reducer` argument is passed to `createReducer()`.\r\n *\r\n * @public\r\n */\r\nexport function createSlice<\r\n  State,\r\n  CaseReducers extends SliceCaseReducers<State>,\r\n  Name extends string = string\r\n>(\r\n  options: CreateSliceOptions<State, CaseReducers, Name>\r\n): Slice<State, CaseReducers, Name> {\r\n  const { name } = options\r\n  if (!name) {\r\n    throw new Error('`name` is a required option for createSlice')\r\n  }\r\n\r\n  if (\r\n    typeof process !== 'undefined' &&\r\n    process.env.NODE_ENV === 'development'\r\n  ) {\r\n    if (options.initialState === undefined) {\r\n      console.error(\r\n        'You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`'\r\n      )\r\n    }\r\n  }\r\n\r\n  const initialState =\r\n    typeof options.initialState == 'function'\r\n      ? options.initialState\r\n      : freezeDraftable(options.initialState)\r\n\r\n  const reducers = options.reducers || {}\r\n\r\n  const reducerNames = Object.keys(reducers)\r\n\r\n  const sliceCaseReducersByName: Record<string, CaseReducer> = {}\r\n  const sliceCaseReducersByType: Record<string, CaseReducer> = {}\r\n  const actionCreators: Record<string, Function> = {}\r\n\r\n  reducerNames.forEach((reducerName) => {\r\n    const maybeReducerWithPrepare = reducers[reducerName]\r\n    const type = getType(name, reducerName)\r\n\r\n    let caseReducer: CaseReducer<State, any>\r\n    let prepareCallback: PrepareAction<any> | undefined\r\n\r\n    if ('reducer' in maybeReducerWithPrepare) {\r\n      caseReducer = maybeReducerWithPrepare.reducer\r\n      prepareCallback = maybeReducerWithPrepare.prepare\r\n    } else {\r\n      caseReducer = maybeReducerWithPrepare\r\n    }\r\n\r\n    sliceCaseReducersByName[reducerName] = caseReducer\r\n    sliceCaseReducersByType[type] = caseReducer\r\n    actionCreators[reducerName] = prepareCallback\r\n      ? createAction(type, prepareCallback)\r\n      : createAction(type)\r\n  })\r\n\r\n  function buildReducer() {\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      if (typeof options.extraReducers === 'object') {\r\n        if (!hasWarnedAboutObjectNotation) {\r\n          hasWarnedAboutObjectNotation = true\r\n          console.warn(\r\n            \"The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\"\r\n          )\r\n        }\r\n      }\r\n    }\r\n    const [\r\n      extraReducers = {},\r\n      actionMatchers = [],\r\n      defaultCaseReducer = undefined,\r\n    ] =\r\n      typeof options.extraReducers === 'function'\r\n        ? executeReducerBuilderCallback(options.extraReducers)\r\n        : [options.extraReducers]\r\n\r\n    const finalCaseReducers = { ...extraReducers, ...sliceCaseReducersByType }\r\n\r\n    return createReducer(initialState, (builder) => {\r\n      for (let key in finalCaseReducers) {\r\n        builder.addCase(key, finalCaseReducers[key] as CaseReducer<any>)\r\n      }\r\n      for (let m of actionMatchers) {\r\n        builder.addMatcher(m.matcher, m.reducer)\r\n      }\r\n      if (defaultCaseReducer) {\r\n        builder.addDefaultCase(defaultCaseReducer)\r\n      }\r\n    })\r\n  }\r\n\r\n  let _reducer: ReducerWithInitialState<State>\r\n\r\n  return {\r\n    name,\r\n    reducer(state, action) {\r\n      if (!_reducer) _reducer = buildReducer()\r\n\r\n      return _reducer(state, action)\r\n    },\r\n    actions: actionCreators as any,\r\n    caseReducers: sliceCaseReducersByName as any,\r\n    getInitialState() {\r\n      if (!_reducer) _reducer = buildReducer()\r\n\r\n      return _reducer.getInitialState()\r\n    },\r\n  }\r\n}\r\n", "import type { EntityState } from './models'\r\n\r\nexport function getInitialEntityState<V>(): EntityState<V> {\r\n  return {\r\n    ids: [],\r\n    entities: {},\r\n  }\r\n}\r\n\r\nexport function createInitialStateFactory<V>() {\r\n  function getInitialState(): EntityState<V>\r\n  function getInitialState<S extends object>(\r\n    additionalState: S\r\n  ): EntityState<V> & S\r\n  function getInitialState(additionalState: any = {}): any {\r\n    return Object.assign(getInitialEntityState(), additionalState)\r\n  }\r\n\r\n  return { getInitialState }\r\n}\r\n", "import type { Selector } from 'reselect'\r\nimport { createDraftSafeSelector } from '../createDraftSafeSelector'\r\nimport type {\r\n  EntityState,\r\n  EntitySelectors,\r\n  Dictionary,\r\n  EntityId,\r\n} from './models'\r\n\r\nexport function createSelectorsFactory<T>() {\r\n  function getSelectors(): EntitySelectors<T, EntityState<T>>\r\n  function getSelectors<V>(\r\n    selectState: (state: V) => EntityState<T>\r\n  ): EntitySelectors<T, V>\r\n  function getSelectors<V>(\r\n    selectState?: (state: V) => EntityState<T>\r\n  ): EntitySelectors<T, any> {\r\n    const selectIds = (state: EntityState<T>) => state.ids\r\n\r\n    const selectEntities = (state: EntityState<T>) => state.entities\r\n\r\n    const selectAll = createDraftSafeSelector(\r\n      selectIds,\r\n      selectEntities,\r\n      (ids, entities): T[] => ids.map((id) => entities[id]!)\r\n    )\r\n\r\n    const selectId = (_: unknown, id: EntityId) => id\r\n\r\n    const selectById = (entities: Dictionary<T>, id: EntityId) => entities[id]\r\n\r\n    const selectTotal = createDraftSafeSelector(selectIds, (ids) => ids.length)\r\n\r\n    if (!selectState) {\r\n      return {\r\n        selectIds,\r\n        selectEntities,\r\n        selectAll,\r\n        selectTotal,\r\n        selectById: createDraftSafeSelector(\r\n          selectEntities,\r\n          selectId,\r\n          selectById\r\n        ),\r\n      }\r\n    }\r\n\r\n    const selectGlobalizedEntities = createDraftSafeSelector(\r\n      selectState as Selector<V, EntityState<T>>,\r\n      selectEntities\r\n    )\r\n\r\n    return {\r\n      selectIds: createDraftSafeSelector(selectState, selectIds),\r\n      selectEntities: selectGlobalizedEntities,\r\n      selectAll: createDraftSafeSelector(selectState, selectAll),\r\n      selectTotal: createDraftSafeSelector(selectState, selectTotal),\r\n      selectById: createDraftSafeSelector(\r\n        selectGlobalizedEntities,\r\n        selectId,\r\n        selectById\r\n      ),\r\n    }\r\n  }\r\n\r\n  return { getSelectors }\r\n}\r\n", "import createNextState, { isDraft } from 'immer'\r\nimport type { EntityState, PreventAny } from './models'\r\nimport type { PayloadAction } from '../createAction'\r\nimport { isFSA } from '../createAction'\r\nimport { IsAny } from '../tsHelpers'\r\n\r\nexport function createSingleArgumentStateOperator<V>(\r\n  mutator: (state: EntityState<V>) => void\r\n) {\r\n  const operator = createStateOperator((_: undefined, state: EntityState<V>) =>\r\n    mutator(state)\r\n  )\r\n\r\n  return function operation<S extends EntityState<V>>(\r\n    state: PreventAny<S, V>\r\n  ): S {\r\n    return operator(state as S, undefined)\r\n  }\r\n}\r\n\r\nexport function createStateOperator<V, R>(\r\n  mutator: (arg: R, state: EntityState<V>) => void\r\n) {\r\n  return function operation<S extends EntityState<V>>(\r\n    state: S,\r\n    arg: R | PayloadAction<R>\r\n  ): S {\r\n    function isPayloadActionArgument(\r\n      arg: R | PayloadAction<R>\r\n    ): arg is PayloadAction<R> {\r\n      return isFSA(arg)\r\n    }\r\n\r\n    const runMutator = (draft: EntityState<V>) => {\r\n      if (isPayloadActionArgument(arg)) {\r\n        mutator(arg.payload, draft)\r\n      } else {\r\n        mutator(arg, draft)\r\n      }\r\n    }\r\n\r\n    if (isDraft(state)) {\r\n      // we must already be inside a `createNextState` call, likely because\r\n      // this is being wrapped in `createReducer` or `createSlice`.\r\n      // It's safe to just pass the draft to the mutator.\r\n      runMutator(state)\r\n\r\n      // since it's a draft, we'll just return it\r\n      return state\r\n    } else {\r\n      // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\r\n      // than an Immutable<S>, and TypeScript cannot find out how to reconcile\r\n      // these two types.\r\n      return createNextState(state, runMutator)\r\n    }\r\n  }\r\n}\r\n", "import type { EntityState, IdSelector, Update, EntityId } from './models'\r\n\r\nexport function selectIdValue<T>(entity: T, selectId: IdSelector<T>) {\r\n  const key = selectId(entity)\r\n\r\n  if (process.env.NODE_ENV !== 'production' && key === undefined) {\r\n    console.warn(\r\n      'The entity passed to the `selectId` implementation returned undefined.',\r\n      'You should probably provide your own `selectId` implementation.',\r\n      'The entity that was passed:',\r\n      entity,\r\n      'The `selectId` implementation:',\r\n      selectId.toString()\r\n    )\r\n  }\r\n\r\n  return key\r\n}\r\n\r\nexport function ensureEntitiesArray<T>(\r\n  entities: readonly T[] | Record<EntityId, T>\r\n): readonly T[] {\r\n  if (!Array.isArray(entities)) {\r\n    entities = Object.values(entities)\r\n  }\r\n\r\n  return entities\r\n}\r\n\r\nexport function splitAddedUpdatedEntities<T>(\r\n  newEntities: readonly T[] | Record<EntityId, T>,\r\n  selectId: IdSelector<T>,\r\n  state: EntityState<T>\r\n): [T[], Update<T>[]] {\r\n  newEntities = ensureEntitiesArray(newEntities)\r\n\r\n  const added: T[] = []\r\n  const updated: Update<T>[] = []\r\n\r\n  for (const entity of newEntities) {\r\n    const id = selectIdValue(entity, selectId)\r\n    if (id in state.entities) {\r\n      updated.push({ id, changes: entity })\r\n    } else {\r\n      added.push(entity)\r\n    }\r\n  }\r\n  return [added, updated]\r\n}\r\n", "import type {\r\n  EntityState,\r\n  EntityStateAdapter,\r\n  IdSelector,\r\n  Update,\r\n  EntityId,\r\n} from './models'\r\nimport {\r\n  createStateOperator,\r\n  createSingleArgumentStateOperator,\r\n} from './state_adapter'\r\nimport {\r\n  selectIdValue,\r\n  ensureEntitiesArray,\r\n  splitAddedUpdatedEntities,\r\n} from './utils'\r\n\r\nexport function createUnsortedStateAdapter<T>(\r\n  selectId: IdSelector<T>\r\n): EntityStateAdapter<T> {\r\n  type R = EntityState<T>\r\n\r\n  function addOneMutably(entity: T, state: R): void {\r\n    const key = selectIdValue(entity, selectId)\r\n\r\n    if (key in state.entities) {\r\n      return\r\n    }\r\n\r\n    state.ids.push(key)\r\n    state.entities[key] = entity\r\n  }\r\n\r\n  function addManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    for (const entity of newEntities) {\r\n      addOneMutably(entity, state)\r\n    }\r\n  }\r\n\r\n  function setOneMutably(entity: T, state: R): void {\r\n    const key = selectIdValue(entity, selectId)\r\n    if (!(key in state.entities)) {\r\n      state.ids.push(key)\r\n    }\r\n    state.entities[key] = entity\r\n  }\r\n\r\n  function setManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    for (const entity of newEntities) {\r\n      setOneMutably(entity, state)\r\n    }\r\n  }\r\n\r\n  function setAllMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    state.ids = []\r\n    state.entities = {}\r\n\r\n    addManyMutably(newEntities, state)\r\n  }\r\n\r\n  function removeOneMutably(key: EntityId, state: R): void {\r\n    return removeManyMutably([key], state)\r\n  }\r\n\r\n  function removeManyMutably(keys: readonly EntityId[], state: R): void {\r\n    let didMutate = false\r\n\r\n    keys.forEach((key) => {\r\n      if (key in state.entities) {\r\n        delete state.entities[key]\r\n        didMutate = true\r\n      }\r\n    })\r\n\r\n    if (didMutate) {\r\n      state.ids = state.ids.filter((id) => id in state.entities)\r\n    }\r\n  }\r\n\r\n  function removeAllMutably(state: R): void {\r\n    Object.assign(state, {\r\n      ids: [],\r\n      entities: {},\r\n    })\r\n  }\r\n\r\n  function takeNewKey(\r\n    keys: { [id: string]: EntityId },\r\n    update: Update<T>,\r\n    state: R\r\n  ): boolean {\r\n    const original = state.entities[update.id]\r\n    const updated: T = Object.assign({}, original, update.changes)\r\n    const newKey = selectIdValue(updated, selectId)\r\n    const hasNewKey = newKey !== update.id\r\n\r\n    if (hasNewKey) {\r\n      keys[update.id] = newKey\r\n      delete state.entities[update.id]\r\n    }\r\n\r\n    state.entities[newKey] = updated\r\n\r\n    return hasNewKey\r\n  }\r\n\r\n  function updateOneMutably(update: Update<T>, state: R): void {\r\n    return updateManyMutably([update], state)\r\n  }\r\n\r\n  function updateManyMutably(\r\n    updates: ReadonlyArray<Update<T>>,\r\n    state: R\r\n  ): void {\r\n    const newKeys: { [id: string]: EntityId } = {}\r\n\r\n    const updatesPerEntity: { [id: string]: Update<T> } = {}\r\n\r\n    updates.forEach((update) => {\r\n      // Only apply updates to entities that currently exist\r\n      if (update.id in state.entities) {\r\n        // If there are multiple updates to one entity, merge them together\r\n        updatesPerEntity[update.id] = {\r\n          id: update.id,\r\n          // Spreads ignore falsy values, so this works even if there isn't\r\n          // an existing update already at this key\r\n          changes: {\r\n            ...(updatesPerEntity[update.id]\r\n              ? updatesPerEntity[update.id].changes\r\n              : null),\r\n            ...update.changes,\r\n          },\r\n        }\r\n      }\r\n    })\r\n\r\n    updates = Object.values(updatesPerEntity)\r\n\r\n    const didMutateEntities = updates.length > 0\r\n\r\n    if (didMutateEntities) {\r\n      const didMutateIds =\r\n        updates.filter((update) => takeNewKey(newKeys, update, state)).length >\r\n        0\r\n\r\n      if (didMutateIds) {\r\n        state.ids = Object.keys(state.entities)\r\n      }\r\n    }\r\n  }\r\n\r\n  function upsertOneMutably(entity: T, state: R): void {\r\n    return upsertManyMutably([entity], state)\r\n  }\r\n\r\n  function upsertManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    const [added, updated] = splitAddedUpdatedEntities<T>(\r\n      newEntities,\r\n      selectId,\r\n      state\r\n    )\r\n\r\n    updateManyMutably(updated, state)\r\n    addManyMutably(added, state)\r\n  }\r\n\r\n  return {\r\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\r\n    addOne: createStateOperator(addOneMutably),\r\n    addMany: createStateOperator(addManyMutably),\r\n    setOne: createStateOperator(setOneMutably),\r\n    setMany: createStateOperator(setManyMutably),\r\n    setAll: createStateOperator(setAllMutably),\r\n    updateOne: createStateOperator(updateOneMutably),\r\n    updateMany: createStateOperator(updateManyMutably),\r\n    upsertOne: createStateOperator(upsertOneMutably),\r\n    upsertMany: createStateOperator(upsertManyMutably),\r\n    removeOne: createStateOperator(removeOneMutably),\r\n    removeMany: createStateOperator(removeManyMutably),\r\n  }\r\n}\r\n", "import type {\r\n  EntityState,\r\n  IdSelector,\r\n  Comparer,\r\n  EntityStateAdapter,\r\n  Update,\r\n  EntityId,\r\n} from './models'\r\nimport { createStateOperator } from './state_adapter'\r\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter'\r\nimport {\r\n  selectIdValue,\r\n  ensureEntitiesArray,\r\n  splitAddedUpdatedEntities,\r\n} from './utils'\r\n\r\nexport function createSortedStateAdapter<T>(\r\n  selectId: IdSelector<T>,\r\n  sort: Comparer<T>\r\n): EntityStateAdapter<T> {\r\n  type R = EntityState<T>\r\n\r\n  const { removeOne, removeMany, removeAll } =\r\n    createUnsortedStateAdapter(selectId)\r\n\r\n  function addOneMutably(entity: T, state: R): void {\r\n    return addManyMutably([entity], state)\r\n  }\r\n\r\n  function addManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    const models = newEntities.filter(\r\n      (model) => !(selectIdValue(model, selectId) in state.entities)\r\n    )\r\n\r\n    if (models.length !== 0) {\r\n      merge(models, state)\r\n    }\r\n  }\r\n\r\n  function setOneMutably(entity: T, state: R): void {\r\n    return setManyMutably([entity], state)\r\n  }\r\n\r\n  function setManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    if (newEntities.length !== 0) {\r\n      merge(newEntities, state)\r\n    }\r\n  }\r\n\r\n  function setAllMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    state.entities = {}\r\n    state.ids = []\r\n\r\n    addManyMutably(newEntities, state)\r\n  }\r\n\r\n  function updateOneMutably(update: Update<T>, state: R): void {\r\n    return updateManyMutably([update], state)\r\n  }\r\n\r\n  function updateManyMutably(\r\n    updates: ReadonlyArray<Update<T>>,\r\n    state: R\r\n  ): void {\r\n    let appliedUpdates = false\r\n\r\n    for (let update of updates) {\r\n      const entity = state.entities[update.id]\r\n      if (!entity) {\r\n        continue\r\n      }\r\n\r\n      appliedUpdates = true\r\n\r\n      Object.assign(entity, update.changes)\r\n      const newId = selectId(entity)\r\n      if (update.id !== newId) {\r\n        delete state.entities[update.id]\r\n        state.entities[newId] = entity\r\n      }\r\n    }\r\n\r\n    if (appliedUpdates) {\r\n      resortEntities(state)\r\n    }\r\n  }\r\n\r\n  function upsertOneMutably(entity: T, state: R): void {\r\n    return upsertManyMutably([entity], state)\r\n  }\r\n\r\n  function upsertManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    const [added, updated] = splitAddedUpdatedEntities<T>(\r\n      newEntities,\r\n      selectId,\r\n      state\r\n    )\r\n\r\n    updateManyMutably(updated, state)\r\n    addManyMutably(added, state)\r\n  }\r\n\r\n  function areArraysEqual(a: readonly unknown[], b: readonly unknown[]) {\r\n    if (a.length !== b.length) {\r\n      return false\r\n    }\r\n\r\n    for (let i = 0; i < a.length && i < b.length; i++) {\r\n      if (a[i] === b[i]) {\r\n        continue\r\n      }\r\n      return false\r\n    }\r\n    return true\r\n  }\r\n\r\n  function merge(models: readonly T[], state: R): void {\r\n    // Insert/overwrite all new/updated\r\n    models.forEach((model) => {\r\n      state.entities[selectId(model)] = model\r\n    })\r\n\r\n    resortEntities(state)\r\n  }\r\n\r\n  function resortEntities(state: R) {\r\n    const allEntities = Object.values(state.entities) as T[]\r\n    allEntities.sort(sort)\r\n\r\n    const newSortedIds = allEntities.map(selectId)\r\n    const { ids } = state\r\n\r\n    if (!areArraysEqual(ids, newSortedIds)) {\r\n      state.ids = newSortedIds\r\n    }\r\n  }\r\n\r\n  return {\r\n    removeOne,\r\n    removeMany,\r\n    removeAll,\r\n    addOne: createStateOperator(addOneMutably),\r\n    updateOne: createStateOperator(updateOneMutably),\r\n    upsertOne: createStateOperator(upsertOneMutably),\r\n    setOne: createStateOperator(setOneMutably),\r\n    setMany: createStateOperator(setManyMutably),\r\n    setAll: createStateOperator(setAllMutably),\r\n    addMany: createStateOperator(addManyMutably),\r\n    updateMany: createStateOperator(updateManyMutably),\r\n    upsertMany: createStateOperator(upsertManyMutably),\r\n  }\r\n}\r\n", "import type {\r\n  EntityDefinition,\r\n  Comparer,\r\n  IdSelector,\r\n  EntityAdapter,\r\n} from './models'\r\nimport { createInitialStateFactory } from './entity_state'\r\nimport { createSelectorsFactory } from './state_selectors'\r\nimport { createSortedStateAdapter } from './sorted_state_adapter'\r\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter'\r\n\r\n/**\r\n *\r\n * @param options\r\n *\r\n * @public\r\n */\r\nexport function createEntityAdapter<T>(\r\n  options: {\r\n    selectId?: IdSelector<T>\r\n    sortComparer?: false | Comparer<T>\r\n  } = {}\r\n): EntityAdapter<T> {\r\n  const { selectId, sortComparer }: EntityDefinition<T> = {\r\n    sortComparer: false,\r\n    selectId: (instance: any) => instance.id,\r\n    ...options,\r\n  }\r\n\r\n  const stateFactory = createInitialStateFactory<T>()\r\n  const selectorsFactory = createSelectorsFactory<T>()\r\n  const stateAdapter = sortComparer\r\n    ? createSortedStateAdapter(selectId, sortComparer)\r\n    : createUnsortedStateAdapter(selectId)\r\n\r\n  return {\r\n    selectId,\r\n    sortComparer,\r\n    ...stateFactory,\r\n    ...selectorsFactory,\r\n    ...stateAdapter,\r\n  }\r\n}\r\n", "// Borrowed from https://github.com/ai/nanoid/blob/3.0.2/non-secure/index.js\r\n// This alphabet uses `A-Za-z0-9_-` symbols. A genetic algorithm helped\r\n// optimize the gzip compression for this alphabet.\r\nlet urlAlphabet =\r\n  'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW'\r\n\r\n/**\r\n *\r\n * @public\r\n */\r\nexport let nanoid = (size = 21) => {\r\n  let id = ''\r\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\r\n  let i = size\r\n  while (i--) {\r\n    // `| 0` is more compact and faster than `Math.floor()`.\r\n    id += urlAlphabet[(Math.random() * 64) | 0]\r\n  }\r\n  return id\r\n}\r\n", "import type { Dispatch, AnyAction } from 'redux'\r\nimport type {\r\n  PayloadAction,\r\n  ActionCreatorWithPreparedPayload,\r\n} from './createAction'\r\nimport { createAction } from './createAction'\r\nimport type { ThunkDispatch } from 'redux-thunk'\r\nimport type { FallbackIfUnknown, Id, IsAny, IsUnknown } from './tsHelpers'\r\nimport { nanoid } from './nanoid'\r\n\r\n// @ts-ignore we need the import of these types due to a bundling issue.\r\ntype _Keep = PayloadAction | ActionCreatorWithPreparedPayload<any, unknown>\r\n\r\nexport type BaseThunkAPI<\r\n  S,\r\n  E,\r\n  D extends Dispatch = Dispatch,\r\n  RejectedValue = unknown,\r\n  RejectedMeta = unknown,\r\n  FulfilledMeta = unknown\r\n> = {\r\n  dispatch: D\r\n  getState: () => S\r\n  extra: E\r\n  requestId: string\r\n  signal: AbortSignal\r\n  abort: (reason?: string) => void\r\n  rejectWithValue: IsUnknown<\r\n    RejectedMeta,\r\n    (value: RejectedValue) => RejectWithValue<RejectedValue, RejectedMeta>,\r\n    (\r\n      value: RejectedValue,\r\n      meta: RejectedMeta\r\n    ) => RejectWithValue<RejectedValue, RejectedMeta>\r\n  >\r\n  fulfillWithValue: IsUnknown<\r\n    FulfilledMeta,\r\n    <FulfilledValue>(value: FulfilledValue) => FulfilledValue,\r\n    <FulfilledValue>(\r\n      value: FulfilledValue,\r\n      meta: FulfilledMeta\r\n    ) => FulfillWithMeta<FulfilledValue, FulfilledMeta>\r\n  >\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport interface SerializedError {\r\n  name?: string\r\n  message?: string\r\n  stack?: string\r\n  code?: string\r\n}\r\n\r\nconst commonProperties: Array<keyof SerializedError> = [\r\n  'name',\r\n  'message',\r\n  'stack',\r\n  'code',\r\n]\r\n\r\nclass RejectWithValue<Payload, RejectedMeta> {\r\n  /*\r\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\r\n  does not exist at runtime\r\n  */\r\n  private readonly _type!: 'RejectWithValue'\r\n  constructor(\r\n    public readonly payload: Payload,\r\n    public readonly meta: RejectedMeta\r\n  ) {}\r\n}\r\n\r\nclass FulfillWithMeta<Payload, FulfilledMeta> {\r\n  /*\r\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\r\n  does not exist at runtime\r\n  */\r\n  private readonly _type!: 'FulfillWithMeta'\r\n  constructor(\r\n    public readonly payload: Payload,\r\n    public readonly meta: FulfilledMeta\r\n  ) {}\r\n}\r\n\r\n/**\r\n * Serializes an error into a plain object.\r\n * Reworked from https://github.com/sindresorhus/serialize-error\r\n *\r\n * @public\r\n */\r\nexport const miniSerializeError = (value: any): SerializedError => {\r\n  if (typeof value === 'object' && value !== null) {\r\n    const simpleError: SerializedError = {}\r\n    for (const property of commonProperties) {\r\n      if (typeof value[property] === 'string') {\r\n        simpleError[property] = value[property]\r\n      }\r\n    }\r\n\r\n    return simpleError\r\n  }\r\n\r\n  return { message: String(value) }\r\n}\r\n\r\ntype AsyncThunkConfig = {\r\n  state?: unknown\r\n  dispatch?: Dispatch\r\n  extra?: unknown\r\n  rejectValue?: unknown\r\n  serializedErrorType?: unknown\r\n  pendingMeta?: unknown\r\n  fulfilledMeta?: unknown\r\n  rejectedMeta?: unknown\r\n}\r\n\r\ntype GetState<ThunkApiConfig> = ThunkApiConfig extends {\r\n  state: infer State\r\n}\r\n  ? State\r\n  : unknown\r\ntype GetExtra<ThunkApiConfig> = ThunkApiConfig extends { extra: infer Extra }\r\n  ? Extra\r\n  : unknown\r\ntype GetDispatch<ThunkApiConfig> = ThunkApiConfig extends {\r\n  dispatch: infer Dispatch\r\n}\r\n  ? FallbackIfUnknown<\r\n      Dispatch,\r\n      ThunkDispatch<\r\n        GetState<ThunkApiConfig>,\r\n        GetExtra<ThunkApiConfig>,\r\n        AnyAction\r\n      >\r\n    >\r\n  : ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, AnyAction>\r\n\r\nexport type GetThunkAPI<ThunkApiConfig> = BaseThunkAPI<\r\n  GetState<ThunkApiConfig>,\r\n  GetExtra<ThunkApiConfig>,\r\n  GetDispatch<ThunkApiConfig>,\r\n  GetRejectValue<ThunkApiConfig>,\r\n  GetRejectedMeta<ThunkApiConfig>,\r\n  GetFulfilledMeta<ThunkApiConfig>\r\n>\r\n\r\ntype GetRejectValue<ThunkApiConfig> = ThunkApiConfig extends {\r\n  rejectValue: infer RejectValue\r\n}\r\n  ? RejectValue\r\n  : unknown\r\n\r\ntype GetPendingMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  pendingMeta: infer PendingMeta\r\n}\r\n  ? PendingMeta\r\n  : unknown\r\n\r\ntype GetFulfilledMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  fulfilledMeta: infer FulfilledMeta\r\n}\r\n  ? FulfilledMeta\r\n  : unknown\r\n\r\ntype GetRejectedMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  rejectedMeta: infer RejectedMeta\r\n}\r\n  ? RejectedMeta\r\n  : unknown\r\n\r\ntype GetSerializedErrorType<ThunkApiConfig> = ThunkApiConfig extends {\r\n  serializedErrorType: infer GetSerializedErrorType\r\n}\r\n  ? GetSerializedErrorType\r\n  : SerializedError\r\n\r\ntype MaybePromise<T> = T | Promise<T> | (T extends any ? Promise<T> : never)\r\n\r\n/**\r\n * A type describing the return value of the `payloadCreator` argument to `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkPayloadCreatorReturnValue<\r\n  Returned,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = MaybePromise<\r\n  | IsUnknown<\r\n      GetFulfilledMeta<ThunkApiConfig>,\r\n      Returned,\r\n      FulfillWithMeta<Returned, GetFulfilledMeta<ThunkApiConfig>>\r\n    >\r\n  | RejectWithValue<\r\n      GetRejectValue<ThunkApiConfig>,\r\n      GetRejectedMeta<ThunkApiConfig>\r\n    >\r\n>\r\n/**\r\n * A type describing the `payloadCreator` argument to `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkPayloadCreator<\r\n  Returned,\r\n  ThunkArg = void,\r\n  ThunkApiConfig extends AsyncThunkConfig = {}\r\n> = (\r\n  arg: ThunkArg,\r\n  thunkAPI: GetThunkAPI<ThunkApiConfig>\r\n) => AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig>\r\n\r\n/**\r\n * A ThunkAction created by `createAsyncThunk`.\r\n * Dispatching it returns a Promise for either a\r\n * fulfilled or rejected action.\r\n * Also, the returned value contains an `abort()` method\r\n * that allows the asyncAction to be cancelled from the outside.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkAction<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = (\r\n  dispatch: GetDispatch<ThunkApiConfig>,\r\n  getState: () => GetState<ThunkApiConfig>,\r\n  extra: GetExtra<ThunkApiConfig>\r\n) => Promise<\r\n  | ReturnType<AsyncThunkFulfilledActionCreator<Returned, ThunkArg>>\r\n  | ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>>\r\n> & {\r\n  abort: (reason?: string) => void\r\n  requestId: string\r\n  arg: ThunkArg\r\n  unwrap: () => Promise<Returned>\r\n}\r\n\r\ntype AsyncThunkActionCreator<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = IsAny<\r\n  ThunkArg,\r\n  // any handling\r\n  (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\r\n  // unknown handling\r\n  unknown extends ThunkArg\r\n    ? (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument not specified or specified as void or undefined\r\n    : [ThunkArg] extends [void] | [undefined]\r\n    ? () => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains void\r\n    : [void] extends [ThunkArg] // make optional\r\n    ? (arg?: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains undefined\r\n    : [undefined] extends [ThunkArg]\r\n    ? WithStrictNullChecks<\r\n        // with strict nullChecks: make optional\r\n        (\r\n          arg?: ThunkArg\r\n        ) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\r\n        // without strict null checks this will match everything, so don't make it optional\r\n        (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>\r\n      > // default case: normal argument\r\n    : (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>\r\n>\r\n\r\n/**\r\n * Options object for `createAsyncThunk`.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkOptions<\r\n  ThunkArg = void,\r\n  ThunkApiConfig extends AsyncThunkConfig = {}\r\n> = {\r\n  /**\r\n   * A method to control whether the asyncThunk should be executed. Has access to the\r\n   * `arg`, `api.getState()` and `api.extra` arguments.\r\n   *\r\n   * @returns `false` if it should be skipped\r\n   */\r\n  condition?(\r\n    arg: ThunkArg,\r\n    api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n  ): MaybePromise<boolean | undefined>\r\n  /**\r\n   * If `condition` returns `false`, the asyncThunk will be skipped.\r\n   * This option allows you to control whether a `rejected` action with `meta.condition == false`\r\n   * will be dispatched or not.\r\n   *\r\n   * @default `false`\r\n   */\r\n  dispatchConditionRejection?: boolean\r\n\r\n  serializeError?: (x: unknown) => GetSerializedErrorType<ThunkApiConfig>\r\n\r\n  /**\r\n   * A function to use when generating the `requestId` for the request sequence.\r\n   *\r\n   * @default `nanoid`\r\n   */\r\n  idGenerator?: (arg: ThunkArg) => string\r\n} & IsUnknown<\r\n  GetPendingMeta<ThunkApiConfig>,\r\n  {\r\n    /**\r\n     * A method to generate additional properties to be added to `meta` of the pending action.\r\n     *\r\n     * Using this optional overload will not modify the types correctly, this overload is only in place to support JavaScript users.\r\n     * Please use the `ThunkApiConfig` parameter `pendingMeta` to get access to a correctly typed overload\r\n     */\r\n    getPendingMeta?(\r\n      base: {\r\n        arg: ThunkArg\r\n        requestId: string\r\n      },\r\n      api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n    ): GetPendingMeta<ThunkApiConfig>\r\n  },\r\n  {\r\n    /**\r\n     * A method to generate additional properties to be added to `meta` of the pending action.\r\n     */\r\n    getPendingMeta(\r\n      base: {\r\n        arg: ThunkArg\r\n        requestId: string\r\n      },\r\n      api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n    ): GetPendingMeta<ThunkApiConfig>\r\n  }\r\n>\r\n\r\nexport type AsyncThunkPendingActionCreator<\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [string, ThunkArg, GetPendingMeta<ThunkApiConfig>?],\r\n  undefined,\r\n  string,\r\n  never,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'pending'\r\n  } & GetPendingMeta<ThunkApiConfig>\r\n>\r\n\r\nexport type AsyncThunkRejectedActionCreator<\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [\r\n    Error | null,\r\n    string,\r\n    ThunkArg,\r\n    GetRejectValue<ThunkApiConfig>?,\r\n    GetRejectedMeta<ThunkApiConfig>?\r\n  ],\r\n  GetRejectValue<ThunkApiConfig> | undefined,\r\n  string,\r\n  GetSerializedErrorType<ThunkApiConfig>,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'rejected'\r\n    aborted: boolean\r\n    condition: boolean\r\n  } & (\r\n    | ({ rejectedWithValue: false } & {\r\n        [K in keyof GetRejectedMeta<ThunkApiConfig>]?: undefined\r\n      })\r\n    | ({ rejectedWithValue: true } & GetRejectedMeta<ThunkApiConfig>)\r\n  )\r\n>\r\n\r\nexport type AsyncThunkFulfilledActionCreator<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [Returned, string, ThunkArg, GetFulfilledMeta<ThunkApiConfig>?],\r\n  Returned,\r\n  string,\r\n  never,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'fulfilled'\r\n  } & GetFulfilledMeta<ThunkApiConfig>\r\n>\r\n\r\n/**\r\n * A type describing the return value of `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunk<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig> & {\r\n  pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig>\r\n  rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>\r\n  fulfilled: AsyncThunkFulfilledActionCreator<\r\n    Returned,\r\n    ThunkArg,\r\n    ThunkApiConfig\r\n  >\r\n  typePrefix: string\r\n}\r\n\r\ntype OverrideThunkApiConfigs<OldConfig, NewConfig> = Id<\r\n  NewConfig & Omit<OldConfig, keyof NewConfig>\r\n>\r\n\r\ntype CreateAsyncThunk<CurriedThunkApiConfig extends AsyncThunkConfig> = {\r\n  /**\r\n   *\r\n   * @param typePrefix\r\n   * @param payloadCreator\r\n   * @param options\r\n   *\r\n   * @public\r\n   */\r\n  // separate signature without `AsyncThunkConfig` for better inference\r\n  <Returned, ThunkArg = void>(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      CurriedThunkApiConfig\r\n    >,\r\n    options?: AsyncThunkOptions<ThunkArg, CurriedThunkApiConfig>\r\n  ): AsyncThunk<Returned, ThunkArg, CurriedThunkApiConfig>\r\n\r\n  /**\r\n   *\r\n   * @param typePrefix\r\n   * @param payloadCreator\r\n   * @param options\r\n   *\r\n   * @public\r\n   */\r\n  <Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n    >,\r\n    options?: AsyncThunkOptions<\r\n      ThunkArg,\r\n      OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n    >\r\n  ): AsyncThunk<\r\n    Returned,\r\n    ThunkArg,\r\n    OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n  >\r\n\r\n  withTypes<ThunkApiConfig extends AsyncThunkConfig>(): CreateAsyncThunk<\r\n    OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n  >\r\n}\r\n\r\nexport const createAsyncThunk = (() => {\r\n  function createAsyncThunk<\r\n    Returned,\r\n    ThunkArg,\r\n    ThunkApiConfig extends AsyncThunkConfig\r\n  >(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      ThunkApiConfig\r\n    >,\r\n    options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>\r\n  ): AsyncThunk<Returned, ThunkArg, ThunkApiConfig> {\r\n    type RejectedValue = GetRejectValue<ThunkApiConfig>\r\n    type PendingMeta = GetPendingMeta<ThunkApiConfig>\r\n    type FulfilledMeta = GetFulfilledMeta<ThunkApiConfig>\r\n    type RejectedMeta = GetRejectedMeta<ThunkApiConfig>\r\n\r\n    const fulfilled: AsyncThunkFulfilledActionCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      ThunkApiConfig\r\n    > = createAction(\r\n      typePrefix + '/fulfilled',\r\n      (\r\n        payload: Returned,\r\n        requestId: string,\r\n        arg: ThunkArg,\r\n        meta?: FulfilledMeta\r\n      ) => ({\r\n        payload,\r\n        meta: {\r\n          ...((meta as any) || {}),\r\n          arg,\r\n          requestId,\r\n          requestStatus: 'fulfilled' as const,\r\n        },\r\n      })\r\n    )\r\n\r\n    const pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig> =\r\n      createAction(\r\n        typePrefix + '/pending',\r\n        (requestId: string, arg: ThunkArg, meta?: PendingMeta) => ({\r\n          payload: undefined,\r\n          meta: {\r\n            ...((meta as any) || {}),\r\n            arg,\r\n            requestId,\r\n            requestStatus: 'pending' as const,\r\n          },\r\n        })\r\n      )\r\n\r\n    const rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> =\r\n      createAction(\r\n        typePrefix + '/rejected',\r\n        (\r\n          error: Error | null,\r\n          requestId: string,\r\n          arg: ThunkArg,\r\n          payload?: RejectedValue,\r\n          meta?: RejectedMeta\r\n        ) => ({\r\n          payload,\r\n          error: ((options && options.serializeError) || miniSerializeError)(\r\n            error || 'Rejected'\r\n          ) as GetSerializedErrorType<ThunkApiConfig>,\r\n          meta: {\r\n            ...((meta as any) || {}),\r\n            arg,\r\n            requestId,\r\n            rejectedWithValue: !!payload,\r\n            requestStatus: 'rejected' as const,\r\n            aborted: error?.name === 'AbortError',\r\n            condition: error?.name === 'ConditionError',\r\n          },\r\n        })\r\n      )\r\n\r\n    let displayedWarning = false\r\n\r\n    const AC =\r\n      typeof AbortController !== 'undefined'\r\n        ? AbortController\r\n        : class implements AbortController {\r\n            signal = {\r\n              aborted: false,\r\n              addEventListener() {},\r\n              dispatchEvent() {\r\n                return false\r\n              },\r\n              onabort() {},\r\n              removeEventListener() {},\r\n              reason: undefined,\r\n              throwIfAborted() {},\r\n            }\r\n            abort() {\r\n              if (process.env.NODE_ENV !== 'production') {\r\n                if (!displayedWarning) {\r\n                  displayedWarning = true\r\n                  console.info(\r\n                    `This platform does not implement AbortController. \r\nIf you want to use the AbortController to react to \\`abort\\` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'.`\r\n                  )\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n    function actionCreator(\r\n      arg: ThunkArg\r\n    ): AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> {\r\n      return (dispatch, getState, extra) => {\r\n        const requestId = options?.idGenerator\r\n          ? options.idGenerator(arg)\r\n          : nanoid()\r\n\r\n        const abortController = new AC()\r\n        let abortReason: string | undefined\r\n\r\n        let started = false\r\n        function abort(reason?: string) {\r\n          abortReason = reason\r\n          abortController.abort()\r\n        }\r\n\r\n        const promise = (async function () {\r\n          let finalAction: ReturnType<typeof fulfilled | typeof rejected>\r\n          try {\r\n            let conditionResult = options?.condition?.(arg, { getState, extra })\r\n            if (isThenable(conditionResult)) {\r\n              conditionResult = await conditionResult\r\n            }\r\n\r\n            if (conditionResult === false || abortController.signal.aborted) {\r\n              // eslint-disable-next-line no-throw-literal\r\n              throw {\r\n                name: 'ConditionError',\r\n                message: 'Aborted due to condition callback returning false.',\r\n              }\r\n            }\r\n            started = true\r\n\r\n            const abortedPromise = new Promise<never>((_, reject) =>\r\n              abortController.signal.addEventListener('abort', () =>\r\n                reject({\r\n                  name: 'AbortError',\r\n                  message: abortReason || 'Aborted',\r\n                })\r\n              )\r\n            )\r\n            dispatch(\r\n              pending(\r\n                requestId,\r\n                arg,\r\n                options?.getPendingMeta?.(\r\n                  { requestId, arg },\r\n                  { getState, extra }\r\n                )\r\n              )\r\n            )\r\n            finalAction = await Promise.race([\r\n              abortedPromise,\r\n              Promise.resolve(\r\n                payloadCreator(arg, {\r\n                  dispatch,\r\n                  getState,\r\n                  extra,\r\n                  requestId,\r\n                  signal: abortController.signal,\r\n                  abort,\r\n                  rejectWithValue: ((\r\n                    value: RejectedValue,\r\n                    meta?: RejectedMeta\r\n                  ) => {\r\n                    return new RejectWithValue(value, meta)\r\n                  }) as any,\r\n                  fulfillWithValue: ((value: unknown, meta?: FulfilledMeta) => {\r\n                    return new FulfillWithMeta(value, meta)\r\n                  }) as any,\r\n                })\r\n              ).then((result) => {\r\n                if (result instanceof RejectWithValue) {\r\n                  throw result\r\n                }\r\n                if (result instanceof FulfillWithMeta) {\r\n                  return fulfilled(result.payload, requestId, arg, result.meta)\r\n                }\r\n                return fulfilled(result as any, requestId, arg)\r\n              }),\r\n            ])\r\n          } catch (err) {\r\n            finalAction =\r\n              err instanceof RejectWithValue\r\n                ? rejected(null, requestId, arg, err.payload, err.meta)\r\n                : rejected(err as any, requestId, arg)\r\n          }\r\n          // We dispatch the result action _after_ the catch, to avoid having any errors\r\n          // here get swallowed by the try/catch block,\r\n          // per https://twitter.com/dan_abramov/status/770914221638942720\r\n          // and https://github.com/reduxjs/redux-toolkit/blob/e85eb17b39a2118d859f7b7746e0f3fee523e089/docs/tutorials/advanced-tutorial.md#async-error-handling-logic-in-thunks\r\n\r\n          const skipDispatch =\r\n            options &&\r\n            !options.dispatchConditionRejection &&\r\n            rejected.match(finalAction) &&\r\n            (finalAction as any).meta.condition\r\n\r\n          if (!skipDispatch) {\r\n            dispatch(finalAction)\r\n          }\r\n          return finalAction\r\n        })()\r\n        return Object.assign(promise as Promise<any>, {\r\n          abort,\r\n          requestId,\r\n          arg,\r\n          unwrap() {\r\n            return promise.then<any>(unwrapResult)\r\n          },\r\n        })\r\n      }\r\n    }\r\n\r\n    return Object.assign(\r\n      actionCreator as AsyncThunkActionCreator<\r\n        Returned,\r\n        ThunkArg,\r\n        ThunkApiConfig\r\n      >,\r\n      {\r\n        pending,\r\n        rejected,\r\n        fulfilled,\r\n        typePrefix,\r\n      }\r\n    )\r\n  }\r\n  createAsyncThunk.withTypes = () => createAsyncThunk\r\n\r\n  return createAsyncThunk as CreateAsyncThunk<AsyncThunkConfig>\r\n})()\r\n\r\ninterface UnwrappableAction {\r\n  payload: any\r\n  meta?: any\r\n  error?: any\r\n}\r\n\r\ntype UnwrappedActionPayload<T extends UnwrappableAction> = Exclude<\r\n  T,\r\n  { error: any }\r\n>['payload']\r\n\r\n/**\r\n * @public\r\n */\r\nexport function unwrapResult<R extends UnwrappableAction>(\r\n  action: R\r\n): UnwrappedActionPayload<R> {\r\n  if (action.meta && action.meta.rejectedWithValue) {\r\n    throw action.payload\r\n  }\r\n  if (action.error) {\r\n    throw action.error\r\n  }\r\n  return action.payload\r\n}\r\n\r\ntype WithStrictNullChecks<True, False> = undefined extends boolean\r\n  ? False\r\n  : True\r\n\r\nfunction isThenable(value: any): value is PromiseLike<any> {\r\n  return (\r\n    value !== null &&\r\n    typeof value === 'object' &&\r\n    typeof value.then === 'function'\r\n  )\r\n}\r\n", "import type {\r\n  Action<PERSON>romMatcher,\r\n  Matcher,\r\n  UnionToIntersection,\r\n} from './tsHelpers'\r\nimport { hasMatchFunction } from './tsHelpers'\r\nimport type {\r\n  AsyncThunk,\r\n  AsyncThunkFulfilledActionCreator,\r\n  AsyncThunkPendingActionCreator,\r\n  AsyncThunkRejectedActionCreator,\r\n} from './createAsyncThunk'\r\n\r\n/** @public */\r\nexport type ActionMatchingAnyOf<Matchers extends [...Matcher<any>[]]> =\r\n  ActionFromMatcher<Matchers[number]>\r\n\r\n/** @public */\r\nexport type ActionMatchingAllOf<Matchers extends [...Matcher<any>[]]> =\r\n  UnionToIntersection<ActionMatchingAnyOf<Matchers>>\r\n\r\nconst matches = (matcher: Matcher<any>, action: any) => {\r\n  if (hasMatchFunction(matcher)) {\r\n    return matcher.match(action)\r\n  } else {\r\n    return matcher(action)\r\n  }\r\n}\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action matches any one of the supplied type guards or action\r\n * creators.\r\n *\r\n * @param matchers The type guards or action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAnyOf<Matchers extends [...Matcher<any>[]]>(\r\n  ...matchers: Matchers\r\n) {\r\n  return (action: any): action is ActionMatchingAnyOf<Matchers> => {\r\n    return matchers.some((matcher) => matches(matcher, action))\r\n  }\r\n}\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action matches all of the supplied type guards or action\r\n * creators.\r\n *\r\n * @param matchers The type guards or action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAllOf<Matchers extends [...Matcher<any>[]]>(\r\n  ...matchers: Matchers\r\n) {\r\n  return (action: any): action is ActionMatchingAllOf<Matchers> => {\r\n    return matchers.every((matcher) => matches(matcher, action))\r\n  }\r\n}\r\n\r\n/**\r\n * @param action A redux action\r\n * @param validStatus An array of valid meta.requestStatus values\r\n *\r\n * @internal\r\n */\r\nexport function hasExpectedRequestMetadata(\r\n  action: any,\r\n  validStatus: readonly string[]\r\n) {\r\n  if (!action || !action.meta) return false\r\n\r\n  const hasValidRequestId = typeof action.meta.requestId === 'string'\r\n  const hasValidRequestStatus =\r\n    validStatus.indexOf(action.meta.requestStatus) > -1\r\n\r\n  return hasValidRequestId && hasValidRequestStatus\r\n}\r\n\r\nfunction isAsyncThunkArray(a: [any] | AnyAsyncThunk[]): a is AnyAsyncThunk[] {\r\n  return (\r\n    typeof a[0] === 'function' &&\r\n    'pending' in a[0] &&\r\n    'fulfilled' in a[0] &&\r\n    'rejected' in a[0]\r\n  )\r\n}\r\n\r\nexport type UnknownAsyncThunkPendingAction = ReturnType<\r\n  AsyncThunkPendingActionCreator<unknown>\r\n>\r\n\r\nexport type PendingActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['pending']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is pending.\r\n *\r\n * @public\r\n */\r\nexport function isPending(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkPendingAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is pending.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isPending<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is PendingActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a pending thunk action\r\n * @public\r\n */\r\nexport function isPending(action: any): action is UnknownAsyncThunkPendingAction\r\nexport function isPending<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isPending()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is PendingActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.pending\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkRejectedAction = ReturnType<\r\n  AsyncThunkRejectedActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type RejectedActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['rejected']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is rejected.\r\n *\r\n * @public\r\n */\r\nexport function isRejected(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkRejectedAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is rejected.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isRejected<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is RejectedActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a rejected thunk action\r\n * @public\r\n */\r\nexport function isRejected(\r\n  action: any\r\n): action is UnknownAsyncThunkRejectedAction\r\nexport function isRejected<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['rejected'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isRejected()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is RejectedActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.rejected\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkRejectedWithValueAction = ReturnType<\r\n  AsyncThunkRejectedActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type RejectedWithValueActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['rejected']> &\r\n    (T extends AsyncThunk<any, any, { rejectValue: infer RejectedValue }>\r\n      ? { payload: RejectedValue }\r\n      : unknown)\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is rejected with value.\r\n *\r\n * @public\r\n */\r\nexport function isRejectedWithValue(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkRejectedAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is rejected with value.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isRejectedWithValue<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (\r\n  action: any\r\n) => action is RejectedWithValueActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a rejected thunk action with value\r\n * @public\r\n */\r\nexport function isRejectedWithValue(\r\n  action: any\r\n): action is UnknownAsyncThunkRejectedAction\r\nexport function isRejectedWithValue<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  const hasFlag = (action: any): action is any => {\r\n    return action && action.meta && action.meta.rejectedWithValue\r\n  }\r\n\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => {\r\n      const combinedMatcher = isAllOf(isRejected(...asyncThunks), hasFlag)\r\n\r\n      return combinedMatcher(action)\r\n    }\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isRejectedWithValue()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is RejectedActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    const combinedMatcher = isAllOf(isRejected(...asyncThunks), hasFlag)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkFulfilledAction = ReturnType<\r\n  AsyncThunkFulfilledActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type FulfilledActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['fulfilled']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is fulfilled.\r\n *\r\n * @public\r\n */\r\nexport function isFulfilled(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkFulfilledAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is fulfilled.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isFulfilled<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is FulfilledActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a fulfilled thunk action\r\n * @public\r\n */\r\nexport function isFulfilled(\r\n  action: any\r\n): action is UnknownAsyncThunkFulfilledAction\r\nexport function isFulfilled<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['fulfilled'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isFulfilled()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is FulfilledActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.fulfilled\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkAction =\r\n  | UnknownAsyncThunkPendingAction\r\n  | UnknownAsyncThunkRejectedAction\r\n  | UnknownAsyncThunkFulfilledAction\r\n\r\nexport type AnyAsyncThunk = {\r\n  pending: { match: (action: any) => action is any }\r\n  fulfilled: { match: (action: any) => action is any }\r\n  rejected: { match: (action: any) => action is any }\r\n}\r\n\r\nexport type ActionsFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  | ActionFromMatcher<T['pending']>\r\n  | ActionFromMatcher<T['fulfilled']>\r\n  | ActionFromMatcher<T['rejected']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator.\r\n *\r\n * @public\r\n */\r\nexport function isAsyncThunkAction(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAsyncThunkAction<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is ActionsFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a thunk action\r\n * @public\r\n */\r\nexport function isAsyncThunkAction(\r\n  action: any\r\n): action is UnknownAsyncThunkAction\r\nexport function isAsyncThunkAction<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) =>\r\n      hasExpectedRequestMetadata(action, ['pending', 'fulfilled', 'rejected'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isAsyncThunkAction()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is ActionsFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = [] as any\r\n\r\n    for (const asyncThunk of asyncThunks) {\r\n      matchers.push(\r\n        asyncThunk.pending,\r\n        asyncThunk.rejected,\r\n        asyncThunk.fulfilled\r\n      )\r\n    }\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n", "import type { AbortSignalWithReason } from './types'\r\n\r\nexport const assertFunction: (\r\n  func: unknown,\r\n  expected: string\r\n) => asserts func is (...args: unknown[]) => unknown = (\r\n  func: unknown,\r\n  expected: string\r\n) => {\r\n  if (typeof func !== 'function') {\r\n    throw new TypeError(`${expected} is not a function`)\r\n  }\r\n}\r\n\r\nexport const noop = () => {}\r\n\r\nexport const catchRejection = <T>(\r\n  promise: Promise<T>,\r\n  onError = noop\r\n): Promise<T> => {\r\n  promise.catch(onError)\r\n\r\n  return promise\r\n}\r\n\r\nexport const addAbortSignalListener = (\r\n  abortSignal: AbortSignal,\r\n  callback: (evt: Event) => void\r\n) => {\r\n  abortSignal.addEventListener('abort', callback, { once: true })\r\n  return () => abortSignal.removeEventListener('abort', callback)\r\n}\r\n\r\n/**\r\n * Calls `abortController.abort(reason)` and patches `signal.reason`.\r\n * if it is not supported.\r\n *\r\n * At the time of writing `signal.reason` is available in FF chrome, edge node 17 and deno.\r\n * @param abortController\r\n * @param reason\r\n * @returns\r\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/reason\r\n */\r\nexport const abortControllerWithReason = <T>(\r\n  abortController: AbortController,\r\n  reason: T\r\n): void => {\r\n  type Consumer<T> = (val: T) => void\r\n\r\n  const signal = abortController.signal as AbortSignalWithReason<T>\r\n\r\n  if (signal.aborted) {\r\n    return\r\n  }\r\n\r\n  // Patch `reason` if necessary.\r\n  // - We use defineProperty here because reason is a getter of `AbortSignal.__proto__`.\r\n  // - We need to patch 'reason' before calling `.abort()` because listeners to the 'abort'\r\n  // event are are notified immediately.\r\n  if (!('reason' in signal)) {\r\n    Object.defineProperty(signal, 'reason', {\r\n      enumerable: true,\r\n      value: reason,\r\n      configurable: true,\r\n      writable: true,\r\n    })\r\n  }\r\n\r\n  ;(abortController.abort as Consumer<typeof reason>)(reason)\r\n}\r\n", "import type { SerializedError } from '@reduxjs/toolkit'\r\n\r\nconst task = 'task'\r\nconst listener = 'listener'\r\nconst completed = 'completed'\r\nconst cancelled = 'cancelled'\r\n\r\n/* TaskAbortError error codes  */\r\nexport const taskCancelled = `task-${cancelled}` as const\r\nexport const taskCompleted = `task-${completed}` as const\r\nexport const listenerCancelled = `${listener}-${cancelled}` as const\r\nexport const listenerCompleted = `${listener}-${completed}` as const\r\n\r\nexport class TaskAbortError implements SerializedError {\r\n  name = 'TaskAbortError'\r\n  message: string\r\n  constructor(public code: string | undefined) {\r\n    this.message = `${task} ${cancelled} (reason: ${code})`\r\n  }\r\n}\r\n", "import { TaskAbortError } from './exceptions'\r\nimport type { AbortSignalWithReason, TaskResult } from './types'\r\nimport { addAbortSignalListener, catchRejection, noop } from './utils'\r\n\r\n/**\r\n * Synchronously raises {@link TaskAbortError} if the task tied to the input `signal` has been cancelled.\r\n * @param signal\r\n * @param reason\r\n * @see {TaskAbortError}\r\n */\r\nexport const validateActive = (signal: AbortSignal): void => {\r\n  if (signal.aborted) {\r\n    throw new TaskAbortError((signal as AbortSignalWithReason<string>).reason)\r\n  }\r\n}\r\n\r\n/**\r\n * Generates a race between the promise(s) and the AbortSignal\r\n * This avoids `Promise.race()`-related memory leaks:\r\n * https://github.com/nodejs/node/issues/17469#issuecomment-349794909\r\n */\r\nexport function raceWithSignal<T>(\r\n  signal: AbortSignalWithReason<string>,\r\n  promise: Promise<T>\r\n): Promise<T> {\r\n  let cleanup = noop\r\n  return new Promise<T>((resolve, reject) => {\r\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason))\r\n\r\n    if (signal.aborted) {\r\n      notifyRejection()\r\n      return\r\n    }\r\n\r\n    cleanup = addAbortSignalListener(signal, notifyRejection)\r\n    promise.finally(() => cleanup()).then(resolve, reject)\r\n  }).finally(() => {\r\n    // after this point, replace `cleanup` with a noop, so there is no reference to `signal` any more\r\n    cleanup = noop\r\n  })\r\n}\r\n\r\n/**\r\n * Runs a task and returns promise that resolves to {@link TaskResult}.\r\n * Second argument is an optional `cleanUp` function that always runs after task.\r\n *\r\n * **Note:** `runTask` runs the executor in the next microtask.\r\n * @returns\r\n */\r\nexport const runTask = async <T>(\r\n  task: () => Promise<T>,\r\n  cleanUp?: () => void\r\n): Promise<TaskResult<T>> => {\r\n  try {\r\n    await Promise.resolve()\r\n    const value = await task()\r\n    return {\r\n      status: 'ok',\r\n      value,\r\n    }\r\n  } catch (error: any) {\r\n    return {\r\n      status: error instanceof TaskAbortError ? 'cancelled' : 'rejected',\r\n      error,\r\n    }\r\n  } finally {\r\n    cleanUp?.()\r\n  }\r\n}\r\n\r\n/**\r\n * Given an input `AbortSignal` and a promise returns another promise that resolves\r\n * as soon the input promise is provided or rejects as soon as\r\n * `AbortSignal.abort` is `true`.\r\n * @param signal\r\n * @returns\r\n */\r\nexport const createPause = <T>(signal: AbortSignal) => {\r\n  return (promise: Promise<T>): Promise<T> => {\r\n    return catchRejection(\r\n      raceWithSignal(signal, promise).then((output) => {\r\n        validateActive(signal)\r\n        return output\r\n      })\r\n    )\r\n  }\r\n}\r\n\r\n/**\r\n * Given an input `AbortSignal` and `timeoutMs` returns a promise that resolves\r\n * after `timeoutMs` or rejects as soon as `AbortSignal.abort` is `true`.\r\n * @param signal\r\n * @returns\r\n */\r\nexport const createDelay = (signal: AbortSignal) => {\r\n  const pause = createPause<void>(signal)\r\n  return (timeoutMs: number): Promise<void> => {\r\n    return pause(new Promise<void>((resolve) => setTimeout(resolve, timeoutMs)))\r\n  }\r\n}\r\n", "import type { Dispatch, AnyAction, MiddlewareAPI } from 'redux'\r\nimport type { ThunkDispatch } from 'redux-thunk'\r\nimport { createAction, isAction } from '../createAction'\r\nimport { nanoid } from '../nanoid'\r\n\r\nimport type {\r\n  ListenerMiddleware,\r\n  ListenerMiddlewareInstance,\r\n  AddListenerOverloads,\r\n  AnyListenerPredicate,\r\n  CreateListenerMiddlewareOptions,\r\n  TypedAddListener,\r\n  TypedCreateListenerEntry,\r\n  FallbackAddListenerOptions,\r\n  ListenerEntry,\r\n  ListenerErrorHandler,\r\n  UnsubscribeListener,\r\n  TakePattern,\r\n  ListenerErrorInfo,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  TypedRemoveListener,\r\n  TaskResult,\r\n  AbortSignalWithReason,\r\n  UnsubscribeListenerOptions,\r\n  ForkOptions,\r\n} from './types'\r\nimport {\r\n  abortControllerWithReason,\r\n  addAbortSignalListener,\r\n  assertFunction,\r\n  catchRejection,\r\n} from './utils'\r\nimport {\r\n  listenerCancelled,\r\n  listenerCompleted,\r\n  TaskAbortError,\r\n  taskCancelled,\r\n  taskCompleted,\r\n} from './exceptions'\r\nimport {\r\n  runTask,\r\n  validateActive,\r\n  createPause,\r\n  createDelay,\r\n  raceWithSignal,\r\n} from './task'\r\nexport { TaskAbortError } from './exceptions'\r\nexport type {\r\n  ListenerEffect,\r\n  ListenerMiddleware,\r\n  ListenerEffectAPI,\r\n  ListenerMiddlewareInstance,\r\n  CreateListenerMiddlewareOptions,\r\n  ListenerErrorHandler,\r\n  TypedStartListening,\r\n  TypedAddListener,\r\n  TypedStopListening,\r\n  TypedRemoveListener,\r\n  UnsubscribeListener,\r\n  UnsubscribeListenerOptions,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  ForkedTaskAPI,\r\n  AsyncTaskExecutor,\r\n  SyncTaskExecutor,\r\n  TaskCancelled,\r\n  TaskRejected,\r\n  TaskResolved,\r\n  TaskResult,\r\n} from './types'\r\n\r\n//Overly-aggressive byte-shaving\r\nconst { assign } = Object\r\n/**\r\n * @internal\r\n */\r\nconst INTERNAL_NIL_TOKEN = {} as const\r\n\r\nconst alm = 'listenerMiddleware' as const\r\n\r\nconst createFork = (\r\n  parentAbortSignal: AbortSignalWithReason<unknown>,\r\n  parentBlockingPromises: Promise<any>[]\r\n) => {\r\n  const linkControllers = (controller: AbortController) =>\r\n    addAbortSignalListener(parentAbortSignal, () =>\r\n      abortControllerWithReason(controller, parentAbortSignal.reason)\r\n    )\r\n\r\n  return <T>(\r\n    taskExecutor: ForkedTaskExecutor<T>,\r\n    opts?: ForkOptions\r\n  ): ForkedTask<T> => {\r\n    assertFunction(taskExecutor, 'taskExecutor')\r\n    const childAbortController = new AbortController()\r\n\r\n    linkControllers(childAbortController)\r\n\r\n    const result = runTask<T>(\r\n      async (): Promise<T> => {\r\n        validateActive(parentAbortSignal)\r\n        validateActive(childAbortController.signal)\r\n        const result = (await taskExecutor({\r\n          pause: createPause(childAbortController.signal),\r\n          delay: createDelay(childAbortController.signal),\r\n          signal: childAbortController.signal,\r\n        })) as T\r\n        validateActive(childAbortController.signal)\r\n        return result\r\n      },\r\n      () => abortControllerWithReason(childAbortController, taskCompleted)\r\n    )\r\n\r\n    if (opts?.autoJoin) {\r\n      parentBlockingPromises.push(result)\r\n    }\r\n\r\n    return {\r\n      result: createPause<TaskResult<T>>(parentAbortSignal)(result),\r\n      cancel() {\r\n        abortControllerWithReason(childAbortController, taskCancelled)\r\n      },\r\n    }\r\n  }\r\n}\r\n\r\nconst createTakePattern = <S>(\r\n  startListening: AddListenerOverloads<\r\n    UnsubscribeListener,\r\n    S,\r\n    Dispatch<AnyAction>\r\n  >,\r\n  signal: AbortSignal\r\n): TakePattern<S> => {\r\n  /**\r\n   * A function that takes a ListenerPredicate and an optional timeout,\r\n   * and resolves when either the predicate returns `true` based on an action\r\n   * state combination or when the timeout expires.\r\n   * If the parent listener is canceled while waiting, this will throw a\r\n   * TaskAbortError.\r\n   */\r\n  const take = async <P extends AnyListenerPredicate<S>>(\r\n    predicate: P,\r\n    timeout: number | undefined\r\n  ) => {\r\n    validateActive(signal)\r\n\r\n    // Placeholder unsubscribe function until the listener is added\r\n    let unsubscribe: UnsubscribeListener = () => {}\r\n\r\n    const tuplePromise = new Promise<[AnyAction, S, S]>((resolve, reject) => {\r\n      // Inside the Promise, we synchronously add the listener.\r\n      let stopListening = startListening({\r\n        predicate: predicate as any,\r\n        effect: (action, listenerApi): void => {\r\n          // One-shot listener that cleans up as soon as the predicate passes\r\n          listenerApi.unsubscribe()\r\n          // Resolve the promise with the same arguments the predicate saw\r\n          resolve([\r\n            action,\r\n            listenerApi.getState(),\r\n            listenerApi.getOriginalState(),\r\n          ])\r\n        },\r\n      })\r\n      unsubscribe = () => {\r\n        stopListening()\r\n        reject()\r\n      }\r\n    })\r\n\r\n    const promises: (Promise<null> | Promise<[AnyAction, S, S]>)[] = [\r\n      tuplePromise,\r\n    ]\r\n\r\n    if (timeout != null) {\r\n      promises.push(\r\n        new Promise<null>((resolve) => setTimeout(resolve, timeout, null))\r\n      )\r\n    }\r\n\r\n    try {\r\n      const output = await raceWithSignal(signal, Promise.race(promises))\r\n\r\n      validateActive(signal)\r\n      return output\r\n    } finally {\r\n      // Always clean up the listener\r\n      unsubscribe()\r\n    }\r\n  }\r\n\r\n  return ((predicate: AnyListenerPredicate<S>, timeout: number | undefined) =>\r\n    catchRejection(take(predicate, timeout))) as TakePattern<S>\r\n}\r\n\r\nconst getListenerEntryPropsFrom = (options: FallbackAddListenerOptions) => {\r\n  let { type, actionCreator, matcher, predicate, effect } = options\r\n\r\n  if (type) {\r\n    predicate = createAction(type).match\r\n  } else if (actionCreator) {\r\n    type = actionCreator!.type\r\n    predicate = actionCreator.match\r\n  } else if (matcher) {\r\n    predicate = matcher\r\n  } else if (predicate) {\r\n    // pass\r\n  } else {\r\n    throw new Error(\r\n      'Creating or removing a listener requires one of the known fields for matching an action'\r\n    )\r\n  }\r\n\r\n  assertFunction(effect, 'options.listener')\r\n\r\n  return { predicate, type, effect }\r\n}\r\n\r\n/** Accepts the possible options for creating a listener, and returns a formatted listener entry */\r\nexport const createListenerEntry: TypedCreateListenerEntry<unknown> = (\r\n  options: FallbackAddListenerOptions\r\n) => {\r\n  const { type, predicate, effect } = getListenerEntryPropsFrom(options)\r\n\r\n  const id = nanoid()\r\n  const entry: ListenerEntry<unknown> = {\r\n    id,\r\n    effect,\r\n    type,\r\n    predicate,\r\n    pending: new Set<AbortController>(),\r\n    unsubscribe: () => {\r\n      throw new Error('Unsubscribe not initialized')\r\n    },\r\n  }\r\n\r\n  return entry\r\n}\r\n\r\nconst cancelActiveListeners = (\r\n  entry: ListenerEntry<unknown, Dispatch<AnyAction>>\r\n) => {\r\n  entry.pending.forEach((controller) => {\r\n    abortControllerWithReason(controller, listenerCancelled)\r\n  })\r\n}\r\n\r\nconst createClearListenerMiddleware = (\r\n  listenerMap: Map<string, ListenerEntry>\r\n) => {\r\n  return () => {\r\n    listenerMap.forEach(cancelActiveListeners)\r\n\r\n    listenerMap.clear()\r\n  }\r\n}\r\n\r\n/**\r\n * Safely reports errors to the `errorHandler` provided.\r\n * Errors that occur inside `errorHandler` are notified in a new task.\r\n * Inspired by [rxjs reportUnhandledError](https://github.com/ReactiveX/rxjs/blob/6fafcf53dc9e557439b25debaeadfd224b245a66/src/internal/util/reportUnhandledError.ts)\r\n * @param errorHandler\r\n * @param errorToNotify\r\n */\r\nconst safelyNotifyError = (\r\n  errorHandler: ListenerErrorHandler,\r\n  errorToNotify: unknown,\r\n  errorInfo: ListenerErrorInfo\r\n): void => {\r\n  try {\r\n    errorHandler(errorToNotify, errorInfo)\r\n  } catch (errorHandlerError) {\r\n    // We cannot let an error raised here block the listener queue.\r\n    // The error raised here will be picked up by `window.onerror`, `process.on('error')` etc...\r\n    setTimeout(() => {\r\n      throw errorHandlerError\r\n    }, 0)\r\n  }\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport const addListener = createAction(\r\n  `${alm}/add`\r\n) as TypedAddListener<unknown>\r\n\r\n/**\r\n * @public\r\n */\r\nexport const clearAllListeners = createAction(`${alm}/removeAll`)\r\n\r\n/**\r\n * @public\r\n */\r\nexport const removeListener = createAction(\r\n  `${alm}/remove`\r\n) as TypedRemoveListener<unknown>\r\n\r\nconst defaultErrorHandler: ListenerErrorHandler = (...args: unknown[]) => {\r\n  console.error(`${alm}/error`, ...args)\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport function createListenerMiddleware<\r\n  S = unknown,\r\n  D extends Dispatch<AnyAction> = ThunkDispatch<S, unknown, AnyAction>,\r\n  ExtraArgument = unknown\r\n>(middlewareOptions: CreateListenerMiddlewareOptions<ExtraArgument> = {}) {\r\n  const listenerMap = new Map<string, ListenerEntry>()\r\n  const { extra, onError = defaultErrorHandler } = middlewareOptions\r\n\r\n  assertFunction(onError, 'onError')\r\n\r\n  const insertEntry = (entry: ListenerEntry) => {\r\n    entry.unsubscribe = () => listenerMap.delete(entry!.id)\r\n\r\n    listenerMap.set(entry.id, entry)\r\n    return (cancelOptions?: UnsubscribeListenerOptions) => {\r\n      entry.unsubscribe()\r\n      if (cancelOptions?.cancelActive) {\r\n        cancelActiveListeners(entry)\r\n      }\r\n    }\r\n  }\r\n\r\n  const findListenerEntry = (\r\n    comparator: (entry: ListenerEntry) => boolean\r\n  ): ListenerEntry | undefined => {\r\n    for (const entry of Array.from(listenerMap.values())) {\r\n      if (comparator(entry)) {\r\n        return entry\r\n      }\r\n    }\r\n\r\n    return undefined\r\n  }\r\n\r\n  const startListening = (options: FallbackAddListenerOptions) => {\r\n    let entry = findListenerEntry(\r\n      (existingEntry) => existingEntry.effect === options.effect\r\n    )\r\n\r\n    if (!entry) {\r\n      entry = createListenerEntry(options as any)\r\n    }\r\n\r\n    return insertEntry(entry)\r\n  }\r\n\r\n  const stopListening = (\r\n    options: FallbackAddListenerOptions & UnsubscribeListenerOptions\r\n  ): boolean => {\r\n    const { type, effect, predicate } = getListenerEntryPropsFrom(options)\r\n\r\n    const entry = findListenerEntry((entry) => {\r\n      const matchPredicateOrType =\r\n        typeof type === 'string'\r\n          ? entry.type === type\r\n          : entry.predicate === predicate\r\n\r\n      return matchPredicateOrType && entry.effect === effect\r\n    })\r\n\r\n    if (entry) {\r\n      entry.unsubscribe()\r\n      if (options.cancelActive) {\r\n        cancelActiveListeners(entry)\r\n      }\r\n    }\r\n\r\n    return !!entry\r\n  }\r\n\r\n  const notifyListener = async (\r\n    entry: ListenerEntry<unknown, Dispatch<AnyAction>>,\r\n    action: AnyAction,\r\n    api: MiddlewareAPI,\r\n    getOriginalState: () => S\r\n  ) => {\r\n    const internalTaskController = new AbortController()\r\n    const take = createTakePattern(\r\n      startListening,\r\n      internalTaskController.signal\r\n    )\r\n    const autoJoinPromises: Promise<any>[] = []\r\n\r\n    try {\r\n      entry.pending.add(internalTaskController)\r\n      await Promise.resolve(\r\n        entry.effect(\r\n          action,\r\n          // Use assign() rather than ... to avoid extra helper functions added to bundle\r\n          assign({}, api, {\r\n            getOriginalState,\r\n            condition: (\r\n              predicate: AnyListenerPredicate<any>,\r\n              timeout?: number\r\n            ) => take(predicate, timeout).then(Boolean),\r\n            take,\r\n            delay: createDelay(internalTaskController.signal),\r\n            pause: createPause<any>(internalTaskController.signal),\r\n            extra,\r\n            signal: internalTaskController.signal,\r\n            fork: createFork(internalTaskController.signal, autoJoinPromises),\r\n            unsubscribe: entry.unsubscribe,\r\n            subscribe: () => {\r\n              listenerMap.set(entry.id, entry)\r\n            },\r\n            cancelActiveListeners: () => {\r\n              entry.pending.forEach((controller, _, set) => {\r\n                if (controller !== internalTaskController) {\r\n                  abortControllerWithReason(controller, listenerCancelled)\r\n                  set.delete(controller)\r\n                }\r\n              })\r\n            },\r\n          })\r\n        )\r\n      )\r\n    } catch (listenerError) {\r\n      if (!(listenerError instanceof TaskAbortError)) {\r\n        safelyNotifyError(onError, listenerError, {\r\n          raisedBy: 'effect',\r\n        })\r\n      }\r\n    } finally {\r\n      await Promise.allSettled(autoJoinPromises)\r\n\r\n      abortControllerWithReason(internalTaskController, listenerCompleted) // Notify that the task has completed\r\n      entry.pending.delete(internalTaskController)\r\n    }\r\n  }\r\n\r\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap)\r\n\r\n  const middleware: ListenerMiddleware<S, D, ExtraArgument> =\r\n    (api) => (next) => (action) => {\r\n      if (!isAction(action)) {\r\n        // we only want to notify listeners for action objects\r\n        return next(action)\r\n      }\r\n\r\n      if (addListener.match(action)) {\r\n        return startListening(action.payload)\r\n      }\r\n\r\n      if (clearAllListeners.match(action)) {\r\n        clearListenerMiddleware()\r\n        return\r\n      }\r\n\r\n      if (removeListener.match(action)) {\r\n        return stopListening(action.payload)\r\n      }\r\n\r\n      // Need to get this state _before_ the reducer processes the action\r\n      let originalState: S | typeof INTERNAL_NIL_TOKEN = api.getState()\r\n\r\n      // `getOriginalState` can only be called synchronously.\r\n      // @see https://github.com/reduxjs/redux-toolkit/discussions/1648#discussioncomment-1932820\r\n      const getOriginalState = (): S => {\r\n        if (originalState === INTERNAL_NIL_TOKEN) {\r\n          throw new Error(\r\n            `${alm}: getOriginalState can only be called synchronously`\r\n          )\r\n        }\r\n\r\n        return originalState as S\r\n      }\r\n\r\n      let result: unknown\r\n\r\n      try {\r\n        // Actually forward the action to the reducer before we handle listeners\r\n        result = next(action)\r\n\r\n        if (listenerMap.size > 0) {\r\n          let currentState = api.getState()\r\n          // Work around ESBuild+TS transpilation issue\r\n          const listenerEntries = Array.from(listenerMap.values())\r\n          for (let entry of listenerEntries) {\r\n            let runListener = false\r\n\r\n            try {\r\n              runListener = entry.predicate(action, currentState, originalState)\r\n            } catch (predicateError) {\r\n              runListener = false\r\n\r\n              safelyNotifyError(onError, predicateError, {\r\n                raisedBy: 'predicate',\r\n              })\r\n            }\r\n\r\n            if (!runListener) {\r\n              continue\r\n            }\r\n\r\n            notifyListener(entry, action, api, getOriginalState)\r\n          }\r\n        }\r\n      } finally {\r\n        // Remove `originalState` store from this scope.\r\n        originalState = INTERNAL_NIL_TOKEN\r\n      }\r\n\r\n      return result\r\n    }\r\n\r\n  return {\r\n    middleware,\r\n    startListening,\r\n    stopListening,\r\n    clearListeners: clearListenerMiddleware,\r\n  } as ListenerMiddlewareInstance<S, D, ExtraArgument>\r\n}\r\n", "import type { StoreEnhancer } from 'redux'\r\n\r\nexport const SHOULD_AUTOBATCH = 'RTK_autoBatch'\r\n\r\nexport const prepareAutoBatched =\r\n  <T>() =>\r\n  (payload: T): { payload: T; meta: unknown } => ({\r\n    payload,\r\n    meta: { [SHOULD_AUTOBATCH]: true },\r\n  })\r\n\r\n// TODO Remove this in 2.0\r\n// Copied from https://github.com/feross/queue-microtask\r\nlet promise: Promise<any>\r\nconst queueMicrotaskShim =\r\n  typeof queueMicrotask === 'function'\r\n    ? queueMicrotask.bind(\r\n        typeof window !== 'undefined'\r\n          ? window\r\n          : typeof global !== 'undefined'\r\n          ? global\r\n          : globalThis\r\n      )\r\n    : // reuse resolved promise, and allocate it lazily\r\n      (cb: () => void) =>\r\n        (promise || (promise = Promise.resolve())).then(cb).catch((err: any) =>\r\n          setTimeout(() => {\r\n            throw err\r\n          }, 0)\r\n        )\r\n\r\nconst createQueueWithTimer = (timeout: number) => {\r\n  return (notify: () => void) => {\r\n    setTimeout(notify, timeout)\r\n  }\r\n}\r\n\r\n// requestAnimationFrame won't exist in SSR environments.\r\n// Fall back to a vague approximation just to keep from erroring.\r\nconst rAF =\r\n  typeof window !== 'undefined' && window.requestAnimationFrame\r\n    ? window.requestAnimationFrame\r\n    : createQueueWithTimer(10)\r\n\r\nexport type AutoBatchOptions =\r\n  | { type: 'tick' }\r\n  | { type: 'timer'; timeout: number }\r\n  | { type: 'raf' }\r\n  | { type: 'callback'; queueNotification: (notify: () => void) => void }\r\n\r\n/**\r\n * A Redux store enhancer that watches for \"low-priority\" actions, and delays\r\n * notifying subscribers until either the queued callback executes or the\r\n * next \"standard-priority\" action is dispatched.\r\n *\r\n * This allows dispatching multiple \"low-priority\" actions in a row with only\r\n * a single subscriber notification to the UI after the sequence of actions\r\n * is finished, thus improving UI re-render performance.\r\n *\r\n * Watches for actions with the `action.meta[SHOULD_AUTOBATCH]` attribute.\r\n * This can be added to `action.meta` manually, or by using the\r\n * `prepareAutoBatched` helper.\r\n *\r\n * By default, it will queue a notification for the end of the event loop tick.\r\n * However, you can pass several other options to configure the behavior:\r\n * - `{type: 'tick'}: queues using `queueMicrotask` (default)\r\n * - `{type: 'timer, timeout: number}`: queues using `setTimeout`\r\n * - `{type: 'raf'}`: queues using `requestAnimationFrame`\r\n * - `{type: 'callback', queueNotification: (notify: () => void) => void}: lets you provide your own callback\r\n *\r\n *\r\n */\r\nexport const autoBatchEnhancer =\r\n  (options: AutoBatchOptions = { type: 'raf' }): StoreEnhancer =>\r\n  (next) =>\r\n  (...args) => {\r\n    const store = next(...args)\r\n\r\n    let notifying = true\r\n    let shouldNotifyAtEndOfTick = false\r\n    let notificationQueued = false\r\n\r\n    const listeners = new Set<() => void>()\r\n\r\n    const queueCallback =\r\n      options.type === 'tick'\r\n        ? queueMicrotaskShim\r\n        : options.type === 'raf'\r\n        ? rAF\r\n        : options.type === 'callback'\r\n        ? options.queueNotification\r\n        : createQueueWithTimer(options.timeout)\r\n\r\n    const notifyListeners = () => {\r\n      // We're running at the end of the event loop tick.\r\n      // Run the real listener callbacks to actually update the UI.\r\n      notificationQueued = false\r\n      if (shouldNotifyAtEndOfTick) {\r\n        shouldNotifyAtEndOfTick = false\r\n        listeners.forEach((l) => l())\r\n      }\r\n    }\r\n\r\n    return Object.assign({}, store, {\r\n      // Override the base `store.subscribe` method to keep original listeners\r\n      // from running if we're delaying notifications\r\n      subscribe(listener: () => void) {\r\n        // Each wrapped listener will only call the real listener if\r\n        // the `notifying` flag is currently active when it's called.\r\n        // This lets the base store work as normal, while the actual UI\r\n        // update becomes controlled by this enhancer.\r\n        const wrappedListener: typeof listener = () => notifying && listener()\r\n        const unsubscribe = store.subscribe(wrappedListener)\r\n        listeners.add(listener)\r\n        return () => {\r\n          unsubscribe()\r\n          listeners.delete(listener)\r\n        }\r\n      },\r\n      // Override the base `store.dispatch` method so that we can check actions\r\n      // for the `shouldAutoBatch` flag and determine if batching is active\r\n      dispatch(action: any) {\r\n        try {\r\n          // If the action does _not_ have the `shouldAutoBatch` flag,\r\n          // we resume/continue normal notify-after-each-dispatch behavior\r\n          notifying = !action?.meta?.[SHOULD_AUTOBATCH]\r\n          // If a `notifyListeners` microtask was queued, you can't cancel it.\r\n          // Instead, we set a flag so that it's a no-op when it does run\r\n          shouldNotifyAtEndOfTick = !notifying\r\n          if (shouldNotifyAtEndOfTick) {\r\n            // We've seen at least 1 action with `SHOULD_AUTOBATCH`. Try to queue\r\n            // a microtask to notify listeners at the end of the event loop tick.\r\n            // Make sure we only enqueue this _once_ per tick.\r\n            if (!notificationQueued) {\r\n              notificationQueued = true\r\n              queueCallback(notifyListeners)\r\n            }\r\n          }\r\n          // Go ahead and process the action as usual, including reducers.\r\n          // If normal notification behavior is enabled, the store will notify\r\n          // all of its own listeners, and the wrapper callbacks above will\r\n          // see `notifying` is true and pass on to the real listener callbacks.\r\n          // If we're \"batching\" behavior, then the wrapped callbacks will\r\n          // bail out, causing the base store notification behavior to be no-ops.\r\n          return store.dispatch(action)\r\n        } finally {\r\n          // Assume we're back to normal behavior after each action\r\n          notifying = true\r\n        }\r\n      },\r\n    })\r\n  }\r\n", "var __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = function (d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n    return function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nvar __defProp = Object.defineProperty;\r\nvar __defProps = Object.defineProperties;\r\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\r\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\r\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\r\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\r\nvar __defNormalProp = function (obj, key, value) { return key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value: value }) : obj[key] = value; };\r\nvar __spreadValues = function (a, b) {\r\n    for (var prop in b || (b = {}))\r\n        if (__hasOwnProp.call(b, prop))\r\n            __defNormalProp(a, prop, b[prop]);\r\n    if (__getOwnPropSymbols)\r\n        for (var _i = 0, _c = __getOwnPropSymbols(b); _i < _c.length; _i++) {\r\n            var prop = _c[_i];\r\n            if (__propIsEnum.call(b, prop))\r\n                __defNormalProp(a, prop, b[prop]);\r\n        }\r\n    return a;\r\n};\r\nvar __spreadProps = function (a, b) { return __defProps(a, __getOwnPropDescs(b)); };\r\nvar __async = function (__this, __arguments, generator) {\r\n    return new Promise(function (resolve, reject) {\r\n        var fulfilled = function (value) {\r\n            try {\r\n                step(generator.next(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var rejected = function (value) {\r\n            try {\r\n                step(generator.throw(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var step = function (x) { return x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected); };\r\n        step((generator = generator.apply(__this, __arguments)).next());\r\n    });\r\n};\r\n// src/index.ts\r\nimport { enableES5 } from \"immer\";\r\nexport * from \"redux\";\r\nimport { default as default2, current as current2, freeze, original, isDraft as isDraft4 } from \"immer\";\r\nimport { createSelector as createSelector2 } from \"reselect\";\r\n// src/createDraftSafeSelector.ts\r\nimport { current, isDraft } from \"immer\";\r\nimport { createSelector } from \"reselect\";\r\nvar createDraftSafeSelector = function () {\r\n    var args = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        args[_i] = arguments[_i];\r\n    }\r\n    var selector = createSelector.apply(void 0, args);\r\n    var wrappedSelector = function (value) {\r\n        var rest = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            rest[_i - 1] = arguments[_i];\r\n        }\r\n        return selector.apply(void 0, __spreadArray([isDraft(value) ? current(value) : value], rest));\r\n    };\r\n    return wrappedSelector;\r\n};\r\n// src/configureStore.ts\r\nimport { createStore, compose as compose2, applyMiddleware, combineReducers } from \"redux\";\r\n// src/devtoolsExtension.ts\r\nimport { compose } from \"redux\";\r\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\r\n    if (arguments.length === 0)\r\n        return void 0;\r\n    if (typeof arguments[0] === \"object\")\r\n        return compose;\r\n    return compose.apply(null, arguments);\r\n};\r\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function () {\r\n    return function (noop2) {\r\n        return noop2;\r\n    };\r\n};\r\n// src/isPlainObject.ts\r\nfunction isPlainObject(value) {\r\n    if (typeof value !== \"object\" || value === null)\r\n        return false;\r\n    var proto = Object.getPrototypeOf(value);\r\n    if (proto === null)\r\n        return true;\r\n    var baseProto = proto;\r\n    while (Object.getPrototypeOf(baseProto) !== null) {\r\n        baseProto = Object.getPrototypeOf(baseProto);\r\n    }\r\n    return proto === baseProto;\r\n}\r\n// src/getDefaultMiddleware.ts\r\nimport thunkMiddleware from \"redux-thunk\";\r\n// src/tsHelpers.ts\r\nvar hasMatchFunction = function (v) {\r\n    return v && typeof v.match === \"function\";\r\n};\r\n// src/createAction.ts\r\nfunction createAction(type, prepareAction) {\r\n    function actionCreator() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        if (prepareAction) {\r\n            var prepared = prepareAction.apply(void 0, args);\r\n            if (!prepared) {\r\n                throw new Error(\"prepareAction did not return an object\");\r\n            }\r\n            return __spreadValues(__spreadValues({\r\n                type: type,\r\n                payload: prepared.payload\r\n            }, \"meta\" in prepared && { meta: prepared.meta }), \"error\" in prepared && { error: prepared.error });\r\n        }\r\n        return { type: type, payload: args[0] };\r\n    }\r\n    actionCreator.toString = function () { return \"\" + type; };\r\n    actionCreator.type = type;\r\n    actionCreator.match = function (action) { return action.type === type; };\r\n    return actionCreator;\r\n}\r\nfunction isAction(action) {\r\n    return isPlainObject(action) && \"type\" in action;\r\n}\r\nfunction isActionCreator(action) {\r\n    return typeof action === \"function\" && \"type\" in action && hasMatchFunction(action);\r\n}\r\nfunction isFSA(action) {\r\n    return isAction(action) && typeof action.type === \"string\" && Object.keys(action).every(isValidKey);\r\n}\r\nfunction isValidKey(key) {\r\n    return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\r\n}\r\nfunction getType(actionCreator) {\r\n    return \"\" + actionCreator;\r\n}\r\n// src/actionCreatorInvariantMiddleware.ts\r\nfunction getMessage(type) {\r\n    var splitType = type ? (\"\" + type).split(\"/\") : [];\r\n    var actionName = splitType[splitType.length - 1] || \"actionCreator\";\r\n    return \"Detected an action creator with type \\\"\" + (type || \"unknown\") + \"\\\" being dispatched. \\nMake sure you're calling the action creator before dispatching, i.e. `dispatch(\" + actionName + \"())` instead of `dispatch(\" + actionName + \")`. This is necessary even if the action has no payload.\";\r\n}\r\nfunction createActionCreatorInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (process.env.NODE_ENV === \"production\") {\r\n        return function () { return function (next) { return function (action) { return next(action); }; }; };\r\n    }\r\n    var _c = options.isActionCreator, isActionCreator2 = _c === void 0 ? isActionCreator : _c;\r\n    return function () { return function (next) { return function (action) {\r\n        if (isActionCreator2(action)) {\r\n            console.warn(getMessage(action.type));\r\n        }\r\n        return next(action);\r\n    }; }; };\r\n}\r\n// src/utils.ts\r\nimport createNextState, { isDraftable } from \"immer\";\r\nfunction getTimeMeasureUtils(maxDelay, fnName) {\r\n    var elapsed = 0;\r\n    return {\r\n        measureTime: function (fn) {\r\n            var started = Date.now();\r\n            try {\r\n                return fn();\r\n            }\r\n            finally {\r\n                var finished = Date.now();\r\n                elapsed += finished - started;\r\n            }\r\n        },\r\n        warnIfExceeded: function () {\r\n            if (elapsed > maxDelay) {\r\n                console.warn(fnName + \" took \" + elapsed + \"ms, which is more than the warning threshold of \" + maxDelay + \"ms. \\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\\nIt is disabled in production builds, so you don't need to worry about that.\");\r\n            }\r\n        }\r\n    };\r\n}\r\nvar MiddlewareArray = /** @class */ (function (_super) {\r\n    __extends(MiddlewareArray, _super);\r\n    function MiddlewareArray() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var _this = _super.apply(this, args) || this;\r\n        Object.setPrototypeOf(_this, MiddlewareArray.prototype);\r\n        return _this;\r\n    }\r\n    Object.defineProperty(MiddlewareArray, Symbol.species, {\r\n        get: function () {\r\n            return MiddlewareArray;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    MiddlewareArray.prototype.concat = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        return _super.prototype.concat.apply(this, arr);\r\n    };\r\n    MiddlewareArray.prototype.prepend = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        if (arr.length === 1 && Array.isArray(arr[0])) {\r\n            return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr[0].concat(this))))();\r\n        }\r\n        return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr.concat(this))))();\r\n    };\r\n    return MiddlewareArray;\r\n}(Array));\r\nvar EnhancerArray = /** @class */ (function (_super) {\r\n    __extends(EnhancerArray, _super);\r\n    function EnhancerArray() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var _this = _super.apply(this, args) || this;\r\n        Object.setPrototypeOf(_this, EnhancerArray.prototype);\r\n        return _this;\r\n    }\r\n    Object.defineProperty(EnhancerArray, Symbol.species, {\r\n        get: function () {\r\n            return EnhancerArray;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    EnhancerArray.prototype.concat = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        return _super.prototype.concat.apply(this, arr);\r\n    };\r\n    EnhancerArray.prototype.prepend = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        if (arr.length === 1 && Array.isArray(arr[0])) {\r\n            return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr[0].concat(this))))();\r\n        }\r\n        return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr.concat(this))))();\r\n    };\r\n    return EnhancerArray;\r\n}(Array));\r\nfunction freezeDraftable(val) {\r\n    return isDraftable(val) ? createNextState(val, function () {\r\n    }) : val;\r\n}\r\n// src/immutableStateInvariantMiddleware.ts\r\nvar isProduction = process.env.NODE_ENV === \"production\";\r\nvar prefix = \"Invariant failed\";\r\nfunction invariant(condition, message) {\r\n    if (condition) {\r\n        return;\r\n    }\r\n    if (isProduction) {\r\n        throw new Error(prefix);\r\n    }\r\n    throw new Error(prefix + \": \" + (message || \"\"));\r\n}\r\nfunction stringify(obj, serializer, indent, decycler) {\r\n    return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\r\n}\r\nfunction getSerialize(serializer, decycler) {\r\n    var stack = [], keys = [];\r\n    if (!decycler)\r\n        decycler = function (_, value) {\r\n            if (stack[0] === value)\r\n                return \"[Circular ~]\";\r\n            return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\r\n        };\r\n    return function (key, value) {\r\n        if (stack.length > 0) {\r\n            var thisPos = stack.indexOf(this);\r\n            ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\r\n            ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\r\n            if (~stack.indexOf(value))\r\n                value = decycler.call(this, key, value);\r\n        }\r\n        else\r\n            stack.push(value);\r\n        return serializer == null ? value : serializer.call(this, key, value);\r\n    };\r\n}\r\nfunction isImmutableDefault(value) {\r\n    return typeof value !== \"object\" || value == null || Object.isFrozen(value);\r\n}\r\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\r\n    var trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\r\n    return {\r\n        detectMutations: function () {\r\n            return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\r\n        }\r\n    };\r\n}\r\nfunction trackProperties(isImmutable, ignorePaths, obj, path, checkedObjects) {\r\n    if (ignorePaths === void 0) { ignorePaths = []; }\r\n    if (path === void 0) { path = \"\"; }\r\n    if (checkedObjects === void 0) { checkedObjects = new Set(); }\r\n    var tracked = { value: obj };\r\n    if (!isImmutable(obj) && !checkedObjects.has(obj)) {\r\n        checkedObjects.add(obj);\r\n        tracked.children = {};\r\n        for (var key in obj) {\r\n            var childPath = path ? path + \".\" + key : key;\r\n            if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\r\n                continue;\r\n            }\r\n            tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\r\n        }\r\n    }\r\n    return tracked;\r\n}\r\nfunction detectMutations(isImmutable, ignoredPaths, trackedProperty, obj, sameParentRef, path) {\r\n    if (ignoredPaths === void 0) { ignoredPaths = []; }\r\n    if (sameParentRef === void 0) { sameParentRef = false; }\r\n    if (path === void 0) { path = \"\"; }\r\n    var prevObj = trackedProperty ? trackedProperty.value : void 0;\r\n    var sameRef = prevObj === obj;\r\n    if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\r\n        return { wasMutated: true, path: path };\r\n    }\r\n    if (isImmutable(prevObj) || isImmutable(obj)) {\r\n        return { wasMutated: false };\r\n    }\r\n    var keysToDetect = {};\r\n    for (var key in trackedProperty.children) {\r\n        keysToDetect[key] = true;\r\n    }\r\n    for (var key in obj) {\r\n        keysToDetect[key] = true;\r\n    }\r\n    var hasIgnoredPaths = ignoredPaths.length > 0;\r\n    var _loop_1 = function (key) {\r\n        var nestedPath = path ? path + \".\" + key : key;\r\n        if (hasIgnoredPaths) {\r\n            var hasMatches = ignoredPaths.some(function (ignored) {\r\n                if (ignored instanceof RegExp) {\r\n                    return ignored.test(nestedPath);\r\n                }\r\n                return nestedPath === ignored;\r\n            });\r\n            if (hasMatches) {\r\n                return \"continue\";\r\n            }\r\n        }\r\n        var result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\r\n        if (result.wasMutated) {\r\n            return { value: result };\r\n        }\r\n    };\r\n    for (var key in keysToDetect) {\r\n        var state_1 = _loop_1(key);\r\n        if (typeof state_1 === \"object\")\r\n            return state_1.value;\r\n    }\r\n    return { wasMutated: false };\r\n}\r\nfunction createImmutableStateInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (process.env.NODE_ENV === \"production\") {\r\n        return function () { return function (next) { return function (action) { return next(action); }; }; };\r\n    }\r\n    var _c = options.isImmutable, isImmutable = _c === void 0 ? isImmutableDefault : _c, ignoredPaths = options.ignoredPaths, _d = options.warnAfter, warnAfter = _d === void 0 ? 32 : _d, ignore = options.ignore;\r\n    ignoredPaths = ignoredPaths || ignore;\r\n    var track = trackForMutations.bind(null, isImmutable, ignoredPaths);\r\n    return function (_c) {\r\n        var getState = _c.getState;\r\n        var state = getState();\r\n        var tracker = track(state);\r\n        var result;\r\n        return function (next) { return function (action) {\r\n            var measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\r\n            measureUtils.measureTime(function () {\r\n                state = getState();\r\n                result = tracker.detectMutations();\r\n                tracker = track(state);\r\n                invariant(!result.wasMutated, \"A state mutation was detected between dispatches, in the path '\" + (result.path || \"\") + \"'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\r\n            });\r\n            var dispatchedAction = next(action);\r\n            measureUtils.measureTime(function () {\r\n                state = getState();\r\n                result = tracker.detectMutations();\r\n                tracker = track(state);\r\n                result.wasMutated && invariant(!result.wasMutated, \"A state mutation was detected inside a dispatch, in the path: \" + (result.path || \"\") + \". Take a look at the reducer(s) handling the action \" + stringify(action) + \". (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\r\n            });\r\n            measureUtils.warnIfExceeded();\r\n            return dispatchedAction;\r\n        }; };\r\n    };\r\n}\r\n// src/serializableStateInvariantMiddleware.ts\r\nfunction isPlain(val) {\r\n    var type = typeof val;\r\n    return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || isPlainObject(val);\r\n}\r\nfunction findNonSerializableValue(value, path, isSerializable, getEntries, ignoredPaths, cache) {\r\n    if (path === void 0) { path = \"\"; }\r\n    if (isSerializable === void 0) { isSerializable = isPlain; }\r\n    if (ignoredPaths === void 0) { ignoredPaths = []; }\r\n    var foundNestedSerializable;\r\n    if (!isSerializable(value)) {\r\n        return {\r\n            keyPath: path || \"<root>\",\r\n            value: value\r\n        };\r\n    }\r\n    if (typeof value !== \"object\" || value === null) {\r\n        return false;\r\n    }\r\n    if (cache == null ? void 0 : cache.has(value))\r\n        return false;\r\n    var entries = getEntries != null ? getEntries(value) : Object.entries(value);\r\n    var hasIgnoredPaths = ignoredPaths.length > 0;\r\n    var _loop_2 = function (key, nestedValue) {\r\n        var nestedPath = path ? path + \".\" + key : key;\r\n        if (hasIgnoredPaths) {\r\n            var hasMatches = ignoredPaths.some(function (ignored) {\r\n                if (ignored instanceof RegExp) {\r\n                    return ignored.test(nestedPath);\r\n                }\r\n                return nestedPath === ignored;\r\n            });\r\n            if (hasMatches) {\r\n                return \"continue\";\r\n            }\r\n        }\r\n        if (!isSerializable(nestedValue)) {\r\n            return { value: {\r\n                    keyPath: nestedPath,\r\n                    value: nestedValue\r\n                } };\r\n        }\r\n        if (typeof nestedValue === \"object\") {\r\n            foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\r\n            if (foundNestedSerializable) {\r\n                return { value: foundNestedSerializable };\r\n            }\r\n        }\r\n    };\r\n    for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\r\n        var _c = entries_1[_i], key = _c[0], nestedValue = _c[1];\r\n        var state_2 = _loop_2(key, nestedValue);\r\n        if (typeof state_2 === \"object\")\r\n            return state_2.value;\r\n    }\r\n    if (cache && isNestedFrozen(value))\r\n        cache.add(value);\r\n    return false;\r\n}\r\nfunction isNestedFrozen(value) {\r\n    if (!Object.isFrozen(value))\r\n        return false;\r\n    for (var _i = 0, _c = Object.values(value); _i < _c.length; _i++) {\r\n        var nestedValue = _c[_i];\r\n        if (typeof nestedValue !== \"object\" || nestedValue === null)\r\n            continue;\r\n        if (!isNestedFrozen(nestedValue))\r\n            return false;\r\n    }\r\n    return true;\r\n}\r\nfunction createSerializableStateInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (process.env.NODE_ENV === \"production\") {\r\n        return function () { return function (next) { return function (action) { return next(action); }; }; };\r\n    }\r\n    var _c = options.isSerializable, isSerializable = _c === void 0 ? isPlain : _c, getEntries = options.getEntries, _d = options.ignoredActions, ignoredActions = _d === void 0 ? [] : _d, _e = options.ignoredActionPaths, ignoredActionPaths = _e === void 0 ? [\"meta.arg\", \"meta.baseQueryMeta\"] : _e, _f = options.ignoredPaths, ignoredPaths = _f === void 0 ? [] : _f, _g = options.warnAfter, warnAfter = _g === void 0 ? 32 : _g, _h = options.ignoreState, ignoreState = _h === void 0 ? false : _h, _j = options.ignoreActions, ignoreActions = _j === void 0 ? false : _j, _k = options.disableCache, disableCache = _k === void 0 ? false : _k;\r\n    var cache = !disableCache && WeakSet ? new WeakSet() : void 0;\r\n    return function (storeAPI) { return function (next) { return function (action) {\r\n        var result = next(action);\r\n        var measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\r\n        if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\r\n            measureUtils.measureTime(function () {\r\n                var foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\r\n                if (foundActionNonSerializableValue) {\r\n                    var keyPath = foundActionNonSerializableValue.keyPath, value = foundActionNonSerializableValue.value;\r\n                    console.error(\"A non-serializable value was detected in an action, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\r\n                }\r\n            });\r\n        }\r\n        if (!ignoreState) {\r\n            measureUtils.measureTime(function () {\r\n                var state = storeAPI.getState();\r\n                var foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\r\n                if (foundStateNonSerializableValue) {\r\n                    var keyPath = foundStateNonSerializableValue.keyPath, value = foundStateNonSerializableValue.value;\r\n                    console.error(\"A non-serializable value was detected in the state, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the reducer(s) handling this action type: \" + action.type + \".\\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)\");\r\n                }\r\n            });\r\n            measureUtils.warnIfExceeded();\r\n        }\r\n        return result;\r\n    }; }; };\r\n}\r\n// src/getDefaultMiddleware.ts\r\nfunction isBoolean(x) {\r\n    return typeof x === \"boolean\";\r\n}\r\nfunction curryGetDefaultMiddleware() {\r\n    return function curriedGetDefaultMiddleware(options) {\r\n        return getDefaultMiddleware(options);\r\n    };\r\n}\r\nfunction getDefaultMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    var _c = options.thunk, thunk = _c === void 0 ? true : _c, _d = options.immutableCheck, immutableCheck = _d === void 0 ? true : _d, _e = options.serializableCheck, serializableCheck = _e === void 0 ? true : _e, _f = options.actionCreatorCheck, actionCreatorCheck = _f === void 0 ? true : _f;\r\n    var middlewareArray = new MiddlewareArray();\r\n    if (thunk) {\r\n        if (isBoolean(thunk)) {\r\n            middlewareArray.push(thunkMiddleware);\r\n        }\r\n        else {\r\n            middlewareArray.push(thunkMiddleware.withExtraArgument(thunk.extraArgument));\r\n        }\r\n    }\r\n    if (process.env.NODE_ENV !== \"production\") {\r\n        if (immutableCheck) {\r\n            var immutableOptions = {};\r\n            if (!isBoolean(immutableCheck)) {\r\n                immutableOptions = immutableCheck;\r\n            }\r\n            middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\r\n        }\r\n        if (serializableCheck) {\r\n            var serializableOptions = {};\r\n            if (!isBoolean(serializableCheck)) {\r\n                serializableOptions = serializableCheck;\r\n            }\r\n            middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\r\n        }\r\n        if (actionCreatorCheck) {\r\n            var actionCreatorOptions = {};\r\n            if (!isBoolean(actionCreatorCheck)) {\r\n                actionCreatorOptions = actionCreatorCheck;\r\n            }\r\n            middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\r\n        }\r\n    }\r\n    return middlewareArray;\r\n}\r\n// src/configureStore.ts\r\nvar IS_PRODUCTION = process.env.NODE_ENV === \"production\";\r\nfunction configureStore(options) {\r\n    var curriedGetDefaultMiddleware = curryGetDefaultMiddleware();\r\n    var _c = options || {}, _d = _c.reducer, reducer = _d === void 0 ? void 0 : _d, _e = _c.middleware, middleware = _e === void 0 ? curriedGetDefaultMiddleware() : _e, _f = _c.devTools, devTools = _f === void 0 ? true : _f, _g = _c.preloadedState, preloadedState = _g === void 0 ? void 0 : _g, _h = _c.enhancers, enhancers = _h === void 0 ? void 0 : _h;\r\n    var rootReducer;\r\n    if (typeof reducer === \"function\") {\r\n        rootReducer = reducer;\r\n    }\r\n    else if (isPlainObject(reducer)) {\r\n        rootReducer = combineReducers(reducer);\r\n    }\r\n    else {\r\n        throw new Error('\"reducer\" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\r\n    }\r\n    var finalMiddleware = middleware;\r\n    if (typeof finalMiddleware === \"function\") {\r\n        finalMiddleware = finalMiddleware(curriedGetDefaultMiddleware);\r\n        if (!IS_PRODUCTION && !Array.isArray(finalMiddleware)) {\r\n            throw new Error(\"when using a middleware builder function, an array of middleware must be returned\");\r\n        }\r\n    }\r\n    if (!IS_PRODUCTION && finalMiddleware.some(function (item) { return typeof item !== \"function\"; })) {\r\n        throw new Error(\"each middleware provided to configureStore must be a function\");\r\n    }\r\n    var middlewareEnhancer = applyMiddleware.apply(void 0, finalMiddleware);\r\n    var finalCompose = compose2;\r\n    if (devTools) {\r\n        finalCompose = composeWithDevTools(__spreadValues({\r\n            trace: !IS_PRODUCTION\r\n        }, typeof devTools === \"object\" && devTools));\r\n    }\r\n    var defaultEnhancers = new EnhancerArray(middlewareEnhancer);\r\n    var storeEnhancers = defaultEnhancers;\r\n    if (Array.isArray(enhancers)) {\r\n        storeEnhancers = __spreadArray([middlewareEnhancer], enhancers);\r\n    }\r\n    else if (typeof enhancers === \"function\") {\r\n        storeEnhancers = enhancers(defaultEnhancers);\r\n    }\r\n    var composedEnhancer = finalCompose.apply(void 0, storeEnhancers);\r\n    return createStore(rootReducer, preloadedState, composedEnhancer);\r\n}\r\n// src/createReducer.ts\r\nimport createNextState2, { isDraft as isDraft2, isDraftable as isDraftable2 } from \"immer\";\r\n// src/mapBuilders.ts\r\nfunction executeReducerBuilderCallback(builderCallback) {\r\n    var actionsMap = {};\r\n    var actionMatchers = [];\r\n    var defaultCaseReducer;\r\n    var builder = {\r\n        addCase: function (typeOrActionCreator, reducer) {\r\n            if (process.env.NODE_ENV !== \"production\") {\r\n                if (actionMatchers.length > 0) {\r\n                    throw new Error(\"`builder.addCase` should only be called before calling `builder.addMatcher`\");\r\n                }\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\r\n                }\r\n            }\r\n            var type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\r\n            if (!type) {\r\n                throw new Error(\"`builder.addCase` cannot be called with an empty action type\");\r\n            }\r\n            if (type in actionsMap) {\r\n                throw new Error(\"`builder.addCase` cannot be called with two reducers for the same action type\");\r\n            }\r\n            actionsMap[type] = reducer;\r\n            return builder;\r\n        },\r\n        addMatcher: function (matcher, reducer) {\r\n            if (process.env.NODE_ENV !== \"production\") {\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\r\n                }\r\n            }\r\n            actionMatchers.push({ matcher: matcher, reducer: reducer });\r\n            return builder;\r\n        },\r\n        addDefaultCase: function (reducer) {\r\n            if (process.env.NODE_ENV !== \"production\") {\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addDefaultCase` can only be called once\");\r\n                }\r\n            }\r\n            defaultCaseReducer = reducer;\r\n            return builder;\r\n        }\r\n    };\r\n    builderCallback(builder);\r\n    return [actionsMap, actionMatchers, defaultCaseReducer];\r\n}\r\n// src/createReducer.ts\r\nfunction isStateFunction(x) {\r\n    return typeof x === \"function\";\r\n}\r\nvar hasWarnedAboutObjectNotation = false;\r\nfunction createReducer(initialState, mapOrBuilderCallback, actionMatchers, defaultCaseReducer) {\r\n    if (actionMatchers === void 0) { actionMatchers = []; }\r\n    if (process.env.NODE_ENV !== \"production\") {\r\n        if (typeof mapOrBuilderCallback === \"object\") {\r\n            if (!hasWarnedAboutObjectNotation) {\r\n                hasWarnedAboutObjectNotation = true;\r\n                console.warn(\"The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\r\n            }\r\n        }\r\n    }\r\n    var _c = typeof mapOrBuilderCallback === \"function\" ? executeReducerBuilderCallback(mapOrBuilderCallback) : [mapOrBuilderCallback, actionMatchers, defaultCaseReducer], actionsMap = _c[0], finalActionMatchers = _c[1], finalDefaultCaseReducer = _c[2];\r\n    var getInitialState;\r\n    if (isStateFunction(initialState)) {\r\n        getInitialState = function () { return freezeDraftable(initialState()); };\r\n    }\r\n    else {\r\n        var frozenInitialState_1 = freezeDraftable(initialState);\r\n        getInitialState = function () { return frozenInitialState_1; };\r\n    }\r\n    function reducer(state, action) {\r\n        if (state === void 0) { state = getInitialState(); }\r\n        var caseReducers = __spreadArray([\r\n            actionsMap[action.type]\r\n        ], finalActionMatchers.filter(function (_c) {\r\n            var matcher = _c.matcher;\r\n            return matcher(action);\r\n        }).map(function (_c) {\r\n            var reducer2 = _c.reducer;\r\n            return reducer2;\r\n        }));\r\n        if (caseReducers.filter(function (cr) { return !!cr; }).length === 0) {\r\n            caseReducers = [finalDefaultCaseReducer];\r\n        }\r\n        return caseReducers.reduce(function (previousState, caseReducer) {\r\n            if (caseReducer) {\r\n                if (isDraft2(previousState)) {\r\n                    var draft = previousState;\r\n                    var result = caseReducer(draft, action);\r\n                    if (result === void 0) {\r\n                        return previousState;\r\n                    }\r\n                    return result;\r\n                }\r\n                else if (!isDraftable2(previousState)) {\r\n                    var result = caseReducer(previousState, action);\r\n                    if (result === void 0) {\r\n                        if (previousState === null) {\r\n                            return previousState;\r\n                        }\r\n                        throw Error(\"A case reducer on a non-draftable value must not return undefined\");\r\n                    }\r\n                    return result;\r\n                }\r\n                else {\r\n                    return createNextState2(previousState, function (draft) {\r\n                        return caseReducer(draft, action);\r\n                    });\r\n                }\r\n            }\r\n            return previousState;\r\n        }, state);\r\n    }\r\n    reducer.getInitialState = getInitialState;\r\n    return reducer;\r\n}\r\n// src/createSlice.ts\r\nvar hasWarnedAboutObjectNotation2 = false;\r\nfunction getType2(slice, actionKey) {\r\n    return slice + \"/\" + actionKey;\r\n}\r\nfunction createSlice(options) {\r\n    var name = options.name;\r\n    if (!name) {\r\n        throw new Error(\"`name` is a required option for createSlice\");\r\n    }\r\n    if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\r\n        if (options.initialState === void 0) {\r\n            console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\r\n        }\r\n    }\r\n    var initialState = typeof options.initialState == \"function\" ? options.initialState : freezeDraftable(options.initialState);\r\n    var reducers = options.reducers || {};\r\n    var reducerNames = Object.keys(reducers);\r\n    var sliceCaseReducersByName = {};\r\n    var sliceCaseReducersByType = {};\r\n    var actionCreators = {};\r\n    reducerNames.forEach(function (reducerName) {\r\n        var maybeReducerWithPrepare = reducers[reducerName];\r\n        var type = getType2(name, reducerName);\r\n        var caseReducer;\r\n        var prepareCallback;\r\n        if (\"reducer\" in maybeReducerWithPrepare) {\r\n            caseReducer = maybeReducerWithPrepare.reducer;\r\n            prepareCallback = maybeReducerWithPrepare.prepare;\r\n        }\r\n        else {\r\n            caseReducer = maybeReducerWithPrepare;\r\n        }\r\n        sliceCaseReducersByName[reducerName] = caseReducer;\r\n        sliceCaseReducersByType[type] = caseReducer;\r\n        actionCreators[reducerName] = prepareCallback ? createAction(type, prepareCallback) : createAction(type);\r\n    });\r\n    function buildReducer() {\r\n        if (process.env.NODE_ENV !== \"production\") {\r\n            if (typeof options.extraReducers === \"object\") {\r\n                if (!hasWarnedAboutObjectNotation2) {\r\n                    hasWarnedAboutObjectNotation2 = true;\r\n                    console.warn(\"The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\r\n                }\r\n            }\r\n        }\r\n        var _c = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers], _d = _c[0], extraReducers = _d === void 0 ? {} : _d, _e = _c[1], actionMatchers = _e === void 0 ? [] : _e, _f = _c[2], defaultCaseReducer = _f === void 0 ? void 0 : _f;\r\n        var finalCaseReducers = __spreadValues(__spreadValues({}, extraReducers), sliceCaseReducersByType);\r\n        return createReducer(initialState, function (builder) {\r\n            for (var key in finalCaseReducers) {\r\n                builder.addCase(key, finalCaseReducers[key]);\r\n            }\r\n            for (var _i = 0, actionMatchers_1 = actionMatchers; _i < actionMatchers_1.length; _i++) {\r\n                var m = actionMatchers_1[_i];\r\n                builder.addMatcher(m.matcher, m.reducer);\r\n            }\r\n            if (defaultCaseReducer) {\r\n                builder.addDefaultCase(defaultCaseReducer);\r\n            }\r\n        });\r\n    }\r\n    var _reducer;\r\n    return {\r\n        name: name,\r\n        reducer: function (state, action) {\r\n            if (!_reducer)\r\n                _reducer = buildReducer();\r\n            return _reducer(state, action);\r\n        },\r\n        actions: actionCreators,\r\n        caseReducers: sliceCaseReducersByName,\r\n        getInitialState: function () {\r\n            if (!_reducer)\r\n                _reducer = buildReducer();\r\n            return _reducer.getInitialState();\r\n        }\r\n    };\r\n}\r\n// src/entities/entity_state.ts\r\nfunction getInitialEntityState() {\r\n    return {\r\n        ids: [],\r\n        entities: {}\r\n    };\r\n}\r\nfunction createInitialStateFactory() {\r\n    function getInitialState(additionalState) {\r\n        if (additionalState === void 0) { additionalState = {}; }\r\n        return Object.assign(getInitialEntityState(), additionalState);\r\n    }\r\n    return { getInitialState: getInitialState };\r\n}\r\n// src/entities/state_selectors.ts\r\nfunction createSelectorsFactory() {\r\n    function getSelectors(selectState) {\r\n        var selectIds = function (state) { return state.ids; };\r\n        var selectEntities = function (state) { return state.entities; };\r\n        var selectAll = createDraftSafeSelector(selectIds, selectEntities, function (ids, entities) { return ids.map(function (id) { return entities[id]; }); });\r\n        var selectId = function (_, id) { return id; };\r\n        var selectById = function (entities, id) { return entities[id]; };\r\n        var selectTotal = createDraftSafeSelector(selectIds, function (ids) { return ids.length; });\r\n        if (!selectState) {\r\n            return {\r\n                selectIds: selectIds,\r\n                selectEntities: selectEntities,\r\n                selectAll: selectAll,\r\n                selectTotal: selectTotal,\r\n                selectById: createDraftSafeSelector(selectEntities, selectId, selectById)\r\n            };\r\n        }\r\n        var selectGlobalizedEntities = createDraftSafeSelector(selectState, selectEntities);\r\n        return {\r\n            selectIds: createDraftSafeSelector(selectState, selectIds),\r\n            selectEntities: selectGlobalizedEntities,\r\n            selectAll: createDraftSafeSelector(selectState, selectAll),\r\n            selectTotal: createDraftSafeSelector(selectState, selectTotal),\r\n            selectById: createDraftSafeSelector(selectGlobalizedEntities, selectId, selectById)\r\n        };\r\n    }\r\n    return { getSelectors: getSelectors };\r\n}\r\n// src/entities/state_adapter.ts\r\nimport createNextState3, { isDraft as isDraft3 } from \"immer\";\r\nfunction createSingleArgumentStateOperator(mutator) {\r\n    var operator = createStateOperator(function (_, state) { return mutator(state); });\r\n    return function operation(state) {\r\n        return operator(state, void 0);\r\n    };\r\n}\r\nfunction createStateOperator(mutator) {\r\n    return function operation(state, arg) {\r\n        function isPayloadActionArgument(arg2) {\r\n            return isFSA(arg2);\r\n        }\r\n        var runMutator = function (draft) {\r\n            if (isPayloadActionArgument(arg)) {\r\n                mutator(arg.payload, draft);\r\n            }\r\n            else {\r\n                mutator(arg, draft);\r\n            }\r\n        };\r\n        if (isDraft3(state)) {\r\n            runMutator(state);\r\n            return state;\r\n        }\r\n        else {\r\n            return createNextState3(state, runMutator);\r\n        }\r\n    };\r\n}\r\n// src/entities/utils.ts\r\nfunction selectIdValue(entity, selectId) {\r\n    var key = selectId(entity);\r\n    if (process.env.NODE_ENV !== \"production\" && key === void 0) {\r\n        console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\r\n    }\r\n    return key;\r\n}\r\nfunction ensureEntitiesArray(entities) {\r\n    if (!Array.isArray(entities)) {\r\n        entities = Object.values(entities);\r\n    }\r\n    return entities;\r\n}\r\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\r\n    newEntities = ensureEntitiesArray(newEntities);\r\n    var added = [];\r\n    var updated = [];\r\n    for (var _i = 0, newEntities_1 = newEntities; _i < newEntities_1.length; _i++) {\r\n        var entity = newEntities_1[_i];\r\n        var id = selectIdValue(entity, selectId);\r\n        if (id in state.entities) {\r\n            updated.push({ id: id, changes: entity });\r\n        }\r\n        else {\r\n            added.push(entity);\r\n        }\r\n    }\r\n    return [added, updated];\r\n}\r\n// src/entities/unsorted_state_adapter.ts\r\nfunction createUnsortedStateAdapter(selectId) {\r\n    function addOneMutably(entity, state) {\r\n        var key = selectIdValue(entity, selectId);\r\n        if (key in state.entities) {\r\n            return;\r\n        }\r\n        state.ids.push(key);\r\n        state.entities[key] = entity;\r\n    }\r\n    function addManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        for (var _i = 0, newEntities_2 = newEntities; _i < newEntities_2.length; _i++) {\r\n            var entity = newEntities_2[_i];\r\n            addOneMutably(entity, state);\r\n        }\r\n    }\r\n    function setOneMutably(entity, state) {\r\n        var key = selectIdValue(entity, selectId);\r\n        if (!(key in state.entities)) {\r\n            state.ids.push(key);\r\n        }\r\n        state.entities[key] = entity;\r\n    }\r\n    function setManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        for (var _i = 0, newEntities_3 = newEntities; _i < newEntities_3.length; _i++) {\r\n            var entity = newEntities_3[_i];\r\n            setOneMutably(entity, state);\r\n        }\r\n    }\r\n    function setAllMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        state.ids = [];\r\n        state.entities = {};\r\n        addManyMutably(newEntities, state);\r\n    }\r\n    function removeOneMutably(key, state) {\r\n        return removeManyMutably([key], state);\r\n    }\r\n    function removeManyMutably(keys, state) {\r\n        var didMutate = false;\r\n        keys.forEach(function (key) {\r\n            if (key in state.entities) {\r\n                delete state.entities[key];\r\n                didMutate = true;\r\n            }\r\n        });\r\n        if (didMutate) {\r\n            state.ids = state.ids.filter(function (id) { return id in state.entities; });\r\n        }\r\n    }\r\n    function removeAllMutably(state) {\r\n        Object.assign(state, {\r\n            ids: [],\r\n            entities: {}\r\n        });\r\n    }\r\n    function takeNewKey(keys, update, state) {\r\n        var original2 = state.entities[update.id];\r\n        var updated = Object.assign({}, original2, update.changes);\r\n        var newKey = selectIdValue(updated, selectId);\r\n        var hasNewKey = newKey !== update.id;\r\n        if (hasNewKey) {\r\n            keys[update.id] = newKey;\r\n            delete state.entities[update.id];\r\n        }\r\n        state.entities[newKey] = updated;\r\n        return hasNewKey;\r\n    }\r\n    function updateOneMutably(update, state) {\r\n        return updateManyMutably([update], state);\r\n    }\r\n    function updateManyMutably(updates, state) {\r\n        var newKeys = {};\r\n        var updatesPerEntity = {};\r\n        updates.forEach(function (update) {\r\n            if (update.id in state.entities) {\r\n                updatesPerEntity[update.id] = {\r\n                    id: update.id,\r\n                    changes: __spreadValues(__spreadValues({}, updatesPerEntity[update.id] ? updatesPerEntity[update.id].changes : null), update.changes)\r\n                };\r\n            }\r\n        });\r\n        updates = Object.values(updatesPerEntity);\r\n        var didMutateEntities = updates.length > 0;\r\n        if (didMutateEntities) {\r\n            var didMutateIds = updates.filter(function (update) { return takeNewKey(newKeys, update, state); }).length > 0;\r\n            if (didMutateIds) {\r\n                state.ids = Object.keys(state.entities);\r\n            }\r\n        }\r\n    }\r\n    function upsertOneMutably(entity, state) {\r\n        return upsertManyMutably([entity], state);\r\n    }\r\n    function upsertManyMutably(newEntities, state) {\r\n        var _c = splitAddedUpdatedEntities(newEntities, selectId, state), added = _c[0], updated = _c[1];\r\n        updateManyMutably(updated, state);\r\n        addManyMutably(added, state);\r\n    }\r\n    return {\r\n        removeAll: createSingleArgumentStateOperator(removeAllMutably),\r\n        addOne: createStateOperator(addOneMutably),\r\n        addMany: createStateOperator(addManyMutably),\r\n        setOne: createStateOperator(setOneMutably),\r\n        setMany: createStateOperator(setManyMutably),\r\n        setAll: createStateOperator(setAllMutably),\r\n        updateOne: createStateOperator(updateOneMutably),\r\n        updateMany: createStateOperator(updateManyMutably),\r\n        upsertOne: createStateOperator(upsertOneMutably),\r\n        upsertMany: createStateOperator(upsertManyMutably),\r\n        removeOne: createStateOperator(removeOneMutably),\r\n        removeMany: createStateOperator(removeManyMutably)\r\n    };\r\n}\r\n// src/entities/sorted_state_adapter.ts\r\nfunction createSortedStateAdapter(selectId, sort) {\r\n    var _c = createUnsortedStateAdapter(selectId), removeOne = _c.removeOne, removeMany = _c.removeMany, removeAll = _c.removeAll;\r\n    function addOneMutably(entity, state) {\r\n        return addManyMutably([entity], state);\r\n    }\r\n    function addManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        var models = newEntities.filter(function (model) { return !(selectIdValue(model, selectId) in state.entities); });\r\n        if (models.length !== 0) {\r\n            merge(models, state);\r\n        }\r\n    }\r\n    function setOneMutably(entity, state) {\r\n        return setManyMutably([entity], state);\r\n    }\r\n    function setManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        if (newEntities.length !== 0) {\r\n            merge(newEntities, state);\r\n        }\r\n    }\r\n    function setAllMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        state.entities = {};\r\n        state.ids = [];\r\n        addManyMutably(newEntities, state);\r\n    }\r\n    function updateOneMutably(update, state) {\r\n        return updateManyMutably([update], state);\r\n    }\r\n    function updateManyMutably(updates, state) {\r\n        var appliedUpdates = false;\r\n        for (var _i = 0, updates_1 = updates; _i < updates_1.length; _i++) {\r\n            var update = updates_1[_i];\r\n            var entity = state.entities[update.id];\r\n            if (!entity) {\r\n                continue;\r\n            }\r\n            appliedUpdates = true;\r\n            Object.assign(entity, update.changes);\r\n            var newId = selectId(entity);\r\n            if (update.id !== newId) {\r\n                delete state.entities[update.id];\r\n                state.entities[newId] = entity;\r\n            }\r\n        }\r\n        if (appliedUpdates) {\r\n            resortEntities(state);\r\n        }\r\n    }\r\n    function upsertOneMutably(entity, state) {\r\n        return upsertManyMutably([entity], state);\r\n    }\r\n    function upsertManyMutably(newEntities, state) {\r\n        var _c = splitAddedUpdatedEntities(newEntities, selectId, state), added = _c[0], updated = _c[1];\r\n        updateManyMutably(updated, state);\r\n        addManyMutably(added, state);\r\n    }\r\n    function areArraysEqual(a, b) {\r\n        if (a.length !== b.length) {\r\n            return false;\r\n        }\r\n        for (var i = 0; i < a.length && i < b.length; i++) {\r\n            if (a[i] === b[i]) {\r\n                continue;\r\n            }\r\n            return false;\r\n        }\r\n        return true;\r\n    }\r\n    function merge(models, state) {\r\n        models.forEach(function (model) {\r\n            state.entities[selectId(model)] = model;\r\n        });\r\n        resortEntities(state);\r\n    }\r\n    function resortEntities(state) {\r\n        var allEntities = Object.values(state.entities);\r\n        allEntities.sort(sort);\r\n        var newSortedIds = allEntities.map(selectId);\r\n        var ids = state.ids;\r\n        if (!areArraysEqual(ids, newSortedIds)) {\r\n            state.ids = newSortedIds;\r\n        }\r\n    }\r\n    return {\r\n        removeOne: removeOne,\r\n        removeMany: removeMany,\r\n        removeAll: removeAll,\r\n        addOne: createStateOperator(addOneMutably),\r\n        updateOne: createStateOperator(updateOneMutably),\r\n        upsertOne: createStateOperator(upsertOneMutably),\r\n        setOne: createStateOperator(setOneMutably),\r\n        setMany: createStateOperator(setManyMutably),\r\n        setAll: createStateOperator(setAllMutably),\r\n        addMany: createStateOperator(addManyMutably),\r\n        updateMany: createStateOperator(updateManyMutably),\r\n        upsertMany: createStateOperator(upsertManyMutably)\r\n    };\r\n}\r\n// src/entities/create_adapter.ts\r\nfunction createEntityAdapter(options) {\r\n    if (options === void 0) { options = {}; }\r\n    var _c = __spreadValues({\r\n        sortComparer: false,\r\n        selectId: function (instance) { return instance.id; }\r\n    }, options), selectId = _c.selectId, sortComparer = _c.sortComparer;\r\n    var stateFactory = createInitialStateFactory();\r\n    var selectorsFactory = createSelectorsFactory();\r\n    var stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\r\n    return __spreadValues(__spreadValues(__spreadValues({\r\n        selectId: selectId,\r\n        sortComparer: sortComparer\r\n    }, stateFactory), selectorsFactory), stateAdapter);\r\n}\r\n// src/nanoid.ts\r\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\r\nvar nanoid = function (size) {\r\n    if (size === void 0) { size = 21; }\r\n    var id = \"\";\r\n    var i = size;\r\n    while (i--) {\r\n        id += urlAlphabet[Math.random() * 64 | 0];\r\n    }\r\n    return id;\r\n};\r\n// src/createAsyncThunk.ts\r\nvar commonProperties = [\r\n    \"name\",\r\n    \"message\",\r\n    \"stack\",\r\n    \"code\"\r\n];\r\nvar RejectWithValue = /** @class */ (function () {\r\n    function RejectWithValue(payload, meta) {\r\n        this.payload = payload;\r\n        this.meta = meta;\r\n    }\r\n    return RejectWithValue;\r\n}());\r\nvar FulfillWithMeta = /** @class */ (function () {\r\n    function FulfillWithMeta(payload, meta) {\r\n        this.payload = payload;\r\n        this.meta = meta;\r\n    }\r\n    return FulfillWithMeta;\r\n}());\r\nvar miniSerializeError = function (value) {\r\n    if (typeof value === \"object\" && value !== null) {\r\n        var simpleError = {};\r\n        for (var _i = 0, commonProperties_1 = commonProperties; _i < commonProperties_1.length; _i++) {\r\n            var property = commonProperties_1[_i];\r\n            if (typeof value[property] === \"string\") {\r\n                simpleError[property] = value[property];\r\n            }\r\n        }\r\n        return simpleError;\r\n    }\r\n    return { message: String(value) };\r\n};\r\nvar createAsyncThunk = (function () {\r\n    function createAsyncThunk2(typePrefix, payloadCreator, options) {\r\n        var fulfilled = createAction(typePrefix + \"/fulfilled\", function (payload, requestId, arg, meta) { return ({\r\n            payload: payload,\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                requestStatus: \"fulfilled\"\r\n            })\r\n        }); });\r\n        var pending = createAction(typePrefix + \"/pending\", function (requestId, arg, meta) { return ({\r\n            payload: void 0,\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                requestStatus: \"pending\"\r\n            })\r\n        }); });\r\n        var rejected = createAction(typePrefix + \"/rejected\", function (error, requestId, arg, payload, meta) { return ({\r\n            payload: payload,\r\n            error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                rejectedWithValue: !!payload,\r\n                requestStatus: \"rejected\",\r\n                aborted: (error == null ? void 0 : error.name) === \"AbortError\",\r\n                condition: (error == null ? void 0 : error.name) === \"ConditionError\"\r\n            })\r\n        }); });\r\n        var displayedWarning = false;\r\n        var AC = typeof AbortController !== \"undefined\" ? AbortController : /** @class */ (function () {\r\n            function class_1() {\r\n                this.signal = {\r\n                    aborted: false,\r\n                    addEventListener: function () {\r\n                    },\r\n                    dispatchEvent: function () {\r\n                        return false;\r\n                    },\r\n                    onabort: function () {\r\n                    },\r\n                    removeEventListener: function () {\r\n                    },\r\n                    reason: void 0,\r\n                    throwIfAborted: function () {\r\n                    }\r\n                };\r\n            }\r\n            class_1.prototype.abort = function () {\r\n                if (process.env.NODE_ENV !== \"production\") {\r\n                    if (!displayedWarning) {\r\n                        displayedWarning = true;\r\n                        console.info(\"This platform does not implement AbortController. \\nIf you want to use the AbortController to react to `abort` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'.\");\r\n                    }\r\n                }\r\n            };\r\n            return class_1;\r\n        }());\r\n        function actionCreator(arg) {\r\n            return function (dispatch, getState, extra) {\r\n                var requestId = (options == null ? void 0 : options.idGenerator) ? options.idGenerator(arg) : nanoid();\r\n                var abortController = new AC();\r\n                var abortReason;\r\n                var started = false;\r\n                function abort(reason) {\r\n                    abortReason = reason;\r\n                    abortController.abort();\r\n                }\r\n                var promise2 = function () {\r\n                    return __async(this, null, function () {\r\n                        var _a, _b, finalAction, conditionResult, abortedPromise, err_1, skipDispatch;\r\n                        return __generator(this, function (_c) {\r\n                            switch (_c.label) {\r\n                                case 0:\r\n                                    _c.trys.push([0, 4, , 5]);\r\n                                    conditionResult = (_a = options == null ? void 0 : options.condition) == null ? void 0 : _a.call(options, arg, { getState: getState, extra: extra });\r\n                                    if (!isThenable(conditionResult)) return [3 /*break*/, 2];\r\n                                    return [4 /*yield*/, conditionResult];\r\n                                case 1:\r\n                                    conditionResult = _c.sent();\r\n                                    _c.label = 2;\r\n                                case 2:\r\n                                    if (conditionResult === false || abortController.signal.aborted) {\r\n                                        throw {\r\n                                            name: \"ConditionError\",\r\n                                            message: \"Aborted due to condition callback returning false.\"\r\n                                        };\r\n                                    }\r\n                                    started = true;\r\n                                    abortedPromise = new Promise(function (_, reject) { return abortController.signal.addEventListener(\"abort\", function () { return reject({\r\n                                        name: \"AbortError\",\r\n                                        message: abortReason || \"Aborted\"\r\n                                    }); }); });\r\n                                    dispatch(pending(requestId, arg, (_b = options == null ? void 0 : options.getPendingMeta) == null ? void 0 : _b.call(options, { requestId: requestId, arg: arg }, { getState: getState, extra: extra })));\r\n                                    return [4 /*yield*/, Promise.race([\r\n                                            abortedPromise,\r\n                                            Promise.resolve(payloadCreator(arg, {\r\n                                                dispatch: dispatch,\r\n                                                getState: getState,\r\n                                                extra: extra,\r\n                                                requestId: requestId,\r\n                                                signal: abortController.signal,\r\n                                                abort: abort,\r\n                                                rejectWithValue: function (value, meta) {\r\n                                                    return new RejectWithValue(value, meta);\r\n                                                },\r\n                                                fulfillWithValue: function (value, meta) {\r\n                                                    return new FulfillWithMeta(value, meta);\r\n                                                }\r\n                                            })).then(function (result) {\r\n                                                if (result instanceof RejectWithValue) {\r\n                                                    throw result;\r\n                                                }\r\n                                                if (result instanceof FulfillWithMeta) {\r\n                                                    return fulfilled(result.payload, requestId, arg, result.meta);\r\n                                                }\r\n                                                return fulfilled(result, requestId, arg);\r\n                                            })\r\n                                        ])];\r\n                                case 3:\r\n                                    finalAction = _c.sent();\r\n                                    return [3 /*break*/, 5];\r\n                                case 4:\r\n                                    err_1 = _c.sent();\r\n                                    finalAction = err_1 instanceof RejectWithValue ? rejected(null, requestId, arg, err_1.payload, err_1.meta) : rejected(err_1, requestId, arg);\r\n                                    return [3 /*break*/, 5];\r\n                                case 5:\r\n                                    skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\r\n                                    if (!skipDispatch) {\r\n                                        dispatch(finalAction);\r\n                                    }\r\n                                    return [2 /*return*/, finalAction];\r\n                            }\r\n                        });\r\n                    });\r\n                }();\r\n                return Object.assign(promise2, {\r\n                    abort: abort,\r\n                    requestId: requestId,\r\n                    arg: arg,\r\n                    unwrap: function () {\r\n                        return promise2.then(unwrapResult);\r\n                    }\r\n                });\r\n            };\r\n        }\r\n        return Object.assign(actionCreator, {\r\n            pending: pending,\r\n            rejected: rejected,\r\n            fulfilled: fulfilled,\r\n            typePrefix: typePrefix\r\n        });\r\n    }\r\n    createAsyncThunk2.withTypes = function () { return createAsyncThunk2; };\r\n    return createAsyncThunk2;\r\n})();\r\nfunction unwrapResult(action) {\r\n    if (action.meta && action.meta.rejectedWithValue) {\r\n        throw action.payload;\r\n    }\r\n    if (action.error) {\r\n        throw action.error;\r\n    }\r\n    return action.payload;\r\n}\r\nfunction isThenable(value) {\r\n    return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\r\n}\r\n// src/matchers.ts\r\nvar matches = function (matcher, action) {\r\n    if (hasMatchFunction(matcher)) {\r\n        return matcher.match(action);\r\n    }\r\n    else {\r\n        return matcher(action);\r\n    }\r\n};\r\nfunction isAnyOf() {\r\n    var matchers = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        matchers[_i] = arguments[_i];\r\n    }\r\n    return function (action) {\r\n        return matchers.some(function (matcher) { return matches(matcher, action); });\r\n    };\r\n}\r\nfunction isAllOf() {\r\n    var matchers = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        matchers[_i] = arguments[_i];\r\n    }\r\n    return function (action) {\r\n        return matchers.every(function (matcher) { return matches(matcher, action); });\r\n    };\r\n}\r\nfunction hasExpectedRequestMetadata(action, validStatus) {\r\n    if (!action || !action.meta)\r\n        return false;\r\n    var hasValidRequestId = typeof action.meta.requestId === \"string\";\r\n    var hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\r\n    return hasValidRequestId && hasValidRequestStatus;\r\n}\r\nfunction isAsyncThunkArray(a) {\r\n    return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\r\n}\r\nfunction isPending() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"pending\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isPending()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.pending; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isRejected() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"rejected\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isRejected()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.rejected; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isRejectedWithValue() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    var hasFlag = function (action) {\r\n        return action && action.meta && action.meta.rejectedWithValue;\r\n    };\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) {\r\n            var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\r\n            return combinedMatcher(action);\r\n        };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isRejectedWithValue()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isFulfilled() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"fulfilled\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isFulfilled()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.fulfilled; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isAsyncThunkAction() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isAsyncThunkAction()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = [];\r\n        for (var _i = 0, asyncThunks_1 = asyncThunks; _i < asyncThunks_1.length; _i++) {\r\n            var asyncThunk = asyncThunks_1[_i];\r\n            matchers.push(asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled);\r\n        }\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\n// src/listenerMiddleware/utils.ts\r\nvar assertFunction = function (func, expected) {\r\n    if (typeof func !== \"function\") {\r\n        throw new TypeError(expected + \" is not a function\");\r\n    }\r\n};\r\nvar noop = function () {\r\n};\r\nvar catchRejection = function (promise2, onError) {\r\n    if (onError === void 0) { onError = noop; }\r\n    promise2.catch(onError);\r\n    return promise2;\r\n};\r\nvar addAbortSignalListener = function (abortSignal, callback) {\r\n    abortSignal.addEventListener(\"abort\", callback, { once: true });\r\n    return function () { return abortSignal.removeEventListener(\"abort\", callback); };\r\n};\r\nvar abortControllerWithReason = function (abortController, reason) {\r\n    var signal = abortController.signal;\r\n    if (signal.aborted) {\r\n        return;\r\n    }\r\n    if (!(\"reason\" in signal)) {\r\n        Object.defineProperty(signal, \"reason\", {\r\n            enumerable: true,\r\n            value: reason,\r\n            configurable: true,\r\n            writable: true\r\n        });\r\n    }\r\n    ;\r\n    abortController.abort(reason);\r\n};\r\n// src/listenerMiddleware/exceptions.ts\r\nvar task = \"task\";\r\nvar listener = \"listener\";\r\nvar completed = \"completed\";\r\nvar cancelled = \"cancelled\";\r\nvar taskCancelled = \"task-\" + cancelled;\r\nvar taskCompleted = \"task-\" + completed;\r\nvar listenerCancelled = listener + \"-\" + cancelled;\r\nvar listenerCompleted = listener + \"-\" + completed;\r\nvar TaskAbortError = /** @class */ (function () {\r\n    function TaskAbortError(code) {\r\n        this.code = code;\r\n        this.name = \"TaskAbortError\";\r\n        this.message = task + \" \" + cancelled + \" (reason: \" + code + \")\";\r\n    }\r\n    return TaskAbortError;\r\n}());\r\n// src/listenerMiddleware/task.ts\r\nvar validateActive = function (signal) {\r\n    if (signal.aborted) {\r\n        throw new TaskAbortError(signal.reason);\r\n    }\r\n};\r\nfunction raceWithSignal(signal, promise2) {\r\n    var cleanup = noop;\r\n    return new Promise(function (resolve, reject) {\r\n        var notifyRejection = function () { return reject(new TaskAbortError(signal.reason)); };\r\n        if (signal.aborted) {\r\n            notifyRejection();\r\n            return;\r\n        }\r\n        cleanup = addAbortSignalListener(signal, notifyRejection);\r\n        promise2.finally(function () { return cleanup(); }).then(resolve, reject);\r\n    }).finally(function () {\r\n        cleanup = noop;\r\n    });\r\n}\r\nvar runTask = function (task2, cleanUp) { return __async(void 0, null, function () {\r\n    var value, error_1;\r\n    return __generator(this, function (_c) {\r\n        switch (_c.label) {\r\n            case 0:\r\n                _c.trys.push([0, 3, 4, 5]);\r\n                return [4 /*yield*/, Promise.resolve()];\r\n            case 1:\r\n                _c.sent();\r\n                return [4 /*yield*/, task2()];\r\n            case 2:\r\n                value = _c.sent();\r\n                return [2 /*return*/, {\r\n                        status: \"ok\",\r\n                        value: value\r\n                    }];\r\n            case 3:\r\n                error_1 = _c.sent();\r\n                return [2 /*return*/, {\r\n                        status: error_1 instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\r\n                        error: error_1\r\n                    }];\r\n            case 4:\r\n                cleanUp == null ? void 0 : cleanUp();\r\n                return [7 /*endfinally*/];\r\n            case 5: return [2 /*return*/];\r\n        }\r\n    });\r\n}); };\r\nvar createPause = function (signal) {\r\n    return function (promise2) {\r\n        return catchRejection(raceWithSignal(signal, promise2).then(function (output) {\r\n            validateActive(signal);\r\n            return output;\r\n        }));\r\n    };\r\n};\r\nvar createDelay = function (signal) {\r\n    var pause = createPause(signal);\r\n    return function (timeoutMs) {\r\n        return pause(new Promise(function (resolve) { return setTimeout(resolve, timeoutMs); }));\r\n    };\r\n};\r\n// src/listenerMiddleware/index.ts\r\nvar assign = Object.assign;\r\nvar INTERNAL_NIL_TOKEN = {};\r\nvar alm = \"listenerMiddleware\";\r\nvar createFork = function (parentAbortSignal, parentBlockingPromises) {\r\n    var linkControllers = function (controller) { return addAbortSignalListener(parentAbortSignal, function () { return abortControllerWithReason(controller, parentAbortSignal.reason); }); };\r\n    return function (taskExecutor, opts) {\r\n        assertFunction(taskExecutor, \"taskExecutor\");\r\n        var childAbortController = new AbortController();\r\n        linkControllers(childAbortController);\r\n        var result = runTask(function () { return __async(void 0, null, function () {\r\n            var result2;\r\n            return __generator(this, function (_c) {\r\n                switch (_c.label) {\r\n                    case 0:\r\n                        validateActive(parentAbortSignal);\r\n                        validateActive(childAbortController.signal);\r\n                        return [4 /*yield*/, taskExecutor({\r\n                                pause: createPause(childAbortController.signal),\r\n                                delay: createDelay(childAbortController.signal),\r\n                                signal: childAbortController.signal\r\n                            })];\r\n                    case 1:\r\n                        result2 = _c.sent();\r\n                        validateActive(childAbortController.signal);\r\n                        return [2 /*return*/, result2];\r\n                }\r\n            });\r\n        }); }, function () { return abortControllerWithReason(childAbortController, taskCompleted); });\r\n        if (opts == null ? void 0 : opts.autoJoin) {\r\n            parentBlockingPromises.push(result);\r\n        }\r\n        return {\r\n            result: createPause(parentAbortSignal)(result),\r\n            cancel: function () {\r\n                abortControllerWithReason(childAbortController, taskCancelled);\r\n            }\r\n        };\r\n    };\r\n};\r\nvar createTakePattern = function (startListening, signal) {\r\n    var take = function (predicate, timeout) { return __async(void 0, null, function () {\r\n        var unsubscribe, tuplePromise, promises, output;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    validateActive(signal);\r\n                    unsubscribe = function () {\r\n                    };\r\n                    tuplePromise = new Promise(function (resolve, reject) {\r\n                        var stopListening = startListening({\r\n                            predicate: predicate,\r\n                            effect: function (action, listenerApi) {\r\n                                listenerApi.unsubscribe();\r\n                                resolve([\r\n                                    action,\r\n                                    listenerApi.getState(),\r\n                                    listenerApi.getOriginalState()\r\n                                ]);\r\n                            }\r\n                        });\r\n                        unsubscribe = function () {\r\n                            stopListening();\r\n                            reject();\r\n                        };\r\n                    });\r\n                    promises = [\r\n                        tuplePromise\r\n                    ];\r\n                    if (timeout != null) {\r\n                        promises.push(new Promise(function (resolve) { return setTimeout(resolve, timeout, null); }));\r\n                    }\r\n                    _c.label = 1;\r\n                case 1:\r\n                    _c.trys.push([1, , 3, 4]);\r\n                    return [4 /*yield*/, raceWithSignal(signal, Promise.race(promises))];\r\n                case 2:\r\n                    output = _c.sent();\r\n                    validateActive(signal);\r\n                    return [2 /*return*/, output];\r\n                case 3:\r\n                    unsubscribe();\r\n                    return [7 /*endfinally*/];\r\n                case 4: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); };\r\n    return function (predicate, timeout) { return catchRejection(take(predicate, timeout)); };\r\n};\r\nvar getListenerEntryPropsFrom = function (options) {\r\n    var type = options.type, actionCreator = options.actionCreator, matcher = options.matcher, predicate = options.predicate, effect = options.effect;\r\n    if (type) {\r\n        predicate = createAction(type).match;\r\n    }\r\n    else if (actionCreator) {\r\n        type = actionCreator.type;\r\n        predicate = actionCreator.match;\r\n    }\r\n    else if (matcher) {\r\n        predicate = matcher;\r\n    }\r\n    else if (predicate) {\r\n    }\r\n    else {\r\n        throw new Error(\"Creating or removing a listener requires one of the known fields for matching an action\");\r\n    }\r\n    assertFunction(effect, \"options.listener\");\r\n    return { predicate: predicate, type: type, effect: effect };\r\n};\r\nvar createListenerEntry = function (options) {\r\n    var _c = getListenerEntryPropsFrom(options), type = _c.type, predicate = _c.predicate, effect = _c.effect;\r\n    var id = nanoid();\r\n    var entry = {\r\n        id: id,\r\n        effect: effect,\r\n        type: type,\r\n        predicate: predicate,\r\n        pending: new Set(),\r\n        unsubscribe: function () {\r\n            throw new Error(\"Unsubscribe not initialized\");\r\n        }\r\n    };\r\n    return entry;\r\n};\r\nvar cancelActiveListeners = function (entry) {\r\n    entry.pending.forEach(function (controller) {\r\n        abortControllerWithReason(controller, listenerCancelled);\r\n    });\r\n};\r\nvar createClearListenerMiddleware = function (listenerMap) {\r\n    return function () {\r\n        listenerMap.forEach(cancelActiveListeners);\r\n        listenerMap.clear();\r\n    };\r\n};\r\nvar safelyNotifyError = function (errorHandler, errorToNotify, errorInfo) {\r\n    try {\r\n        errorHandler(errorToNotify, errorInfo);\r\n    }\r\n    catch (errorHandlerError) {\r\n        setTimeout(function () {\r\n            throw errorHandlerError;\r\n        }, 0);\r\n    }\r\n};\r\nvar addListener = createAction(alm + \"/add\");\r\nvar clearAllListeners = createAction(alm + \"/removeAll\");\r\nvar removeListener = createAction(alm + \"/remove\");\r\nvar defaultErrorHandler = function () {\r\n    var args = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        args[_i] = arguments[_i];\r\n    }\r\n    console.error.apply(console, __spreadArray([alm + \"/error\"], args));\r\n};\r\nfunction createListenerMiddleware(middlewareOptions) {\r\n    var _this = this;\r\n    if (middlewareOptions === void 0) { middlewareOptions = {}; }\r\n    var listenerMap = new Map();\r\n    var extra = middlewareOptions.extra, _c = middlewareOptions.onError, onError = _c === void 0 ? defaultErrorHandler : _c;\r\n    assertFunction(onError, \"onError\");\r\n    var insertEntry = function (entry) {\r\n        entry.unsubscribe = function () { return listenerMap.delete(entry.id); };\r\n        listenerMap.set(entry.id, entry);\r\n        return function (cancelOptions) {\r\n            entry.unsubscribe();\r\n            if (cancelOptions == null ? void 0 : cancelOptions.cancelActive) {\r\n                cancelActiveListeners(entry);\r\n            }\r\n        };\r\n    };\r\n    var findListenerEntry = function (comparator) {\r\n        for (var _i = 0, _c = Array.from(listenerMap.values()); _i < _c.length; _i++) {\r\n            var entry = _c[_i];\r\n            if (comparator(entry)) {\r\n                return entry;\r\n            }\r\n        }\r\n        return void 0;\r\n    };\r\n    var startListening = function (options) {\r\n        var entry = findListenerEntry(function (existingEntry) { return existingEntry.effect === options.effect; });\r\n        if (!entry) {\r\n            entry = createListenerEntry(options);\r\n        }\r\n        return insertEntry(entry);\r\n    };\r\n    var stopListening = function (options) {\r\n        var _c = getListenerEntryPropsFrom(options), type = _c.type, effect = _c.effect, predicate = _c.predicate;\r\n        var entry = findListenerEntry(function (entry2) {\r\n            var matchPredicateOrType = typeof type === \"string\" ? entry2.type === type : entry2.predicate === predicate;\r\n            return matchPredicateOrType && entry2.effect === effect;\r\n        });\r\n        if (entry) {\r\n            entry.unsubscribe();\r\n            if (options.cancelActive) {\r\n                cancelActiveListeners(entry);\r\n            }\r\n        }\r\n        return !!entry;\r\n    };\r\n    var notifyListener = function (entry, action, api, getOriginalState) { return __async(_this, null, function () {\r\n        var internalTaskController, take, autoJoinPromises, listenerError_1;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    internalTaskController = new AbortController();\r\n                    take = createTakePattern(startListening, internalTaskController.signal);\r\n                    autoJoinPromises = [];\r\n                    _c.label = 1;\r\n                case 1:\r\n                    _c.trys.push([1, 3, 4, 6]);\r\n                    entry.pending.add(internalTaskController);\r\n                    return [4 /*yield*/, Promise.resolve(entry.effect(action, assign({}, api, {\r\n                            getOriginalState: getOriginalState,\r\n                            condition: function (predicate, timeout) { return take(predicate, timeout).then(Boolean); },\r\n                            take: take,\r\n                            delay: createDelay(internalTaskController.signal),\r\n                            pause: createPause(internalTaskController.signal),\r\n                            extra: extra,\r\n                            signal: internalTaskController.signal,\r\n                            fork: createFork(internalTaskController.signal, autoJoinPromises),\r\n                            unsubscribe: entry.unsubscribe,\r\n                            subscribe: function () {\r\n                                listenerMap.set(entry.id, entry);\r\n                            },\r\n                            cancelActiveListeners: function () {\r\n                                entry.pending.forEach(function (controller, _, set) {\r\n                                    if (controller !== internalTaskController) {\r\n                                        abortControllerWithReason(controller, listenerCancelled);\r\n                                        set.delete(controller);\r\n                                    }\r\n                                });\r\n                            }\r\n                        })))];\r\n                case 2:\r\n                    _c.sent();\r\n                    return [3 /*break*/, 6];\r\n                case 3:\r\n                    listenerError_1 = _c.sent();\r\n                    if (!(listenerError_1 instanceof TaskAbortError)) {\r\n                        safelyNotifyError(onError, listenerError_1, {\r\n                            raisedBy: \"effect\"\r\n                        });\r\n                    }\r\n                    return [3 /*break*/, 6];\r\n                case 4: return [4 /*yield*/, Promise.allSettled(autoJoinPromises)];\r\n                case 5:\r\n                    _c.sent();\r\n                    abortControllerWithReason(internalTaskController, listenerCompleted);\r\n                    entry.pending.delete(internalTaskController);\r\n                    return [7 /*endfinally*/];\r\n                case 6: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); };\r\n    var clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\r\n    var middleware = function (api) { return function (next) { return function (action) {\r\n        if (!isAction(action)) {\r\n            return next(action);\r\n        }\r\n        if (addListener.match(action)) {\r\n            return startListening(action.payload);\r\n        }\r\n        if (clearAllListeners.match(action)) {\r\n            clearListenerMiddleware();\r\n            return;\r\n        }\r\n        if (removeListener.match(action)) {\r\n            return stopListening(action.payload);\r\n        }\r\n        var originalState = api.getState();\r\n        var getOriginalState = function () {\r\n            if (originalState === INTERNAL_NIL_TOKEN) {\r\n                throw new Error(alm + \": getOriginalState can only be called synchronously\");\r\n            }\r\n            return originalState;\r\n        };\r\n        var result;\r\n        try {\r\n            result = next(action);\r\n            if (listenerMap.size > 0) {\r\n                var currentState = api.getState();\r\n                var listenerEntries = Array.from(listenerMap.values());\r\n                for (var _i = 0, listenerEntries_1 = listenerEntries; _i < listenerEntries_1.length; _i++) {\r\n                    var entry = listenerEntries_1[_i];\r\n                    var runListener = false;\r\n                    try {\r\n                        runListener = entry.predicate(action, currentState, originalState);\r\n                    }\r\n                    catch (predicateError) {\r\n                        runListener = false;\r\n                        safelyNotifyError(onError, predicateError, {\r\n                            raisedBy: \"predicate\"\r\n                        });\r\n                    }\r\n                    if (!runListener) {\r\n                        continue;\r\n                    }\r\n                    notifyListener(entry, action, api, getOriginalState);\r\n                }\r\n            }\r\n        }\r\n        finally {\r\n            originalState = INTERNAL_NIL_TOKEN;\r\n        }\r\n        return result;\r\n    }; }; };\r\n    return {\r\n        middleware: middleware,\r\n        startListening: startListening,\r\n        stopListening: stopListening,\r\n        clearListeners: clearListenerMiddleware\r\n    };\r\n}\r\n// src/autoBatchEnhancer.ts\r\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\r\nvar prepareAutoBatched = function () { return function (payload) {\r\n    var _c;\r\n    return ({\r\n        payload: payload,\r\n        meta: (_c = {}, _c[SHOULD_AUTOBATCH] = true, _c)\r\n    });\r\n}; };\r\nvar promise;\r\nvar queueMicrotaskShim = typeof queueMicrotask === \"function\" ? queueMicrotask.bind(typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : globalThis) : function (cb) { return (promise || (promise = Promise.resolve())).then(cb).catch(function (err) { return setTimeout(function () {\r\n    throw err;\r\n}, 0); }); };\r\nvar createQueueWithTimer = function (timeout) {\r\n    return function (notify) {\r\n        setTimeout(notify, timeout);\r\n    };\r\n};\r\nvar rAF = typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10);\r\nvar autoBatchEnhancer = function (options) {\r\n    if (options === void 0) { options = { type: \"raf\" }; }\r\n    return function (next) { return function () {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var store = next.apply(void 0, args);\r\n        var notifying = true;\r\n        var shouldNotifyAtEndOfTick = false;\r\n        var notificationQueued = false;\r\n        var listeners = new Set();\r\n        var queueCallback = options.type === \"tick\" ? queueMicrotaskShim : options.type === \"raf\" ? rAF : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\r\n        var notifyListeners = function () {\r\n            notificationQueued = false;\r\n            if (shouldNotifyAtEndOfTick) {\r\n                shouldNotifyAtEndOfTick = false;\r\n                listeners.forEach(function (l) { return l(); });\r\n            }\r\n        };\r\n        return Object.assign({}, store, {\r\n            subscribe: function (listener2) {\r\n                var wrappedListener = function () { return notifying && listener2(); };\r\n                var unsubscribe = store.subscribe(wrappedListener);\r\n                listeners.add(listener2);\r\n                return function () {\r\n                    unsubscribe();\r\n                    listeners.delete(listener2);\r\n                };\r\n            },\r\n            dispatch: function (action) {\r\n                var _a;\r\n                try {\r\n                    notifying = !((_a = action == null ? void 0 : action.meta) == null ? void 0 : _a[SHOULD_AUTOBATCH]);\r\n                    shouldNotifyAtEndOfTick = !notifying;\r\n                    if (shouldNotifyAtEndOfTick) {\r\n                        if (!notificationQueued) {\r\n                            notificationQueued = true;\r\n                            queueCallback(notifyListeners);\r\n                        }\r\n                    }\r\n                    return store.dispatch(action);\r\n                }\r\n                finally {\r\n                    notifying = true;\r\n                }\r\n            }\r\n        });\r\n    }; };\r\n};\r\n// src/index.ts\r\nenableES5();\r\nexport { EnhancerArray, MiddlewareArray, SHOULD_AUTOBATCH, TaskAbortError, addListener, autoBatchEnhancer, clearAllListeners, configureStore, createAction, createActionCreatorInvariantMiddleware, createAsyncThunk, createDraftSafeSelector, createEntityAdapter, createImmutableStateInvariantMiddleware, createListenerMiddleware, default2 as createNextState, createReducer, createSelector2 as createSelector, createSerializableStateInvariantMiddleware, createSlice, current2 as current, findNonSerializableValue, freeze, getDefaultMiddleware, getType, isAction, isActionCreator, isAllOf, isAnyOf, isAsyncThunkAction, isDraft4 as isDraft, isFSA as isFluxStandardAction, isFulfilled, isImmutableDefault, isPending, isPlain, isPlainObject, isRejected, isRejectedWithValue, miniSerializeError, nanoid, original, prepareAutoBatched, removeListener, unwrapResult };\r\n//# sourceMappingURL=redux-toolkit.esm.js.map"], "mappings": ";;;;;;;;;;;;;SA4CgBA,EAAIC,IAAAA;AAAAA,WAAAA,KAAAA,UAAAA,QAA+BC,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,IAAAA,UAAAA,EAAAA;AAAAA,MAAAA,MACrC;AAAA,QACNC,KAAIC,EAAOH,EAAAA,GACXI,KAAOF,KAEG,cAAA,OAANA,KACPA,GAAEG,MAAM,MAAMJ,EAAAA,IACdC,KAHA,uBAAuBF;AAAAA,UAIhBM,MAAAA,aAAiBF,EAAAA;EAAAA;AAAAA,QAElBE,MAAAA,gCACqBN,MAC7BC,GAAKM,SAAS,MAAMN,GAAKO,IAAI,SAAAC,IAAAA;AAAAA,WAAAA,MAASA,KAAAA;EAAAA,CAAAA,EAAMC,KAAK,GAAA,IAAO,MAAA,kDAAA;AAAA;AAAA,SCvC3CC,EAAQC,IAAAA;AAAAA,SAAAA,CAAAA,CACdA,MAAAA,CAAAA,CAAWA,GAAMC,CAAAA;AAAAA;AAAAA,SAKXC,EAAYF,IAAAA;AAAAA,MAAAA;AAAAA,SAAAA,CAAAA,CACtBA,OAAAA,SAawBA,IAAAA;AAAAA,QAAAA,CACxBA,MAA0B,YAAA,OAAVA,GAAoB,QAAA;AAAO,QAC1CG,KAAQC,OAAOC,eAAeL,EAAAA;AAAAA,QACtB,SAAVG,GAAAA,QAAAA;AACI,QAEFG,KACLF,OAAOG,eAAeC,KAAKL,IAAO,aAAA,KAAkBA,GAAMM;AAAAA,WAEvDH,OAASF,UAGG,cAAA,OAARE,MACPI,SAASC,SAASH,KAAKF,EAAAA,MAAUM;EAAAA,EAxBnBZ,EAAAA,KACda,MAAMC,QAAQd,EAAAA,KAAAA,CAAAA,CACZA,GAAMe,CAAAA,KAAAA,CAAAA,EAAAA,UAAAA,KACNf,GAAMS,gBAAAA,WAAAA,KAAAA,SAANO,GAAoBD,CAAAA,MACtBE,EAAMjB,EAAAA,KACNkB,EAAMlB,EAAAA;AAAAA;AAAAA,SA0BQmB,EAASnB,IAAAA;AAAAA,SACnBD,EAAQC,EAAAA,KAAQb,EAAI,IAAIa,EAAAA,GACtBA,GAAMC,CAAAA,EAAamB;AAAAA;AA8B3B,SAAgBC,EAAKC,IAAUC,IAAWC,IAAAA;AAAAA,aAAAA,OAAAA,KAAAA,QAAiB,MACtDC,EAAYH,EAAAA,KACbE,KAAiBpB,OAAOsB,OAAOC,IAASL,EAAAA,EAAKM,QAAQ,SAAAC,IAAAA;AACjDL,IAAAA,MAAiC,YAAA,OAARK,MAAkBN,GAAKM,IAAKP,GAAIO,EAAAA,GAAMP,EAAAA;EAAAA,CAAAA,IAGrEA,GAAIM,QAAQ,SAACE,IAAYC,IAAAA;AAAAA,WAAeR,GAAKQ,IAAOD,IAAOR,EAAAA;EAAAA,CAAAA;AAAAA;AAAAA,SAK7CG,EAAYO,IAAAA;AAAAA,MAErBC,KAAgCD,GAAM/B,CAAAA;AAAAA,SACrCgC,KACJA,GAAMC,IAAQ,IACbD,GAAMC,IAAQ,IACbD,GAAMC,IACRrB,MAAMC,QAAQkB,EAAAA,IAAAA,IAEdf,EAAMe,EAAAA,IAAAA,IAENd,EAAMc,EAAAA,IAAAA,IAAAA;AAAAA;AAAAA,SAMMG,EAAIH,IAAYI,IAAAA;AAAAA,SAAAA,MACxBX,EAAYO,EAAAA,IAChBA,GAAMG,IAAIC,EAAAA,IACVhC,OAAOiC,UAAU9B,eAAeC,KAAKwB,IAAOI,EAAAA;AAAAA;AAAAA,SAIhCE,EAAIN,IAA2BI,IAAAA;AAAAA,SAAAA,MAEvCX,EAAYO,EAAAA,IAA0BA,GAAMM,IAAIF,EAAAA,IAAQJ,GAAMI,EAAAA;AAAAA;AAItE,SAAgBG,EAAIP,IAAYQ,IAA6BxC,IAAAA;AAAAA,MACtDyC,KAAIhB,EAAYO,EAAAA;AAAAA,QAClBS,KAAoBT,GAAMO,IAAIC,IAAgBxC,EAAAA,IAAAA,MACzCyC,KACRT,GAAMU,IAAI1C,EAAAA,IACJgC,GAAMQ,EAAAA,IAAkBxC;AAAAA;AAAAA,SAIhB2C,EAAGC,IAAQC,IAAAA;AAAAA,SAEtBD,OAAMC,KACI,MAAND,MAAW,IAAIA,MAAM,IAAIC,KAEzBD,MAAMA,MAAKC,MAAMA;AAAAA;AAAAA,SAKV5B,EAAM6B,IAAAA;AAAAA,SACdC,KAAUD,cAAkBE;AAAAA;AAAAA,SAIpB9B,EAAM4B,IAAAA;AAAAA,SACdG,KAAUH,cAAkBI;AAAAA;AAAAA,SAGpBC,EAAOlB,IAAAA;AAAAA,SACfA,GAAMmB,KAASnB,GAAMb;AAAAA;AAAAA,SAIbiC,EAAYC,IAAAA;AAAAA,MACvBzC,MAAMC,QAAQwC,EAAAA,EAAO,QAAOzC,MAAMwB,UAAUkB,MAAM/C,KAAK8C,EAAAA;AAAAA,MACrDE,KAAcC,GAA0BH,EAAAA;AAAAA,SACvCE,GAAYvD,CAAAA;AAAAA,WACfyB,KAAOC,GAAQ6B,EAAAA,GACVE,KAAI,GAAGA,KAAIhC,GAAK/B,QAAQ+D,MAAK;AAAA,QAC/B7B,KAAWH,GAAKgC,EAAAA,GAChBC,KAAOH,GAAY3B,EAAAA;AAAAA,cACrB8B,GAAKC,aACRD,GAAKC,WAAAA,MACLD,GAAKE,eAAAA,QAKFF,GAAKrB,OAAOqB,GAAKpB,SACpBiB,GAAY3B,EAAAA,IAAO,EAClBgC,cAAAA,MACAD,UAAAA,MACAE,YAAYH,GAAKG,YACjB9D,OAAOsD,GAAKzB,EAAAA,EAAAA;EAAAA;AAAAA,SAGRzB,OAAO2D,OAAO3D,OAAOC,eAAeiD,EAAAA,GAAOE,EAAAA;AAAAA;AAAAA,SAWnCQ,EAAU1C,IAAU2C,IAAAA;AAAAA,SAAAA,WAAAA,OAAAA,KAAAA,QAC/BC,EAAS5C,EAAAA,KAAQvB,EAAQuB,EAAAA,KAAAA,CAASpB,EAAYoB,EAAAA,MAC9CG,EAAYH,EAAAA,IAAO,MACtBA,GAAIiB,MAAMjB,GAAIoB,MAAMpB,GAAI6C,QAAQ7C,GAAI8C,SAASC,IAE9CjE,OAAO4D,OAAO1C,EAAAA,GACV2C,MAAM5C,EAAKC,IAAK,SAACO,IAAK7B,IAAAA;AAAAA,WAAUgE,EAAOhE,IAAAA,IAAO;EAAA,GAAA,IAAO,IALMsB;AAAAA;AAShE,SAAS+C,IAAAA;AACRlF,IAAI,CAAA;AAAA;AAAA,SAGW+E,EAAS5C,IAAAA;AAAAA,SACb,QAAPA,MAA8B,YAAA,OAARA,MAEnBlB,OAAO8D,SAAS5C,EAAAA;AAAAA;AAAAA,SCxKRgD,EACfC,IAAAA;AAAAA,MAEMC,KAASC,GAAQF,EAAAA;AAAAA,SAClBC,MACJrF,EAAI,IAAIoF,EAAAA,GAGFC;AAAAA;AAAAA,SAGQE,EACfH,IACAI,IAAAA;AAEKF,KAAQF,EAAAA,MAAYE,GAAQF,EAAAA,IAAaI;AAAAA;AClC/C,SAAgBC,IAAAA;AAAAA,SACCC,KAAc1F,EAAI,CAAA,GAC3B0F;AAAAA;AAAAA,SAkBQC,EACfC,IACAC,IAAAA;AAEIA,EAAAA,OACHV,EAAU,SAAA,GACVS,GAAME,IAAW,CAAA,GACjBF,GAAMG,IAAkB,CAAA,GACxBH,GAAMI,IAAiBH;AAAAA;AAAAA,SAITI,EAAYL,IAAAA;AAC3BM,IAAWN,EAAAA,GACXA,GAAMO,EAAQ1D,QAAQ2D,CAAAA,GAEtBR,GAAMO,IAAU;AAAA;AAAA,SAGDD,EAAWN,IAAAA;AACtBA,EAAAA,OAAUF,MACbA,IAAeE,GAAMS;AAAAA;AAAAA,SAIPC,EAAWC,IAAAA;AAAAA,SAClBb,IArCD,EACNS,GAAS,CAAA,GACTE,GAmCkCX,GAlClCc,GAkCgDD,IA/BhDE,GAAAA,MACAC,GAAoB,EAAA;AAAA;AAiCtB,SAASN,EAAYO,IAAAA;AAAAA,MACd7D,KAAoB6D,GAAM7F,CAAAA;AAAAA,QAE/BgC,GAAMC,KAAAA,MACND,GAAMC,IAEND,GAAM8D,EAAAA,IACF9D,GAAM+D,IAAAA;AAAW;AAAA,SC9DPC,EAAcC,IAAanB,IAAAA;AAC1CA,EAAAA,GAAMc,IAAqBd,GAAMO,EAAQ3F;AAAAA,MACnCwG,KAAYpB,GAAMO,EAAS,CAAA,GAC3Bc,KAAAA,WAAaF,MAAwBA,OAAWC;AAAAA,SACjDpB,GAAMY,EAAOU,KACjB/B,EAAU,KAAA,EAAOgC,EAAiBvB,IAAOmB,IAAQE,EAAAA,GAC9CA,MACCD,GAAUlG,CAAAA,EAAasG,MAC1BnB,EAAYL,EAAAA,GACZ5F,EAAI,CAAA,IAEDe,EAAYgG,EAAAA,MAEfA,KAASM,EAASzB,IAAOmB,EAAAA,GACpBnB,GAAMS,KAASiB,EAAY1B,IAAOmB,EAAAA,IAEpCnB,GAAME,KACTX,EAAU,SAAA,EAAWoC,EACpBP,GAAUlG,CAAAA,EAAamB,GACvB8E,IACAnB,GAAME,GACNF,GAAMG,CAAAA,KAKRgB,KAASM,EAASzB,IAAOoB,IAAW,CAAA,CAAA,GAErCf,EAAYL,EAAAA,GACRA,GAAME,KACTF,GAAMI,EAAgBJ,GAAME,GAAUF,GAAMG,CAAAA,GAEtCgB,OAAWS,IAAUT,KAAAA;AAASU;AAGtC,SAASJ,EAASK,IAAuB7G,IAAY8G,IAAAA;AAAAA,MAEhD5C,EAASlE,EAAAA,EAAQ,QAAOA;AAAAA,MAEtBiC,KAAoBjC,GAAMC,CAAAA;AAAAA,MAAAA,CAE3BgC,GAAAA,QACJZ,EACCrB,IACA,SAAC6B,IAAKkF,IAAAA;AAAAA,WACLC,EAAiBH,IAAW5E,IAAOjC,IAAO6B,IAAKkF,IAAYD,EAAAA;EAAAA,GAAAA,IAC5D,GAEM9G;AAAAA,MAGJiC,GAAMgF,MAAWJ,GAAW,QAAO7G;AAAAA,MAAAA,CAElCiC,GAAMsE,EAAAA,QACVE,EAAYI,IAAW5E,GAAMb,GAAAA,IAAO,GAC7Ba,GAAMb;AAAAA,MAAAA,CAGTa,GAAMiF,GAAY;AACtBjF,IAAAA,GAAMiF,IAAAA,MACNjF,GAAMgF,EAAOpB;AAAAA,QACPK,KAAAA,MAELjE,GAAMC,KAAAA,MAAiCD,GAAMC,IACzCD,GAAMmB,IAAQC,EAAYpB,GAAMkF,CAAAA,IACjClF,GAAMmB,GAKNgE,KAAalB,IACbhF,KAAAA;AAAQ,UACRe,GAAMC,MACTkF,KAAa,IAAIlE,IAAIgD,EAAAA,GACrBA,GAAO/B,MAAAA,GACPjD,KAAAA,OAEDG,EAAK+F,IAAY,SAACvF,IAAKkF,IAAAA;AAAAA,aACtBC,EAAiBH,IAAW5E,IAAOiE,IAAQrE,IAAKkF,IAAYD,IAAM5F,EAAAA;IAAAA,CAAAA,GAGnEuF,EAAYI,IAAWX,IAAAA,KAAQ,GAE3BY,MAAQD,GAAU5B,KACrBX,EAAU,SAAA,EAAW+C,EACpBpF,IACA6E,IACAD,GAAU5B,GACV4B,GAAU3B,CAAAA;EAAAA;AAAAA,SAINjD,GAAMmB;AAAAA;AAGd,SAAS4D,EACRH,IACAS,IACAC,IACAnF,IACA2E,IACAS,IACAC,IAAAA;AAAAA,MAEeV,OAAeQ,MAAcpI,EAAI,CAAA,GAC5CY,EAAQgH,EAAAA,GAAa;AAAA,QASlBW,KAAMlB,EAASK,IAAWE,IAP/BS,MACAF,MAAAA,MACAA,GAAapF,KAAAA,CACZC,EAAKmF,GAA8CK,GAAYvF,EAAAA,IAC7DoF,GAAUI,OAAOxF,EAAAA,IAAAA,MACjBwE;AAAAA,QAGJrE,EAAIgF,IAAcnF,IAAMsF,EAAAA,GAAAA,CAGpB3H,EAAQ2H,EAAAA,EAEL;AADNb,IAAAA,GAAUjB,IAAAA;EAAiB,MAElB6B,CAAAA,MACVF,GAAa7E,IAAIqE,EAAAA;AAAAA,MAGd7G,EAAY6G,EAAAA,KAAAA,CAAgB7C,EAAS6C,EAAAA,GAAa;AAAA,QAAA,CAChDF,GAAUlB,EAAOkC,KAAehB,GAAUhB,IAAqB,EAAA;AAQpEW,MAASK,IAAWE,EAAAA,GAEfO,MAAgBA,GAAYL,EAAOzB,KACvCiB,EAAYI,IAAWE,EAAAA;EAAAA;AAAAA;AAI1B,SAASN,EAAY1B,IAAmB/E,IAAYiE,IAAAA;AAAAA,aAAAA,OAAAA,KAAAA,QAAO,CAErDc,GAAMS,KAAWT,GAAMY,EAAOkC,KAAe9C,GAAMa,KACvD5B,EAAOhE,IAAOiE,EAAAA;AAAAA;ACqEhB,SAAS6D,EAAKhC,IAAgB1D,IAAAA;AAAAA,MACvBH,KAAQ6D,GAAM7F,CAAAA;AAAAA,UACLgC,KAAQkB,EAAOlB,EAAAA,IAAS6D,IACzB1D,EAAAA;AAAAA;AAcf,SAAS2F,EACRC,IACA5F,IAAAA;AAAAA,MAGMA,MAAQ4F,GAAAA,UACV7H,KAAQC,OAAOC,eAAe2H,EAAAA,GAC3B7H,MAAO;AAAA,QACPwD,KAAOvD,OAAO6H,yBAAyB9H,IAAOiC,EAAAA;AAAAA,QAChDuB,GAAM,QAAOA;AACjBxD,IAAAA,KAAQC,OAAOC,eAAeF,EAAAA;EAAAA;AAAAA;AAAAA,SAKhB+H,EAAYjG,IAAAA;AACtBA,EAAAA,GAAMsE,MACVtE,GAAMsE,IAAAA,MACFtE,GAAMuD,KACT0C,EAAYjG,GAAMuD,CAAAA;AAAAA;AAAAA,SAKL2C,EAAYlG,IAAAA;AACtBA,EAAAA,GAAMmB,MACVnB,GAAMmB,IAAQC,EAAYpB,GAAMb,CAAAA;AAAAA;ACtDlC,SAAgBgH,EACf1C,IACA1F,IACAqI,IAAAA;AAAAA,MAGMvC,KAAiB7E,EAAMjB,EAAAA,IAC1BsE,EAAU,QAAA,EAAUgE,EAAUtI,IAAOqI,EAAAA,IACrCnH,EAAMlB,EAAAA,IACNsE,EAAU,QAAA,EAAUiE,EAAUvI,IAAOqI,EAAAA,IACrC3C,GAAMW,IAAAA,SDvLT/C,IACA+E,IAAAA;AAAAA,QAEMvH,KAAUD,MAAMC,QAAQwC,EAAAA,GACxBrB,KAAoB,EACzBC,GAAOpB,KAAAA,IAAkC,GAEzCmG,GAAQoB,KAASA,GAAOpB,IAASrC,EAAAA,GAEjC2B,GAAAA,OAEAW,GAAAA,OAEAS,GAAW,CAAA,GAEXnC,GAAS6C,IAETjH,GAAOkC,IAEP6D,GAAQ,MAER/D,GAAO,MAEP2C,GAAS,MACTyC,GAAAA,MAAW,GASR1F,KAAYb,IACZwG,KAA2CC;AAC3C5H,IAAAA,OACHgC,KAAS,CAACb,EAAAA,GACVwG,KAAQE;AAAAA,QAAAA,KAGeC,MAAMC,UAAU/F,IAAQ2F,EAAAA,GAAzCK,KAAAA,GAAAA,QAAQC,KAAAA,GAAAA;AAAAA,WACf9G,GAAMkF,IAAS4B,IACf9G,GAAM8D,IAAU+C,IACTC;EAAAA,EC6Ia/I,IAAOqI,EAAAA,IACxB/D,EAAU,KAAA,EAAO0E,EAAgBhJ,IAAOqI,EAAAA;AAAAA,UAE7BA,KAASA,GAAOpB,IAASrC,EAAAA,GACjCU,EAAQ2D,KAAKnD,EAAAA,GACZA;AAAAA;AAAAA,SC9NQoD,EAAQlJ,IAAAA;AAAAA,SAClBD,EAAQC,EAAAA,KAAQb,EAAI,IAAIa,EAAAA,GAI9B,SAASmJ,GAAYnJ,IAAAA;AAAAA,QAAAA,CACfE,EAAYF,EAAAA,EAAQ,QAAOA;AAAAA,QAE5BoJ,IADEnH,KAAgCjC,GAAMC,CAAAA,GAEtCoJ,KAAW5H,EAAYzB,EAAAA;AAAAA,QACzBiC,IAAO;AAAA,UAAA,CAERA,GAAMsE,MACNtE,GAAMC,IAAQ,KAAA,CAAMoC,EAAU,KAAA,EAAOgF,EAAYrH,EAAAA,GAElD,QAAOA,GAAMb;AAEda,MAAAA,GAAMiF,IAAAA,MACNkC,KAAOG,EAAWvJ,IAAOqJ,EAAAA,GACzBpH,GAAMiF,IAAAA;IAAa,MAEnBkC,CAAAA,KAAOG,EAAWvJ,IAAOqJ,EAAAA;AAAAA,WAG1BhI,EAAK+H,IAAM,SAACvH,IAAKkF,IAAAA;AACZ9E,MAAAA,MAASK,EAAIL,GAAMb,GAAOS,EAAAA,MAASkF,MACvCxE,EAAI6G,IAAMvH,IAAKsH,GAAYpC,EAAAA,CAAAA;IAAAA,CAAAA,GAAAA,MAGrBsC,KAA4B,IAAInG,IAAIkG,EAAAA,IAAQA;EAAAA,EA3BhCpJ,EAAAA;AAAAA;AA8BpB,SAASuJ,EAAWvJ,IAAYqJ,IAAAA;AAAAA,UAEvBA,IAAAA;IAAAA,KAAAA;AAAAA,aAEC,IAAIrG,IAAIhD,EAAAA;IAAAA,KAAAA;AAAAA,aAGRa,MAAM2I,KAAKxJ,EAAAA;EAAAA;AAAAA,SAEbqD,EAAYrD,EAAAA;AAAAA;AAAAA,SClCJyJ,IAAAA;AAAAA,WA8ENC,GACRtH,IACA0B,IAAAA;AAAAA,QAEIH,KAAOH,GAAYpB,EAAAA;AAAAA,WACnBuB,KACHA,GAAKG,aAAaA,KAElBN,GAAYpB,EAAAA,IAAQuB,KAAO,EAC1BE,cAAAA,MACAC,YAAAA,IACAxB,KAAAA,WAAAA;AAAAA,UACOL,KAAQ0H,KAAK1J,CAAAA;AAAAA,aACN2J,GAAgB3H,EAAAA,GAEtByG,GAAYpG,IAAIL,IAAOG,EAAAA;IAAAA,GAE/BG,KAAAA,SAAevC,IAAAA;AAAAA,UACRiC,KAAQ0H,KAAK1J,CAAAA;AAAAA,MACN2J,GAAgB3H,EAAAA,GAE7ByG,GAAYnG,IAAIN,IAAOG,IAAMpC,EAAAA;IAAAA,EAAAA,GAIzB2D;EAAAA;AAAAA,WAICkG,GAAiBC,IAAAA;AAAAA,aAKhBpG,KAAIoG,GAAOnK,SAAS,GAAG+D,MAAK,GAAGA,MAAK;AAAA,UACtCzB,KAAkB6H,GAAOpG,EAAAA,EAAGzD,CAAAA;AAAAA,UAAAA,CAC7BgC,GAAMsE,EAAAA,SACFtE,GAAMC,GAAAA;QAAAA,KAAAA;AAER6H,UAAAA,GAAgB9H,EAAAA,KAAQiG,EAAYjG,EAAAA;AAAAA;QAAAA,KAAAA;AAGpC+H,UAAAA,GAAiB/H,EAAAA,KAAQiG,EAAYjG,EAAAA;MAAAA;IAAAA;EAAAA;AAAAA,WA6DrC+H,GAAiB/H,IAAAA;AAAAA,aAClBb,KAAiBa,GAAjBb,GAAO+F,KAAUlF,GAAVkF,GAIRzF,KAAOC,GAAQwF,EAAAA,GACZzD,KAAIhC,GAAK/B,SAAS,GAAG+D,MAAK,GAAGA,MAAK;AAAA,UACpC7B,KAAWH,GAAKgC,EAAAA;AAAAA,UAClB7B,OAAQ5B,GAAAA;AAAAA,YACNgK,KAAY7I,GAAMS,EAAAA;AAAAA,YAAAA,WAEpBoI,MAAAA,CAA4B9H,EAAIf,IAAOS,EAAAA,EAAAA,QAAAA;AACnC,YAKD7B,KAAQmH,GAAOtF,EAAAA,GACfI,KAAoBjC,MAASA,GAAMC,CAAAA;AAAAA,YACrCgC,KAAQA,GAAMb,MAAU6I,KAAAA,CAAatH,EAAG3C,IAAOiK,EAAAA,EAAAA,QAAAA;MAC3C;IAAA;AAAA,QAOJC,KAAAA,CAAAA,CAAgB9I,GAAMnB,CAAAA;AAAAA,WACrByB,GAAK/B,WAAWgC,GAAQP,EAAAA,EAAOzB,UAAUuK,KAAc,IAAI;EAAA;AAAA,WAG1DH,GAAgB9H,IAAAA;AAAAA,QACjBkF,KAAUlF,GAAVkF;AAAAA,QACHA,GAAOxH,WAAWsC,GAAMb,EAAMzB,OAAQ,QAAA;AAAO,QAS3CwK,KAAa/J,OAAO6H,yBACzBd,IACAA,GAAOxH,SAAS,CAAA;AAAA,QAGbwK,MAAAA,CAAeA,GAAW7H,IAAK,QAAA;AAAO,aAEjCoB,KAAI,GAAGA,KAAIyD,GAAOxH,QAAQ+D,KAAAA,KAAAA,CAC7ByD,GAAO5G,eAAemD,EAAAA,EAAI,QAAA;AAAO,WAAA;EAGhC;AAAA,WASCkG,GAAgB3H,IAAAA;AACpBA,IAAAA,GAAM+D,KAAU7G,EAAI,GAAGiL,KAAKC,UAAUlH,EAAOlB,EAAAA,CAAAA,CAAAA;EAAAA;AAAAA,MAxK5CuB,KAAoD,CAAA;AA2K1DkB,IAAW,OAAO,EACjBsE,GAAAA,SA5MA1F,IACA+E,IAAAA;AAAAA,QAEMvH,KAAUD,MAAMC,QAAQwC,EAAAA,GACxBwC,KAAAA,SA1BiBhF,IAAkBwC,IAAAA;AAAAA,UACrCxC,IAAS;AAAA,iBACNgF,KAAYjF,MAAMyC,GAAK3D,MAAAA,GACpB+D,KAAI,GAAGA,KAAIJ,GAAK3D,QAAQ+D,KAChCtD,QAAOkK,eAAexE,IAAO,KAAKpC,IAAGgG,GAAchG,IAAAA,IAAG,CAAA;AAAA,eAChDoC;MAAAA;AAAAA,UAEDtC,KAAcC,GAA0BH,EAAAA;AAAAA,aACvCE,GAAYvD,CAAAA;AAAAA,eACbyB,KAAOC,GAAQ6B,EAAAA,GACZE,KAAI,GAAGA,KAAIhC,GAAK/B,QAAQ+D,MAAK;AAAA,YAC/B7B,KAAWH,GAAKgC,EAAAA;AACtBF,QAAAA,GAAY3B,EAAAA,IAAO6H,GAClB7H,IACAf,MAAAA,CAAAA,CAAa0C,GAAY3B,EAAAA,EAAKiC,UAAAA;MAAAA;AAAAA,aAGzB1D,OAAO2D,OAAO3D,OAAOC,eAAeiD,EAAAA,GAAOE,EAAAA;IAAAA,EAStB1C,IAASwC,EAAAA,GAEhCrB,KAAwC,EAC7CC,GAAOpB,KAAAA,IAAgC,GACvCmG,GAAQoB,KAASA,GAAOpB,IAASrC,EAAAA,GACjC2B,GAAAA,OACAW,GAAAA,OACAS,GAAW,CAAA,GACXnC,GAAS6C,IAETjH,GAAOkC,IAEP6D,GAAQrB,IACR1C,GAAO,MACP4C,GAAAA,OACAwC,GAAAA,MAAW;AAAA,WAGZpI,OAAOkK,eAAexE,IAAO7F,GAAa,EACzCD,OAAOiC,IAEP2B,UAAAA,KAAU,CAAA,GAEJkC;EAAAA,GAkLPQ,GAAAA,SAvPAvB,IACAmB,IACAE,IAAAA;AAEKA,IAAAA,KASJrG,EAAQmG,EAAAA,KACPA,GAAOjG,CAAAA,EAA0BgH,MAAWlC,MAE7C8E,GAAiB9E,GAAMO,CAAAA,KAXnBP,GAAME,KAAAA,SAwHHsF,GAAuBC,IAAAA;AAAAA,UAC1BA,MAA4B,YAAA,OAAXA,IAAAA;AAAAA,YAChBvI,KAA8BuI,GAAOvK,CAAAA;AAAAA,YACtCgC,IAAAA;AAAAA,cACEb,KAAmCa,GAAnCb,GAAO+F,KAA4BlF,GAA5BkF,GAAQQ,KAAoB1F,GAApB0F,GAAWzF,KAASD,GAATC;AAAAA,cAAAA,MAC7BA,GAKHb,GAAK8F,IAAQ,SAAAtF,IAAAA;AACPA,YAAAA,OAAgB5B,MAAAA,WAEhBmB,GAAcS,EAAAA,KAAuBM,EAAIf,IAAOS,EAAAA,IAGzC8F,GAAU9F,EAAAA,KAErB0I,GAAuBpD,GAAOtF,EAAAA,CAAAA,KAJ9B8F,GAAU9F,EAAAA,IAAAA,MACVqG,EAAYjG,EAAAA;UAAAA,CAAAA,GAOdZ,EAAKD,IAAO,SAAAS,IAAAA;AAAAA,uBAEPsF,GAAOtF,EAAAA,KAAuBM,EAAIgF,IAAQtF,EAAAA,MAC7C8F,GAAU9F,EAAAA,IAAAA,OACVqG,EAAYjG,EAAAA;UAAAA,CAAAA;mBAGR,MAAIC,IAA8B;AAAA,gBACpC6H,GAAgB9H,EAAAA,MACnBiG,EAAYjG,EAAAA,GACZ0F,GAAUhI,SAAAA,OAGPwH,GAAOxH,SAASyB,GAAMzB,OAAAA,UAChB+D,KAAIyD,GAAOxH,QAAQ+D,KAAItC,GAAMzB,QAAQ+D,KAAKiE,CAAAA,GAAUjE,EAAAA,IAAAA;gBAAK,UAEzDA,KAAItC,GAAMzB,QAAQ+D,KAAIyD,GAAOxH,QAAQ+D,KAAKiE,CAAAA,GAAUjE,EAAAA,IAAAA;AAAK,qBAI7D+G,KAAMC,KAAKD,IAAItD,GAAOxH,QAAQyB,GAAMzB,MAAAA,GAEjC+D,KAAI,GAAGA,KAAI+G,IAAK/G,KAEnByD,CAAAA,GAAO5G,eAAemD,EAAAA,MAC1BiE,GAAUjE,EAAAA,IAAAA,OAAK,WAEZiE,GAAUjE,EAAAA,KAAkB6G,GAAuBpD,GAAOzD,EAAAA,CAAAA;UAAAA;QAAAA;MAAAA;IAAAA,EAxKvCqB,GAAMO,EAAS,CAAA,CAAA,GAGvCuE,GAAiB9E,GAAMO,CAAAA;EAAAA,GA+OxBgE,GAAAA,SAboBrH,IAAAA;AAAAA,WAAAA,MACbA,GAAMC,IACV8H,GAAiB/H,EAAAA,IACjB8H,GAAgB9H,EAAAA;EAAAA,EAAAA,CAAAA;AAAAA;AI5Jb0I,IAAAA;AAAAA,ITnFJC;ASmFID,ICvGFE,IACa,eAAA,OAAXC,UAAiD,YAAA,OAAhBA,OAAO,GAAA;ADsGxCH,ICrGKI,IAAwB,eAAA,OAARC;ADqGrBL,ICpGKM,IAAwB,eAAA,OAARC;ADoGrBP,ICnGKQ,IACK,eAAA,OAAVC,SAAAA,WACAA,MAAMC,aACM,eAAA,OAAZC;ADgGAX,IC3FKY,IAAmBV,IAC7BC,OAAOU,IAAI,eAAA,MAAA,IAAA,CAAA,GACR,eAAA,IAAA,MAAkB;ADyFhBb,IC/EKc,IAA2BZ,IACrCC,OAAOU,IAAI,iBAAA,IACV;AD6EIb,IC3EKe,IAA6Bb,IACvCC,OAAOU,IAAI,aAAA,IACV;ADyEIG,IZ5GFC,IAAS,EAAA,GACX,iBAAA,GACA,gDAAA,GACA,yDAAA,GAAA,SACDC,IAAAA;AAAAA,SAEA,yHACAA;AAAAA,GAAAA,GAGC,qHAAA,GACA,qCAAA,GACA,gEAAA,GACA,mEAAA,GACA,4FAAA,GACA,6EAAA,IACC,wCAAA,IACA,4DAAA,IACA,4DAAA,IACA,8CAAA,IACA,uEAAA,IAAA,SACDC,IAAAA;AAAAA,SACK,+CAA+CA;AAAAA,GAAAA,IAEnD,uCAAA,IAAA,SACDC,IAAAA;AAAAA,SACK,kCAAkCA;AAAAA,GAAAA,IAAAA,SAEvCC,IAAAA;AAAAA,SAAAA,qBACwBA,KAAAA,oFAAyFA,KAAAA;AAAAA,GAAAA,IAEhH,6EAAA,IAAA,SACDC,IAAAA;AAAAA,SAAAA,wJAC2JA,KAAAA;AAAAA,GAAAA,IAAAA,SAE3JA,IAAAA;AAAAA,SAAAA,qCACwCA;AAAAA,GAAAA,IAAAA,SAExCA,IAAAA;AAAAA,SAAAA,sCACyCA;AAAAA,GAAAA,IAExC,wFAAA;AYmEGN,IXzEFO,IAAmBC,KAAAA,OAAOC,UAAUC;AWyElCV,IX7CKW,KACO,eAAA,OAAZC,WAA2BA,QAAQD,UACvCC,QAAQD,UAAAA,WACDH,OAAOK,wBACd,SAAAC,IAAAA;AAAAA,SACAN,OAAOO,oBAAoBD,EAAAA,EAAKE,OAC/BR,OAAOK,sBAAsBC,EAAAA,CAAAA;AAAAA,IAEHN,OAAOO;AWqC9Bf,IXnCKiB,KACZT,OAAOS,6BACP,SAAmCC,IAAAA;AAAAA,MAE5BC,KAAW,CAAA;AAAA,SACjBR,GAAQO,EAAAA,EAAQE,QAAQ,SAAAC,IAAAA;AACvBF,IAAAA,GAAIE,EAAAA,IAAOb,OAAOc,yBAAyBJ,IAAQG,EAAAA;EAAAA,CAAAA,GAE7CF;AAAAA;AW2BDnB,IV9FFuB,KA4BF,CAAA;AUkEIvB,IPTKwB,KAAwC,EACpDC,KAAAA,SAAIC,IAAOC,IAAAA;AAAAA,MACNA,OAASC,EAAa,QAAOF;AAAAA,MAE3BG,KAASC,EAAOJ,EAAAA;AAAAA,MAAAA,CACjBK,EAAIF,IAAQF,EAAAA,EAAAA,QAwInB,SAA2BD,IAAmBG,IAAaF,IAAAA;AAAAA,QAAAA,IACpDK,KAAOC,EAAuBJ,IAAQF,EAAAA;AAAAA,WACrCK,KACJ,WAAWA,KACVA,GAAKhC,QAAAA,UAAAA,KAGLgC,GAAKP,QAAAA,WAAAA,KAAAA,SAALS,GAAUC,KAAKT,GAAMU,CAAAA,IAAAA;EACtBC,EA9IwBX,IAAOG,IAAQF,EAAAA;AAAAA,MAEnC3B,KAAQ6B,GAAOF,EAAAA;AAAAA,SACjBD,GAAMY,KAAAA,CAAeC,EAAYvC,EAAAA,IAC7BA,KAIJA,OAAUwC,EAAKd,GAAMe,GAAOd,EAAAA,KAC/Be,EAAYhB,EAAAA,GACJA,GAAMiB,EAAOhB,EAAAA,IAAeiB,EACnClB,GAAMmB,EAAOC,GACb9C,IACA0B,EAAAA,KAGK1B;AAAAA,GAER+B,KAAAA,SAAIL,IAAOC,IAAAA;AAAAA,SACHA,MAAQG,EAAOJ,EAAAA;AAAAA,GAEvBf,SAAAA,SAAQe,IAAAA;AAAAA,SACAd,QAAQD,QAAQmB,EAAOJ,EAAAA,CAAAA;AAAAA,GAE/BqB,KAAAA,SACCrB,IACAC,IACA3B,IAAAA;AAAAA,MAEMgC,KAAOC,EAAuBH,EAAOJ,EAAAA,GAAQC,EAAAA;AAAAA,MAC/CK,QAAAA,KAAAA,SAAAA,GAAMe,IAAAA,QAGTf,GAAKe,IAAIZ,KAAKT,GAAMU,GAAQpC,EAAAA,GAAAA;AACrB,MAAA,CAEH0B,GAAMsB,GAAW;AAAA,QAGfC,KAAUT,EAAKV,EAAOJ,EAAAA,GAAQC,EAAAA,GAE9BuB,KAAiCD,QAAAA,KAAAA,SAAAA,GAAUrB,CAAAA;AAAAA,QAC7CsB,MAAgBA,GAAaT,MAAUzC,GAAAA,QAC1C0B,GAAMiB,EAAOhB,EAAAA,IAAQ3B,IACrB0B,GAAMyB,EAAUxB,EAAAA,IAAAA,OAAQ;AACjB,QAEJyB,EAAGpD,IAAOiD,EAAAA,MAAAA,WAAajD,MAAuB+B,EAAIL,GAAMe,GAAOd,EAAAA,GAClE,QAAA;AACDe,MAAYhB,EAAAA,GACZ2B,EAAY3B,EAAAA;EAAAA;AAAAA,SAIXA,GAAMiB,EAAOhB,EAAAA,MAAU3B,OAAAA,WAEtBA,MAAuB2B,MAAQD,GAAMiB,MAEtCW,OAAOC,MAAMvD,EAAAA,KAAUsD,OAAOC,MAAM7B,GAAMiB,EAAOhB,EAAAA,CAAAA,MAKnDD,GAAMiB,EAAOhB,EAAAA,IAAQ3B,IACrB0B,GAAMyB,EAAUxB,EAAAA,IAAAA,OAAQ;AAJhB,GAOT6B,gBAAAA,SAAe9B,IAAOC,IAAAA;AAAAA,SAAAA,WAEjBa,EAAKd,GAAMe,GAAOd,EAAAA,KAAuBA,MAAQD,GAAMe,KAC1Df,GAAMyB,EAAUxB,EAAAA,IAAAA,OAChBe,EAAYhB,EAAAA,GACZ2B,EAAY3B,EAAAA,KAAAA,OAGLA,GAAMyB,EAAUxB,EAAAA,GAGpBD,GAAMiB,KAAAA,OAAcjB,GAAMiB,EAAMhB,EAAAA,GAAAA;AAC7B,GAIRL,0BAAAA,SAAyBI,IAAOC,IAAAA;AAAAA,MACzB8B,KAAQ3B,EAAOJ,EAAAA,GACfM,KAAOpB,QAAQU,yBAAyBmC,IAAO9B,EAAAA;AAAAA,SAChDK,KACE,EACN0B,UAAAA,MACAC,cAAAA,MAAcjC,GAAMkC,KAA2C,aAATjC,IACtDkC,YAAY7B,GAAK6B,YACjB7D,OAAOyD,GAAM9B,EAAAA,EAAAA,IALIK;AAAAA,GAQnB8B,gBAAAA,WAAAA;AACCC,IAAI,EAAA;AAAA,GAELC,gBAAAA,SAAetC,IAAAA;AAAAA,SACPlB,OAAOwD,eAAetC,GAAMe,CAAAA;AAAAA,GAEpCwB,gBAAAA,WAAAA;AACCF,IAAI,EAAA;AAAA,EAAA;AOnGE/D,IP2GFkE,KAA8C,CAAA;AACpDC,EAAK3C,IAAa,SAACH,IAAK+C,IAAAA;AAEvBF,KAAW7C,EAAAA,IAAO,WAAA;AAAA,WACjBgD,UAAU,CAAA,IAAKA,UAAU,CAAA,EAAG,CAAA,GACrBD,GAAGE,MAAMC,MAAMF,SAAAA;EAAAA;AAAAA,CAAAA,GAGxBH,GAAWV,iBAAiB,SAAS9B,IAAOC,IAAAA;AAAAA,SAC5B4B,MAAMiB,SAAS7C,EAAAA,CAAAA,KAAeoC,EAAI,EAAA,GAE1CG,GAAWnB,IAAKZ,KAAKoC,MAAM7C,IAAOC,IAAAA,MAAMU;AAAAA,GAEhD6B,GAAWnB,MAAM,SAASrB,IAAOC,IAAM3B,IAAAA;AAAAA,SACd,aAAT2B,MAAqB4B,MAAMiB,SAAS7C,EAAAA,CAAAA,KAAeoC,EAAI,EAAA,GAC/DvC,GAAYuB,IAAKZ,KAAKoC,MAAM7C,GAAM,CAAA,GAAIC,IAAM3B,IAAO0B,GAAM,CAAA,CAAA;AAAA;AAAA,ICpMpD+C,KAAb,WAAA;AAAA,WAAAC,GAKaC,IAAAA;AAAAA,QAAAA,KAAAA;AAAAA,SAAAA,IAJWC,GAAAA,KAAAA,IAAAA,MAEA,KAAA,UA4BH,SAACC,IAAWC,IAAcC,IAAAA;AAAAA,UAEzB,cAAA,OAATF,MAAyC,cAAA,OAAXC,IAAuB;AAAA,YACzDE,KAAcF;AACpBA,QAAAA,KAASD;AAAAA,YAEHI,KAAOC;AAAAA,eACN,SAENL,IAAAA;AAAAA,cAAAA,KAAAA;AAAAA,qBAAAA,OAAAA,KAAOG;AAAAA,mBAAAA,KAAAA,UAAAA,QACJG,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,IAAAA,UAAAA,EAAAA;AAAAA,iBAEIF,GAAKG,QAAQP,IAAM,SAACQ,IAAAA;AAAAA,gBAAAA;AAAAA,oBAAAA,KAAmBP,IAAO3C,KAAAA,MAAAA,IAAAA,CAAKmD,IAAMD,EAAAA,EAAAA,OAAUF,EAAAA,CAAAA;UAAAA,CAAAA;QAAAA;MAAAA;AAAAA,UAQxEI;AAAAA,UAJkB,cAAA,OAAXT,MAAuBf,EAAI,CAAA,GAAA,WAClCgB,MAAwD,cAAA,OAAlBA,MACzChB,EAAI,CAAA,GAKDxB,EAAYsC,EAAAA,GAAO;AAAA,YAChBW,KAAQC,EAAWP,EAAAA,GACnBQ,KAAQ9C,EAAYsC,IAAML,IAAAA,MAAMxC,GAClCsD,KAAAA;AAAW,YAAA;AAEdJ,UAAAA,KAAST,GAAOY,EAAAA,GAChBC,KAAAA;QAAW,UAAA;AAGPA,UAAAA,KAAUC,EAAYJ,EAAAA,IACrBK,EAAWL,EAAAA;QAAAA;AAAAA,eAEM,eAAA,OAAZM,WAA2BP,cAAkBO,UAChDP,GAAOQ,KACb,SAAAR,IAAAA;AAAAA,iBACCS,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAcV,IAAQC,EAAAA;QAAAA,GAE9B,SAAAU,IAAAA;AAAAA,gBACCN,EAAYJ,EAAAA,GACNU;QAAAA,CAAAA,KAITF,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAcV,IAAQC,EAAAA;MAAAA;AACvB,UAAA,CAAKX,MAAwB,YAAA,OAATA,IAAmB;AAAA,YAAA,YAC7CU,KAAST,GAAOD,EAAAA,OACUU,KAASV,KAC/BU,OAAWY,MAASZ,KAAAA,SACpBL,GAAKkB,KAAaC,EAAOd,IAAAA,IAAQ,GACjCR,IAAe;AAAA,cACZuB,KAAa,CAAA,GACbC,KAAc,CAAA;AACpBC,YAAU,SAAA,EAAWC,EAA4B5B,IAAMU,IAAQe,IAAGC,EAAAA,GAClExB,GAAcuB,IAAGC,EAAAA;QAAAA;AAAAA,eAEXhB;MAAAA;AACDxB,QAAI,IAAIc,EAAAA;IAAAA,GAAAA,KAAAA,qBAG0B,SAACA,IAAWC,IAAAA;AAAAA,UAEjC,cAAA,OAATD,GAAAA,QACH,SAACnD,IAAAA;AAAAA,iBAAAA,KAAAA,UAAAA,QAAeyD,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,IAAAA,UAAAA,EAAAA;AAAAA,eACtBD,GAAKwB,mBAAmBhF,IAAO,SAAC2D,IAAAA;AAAAA,iBAAeR,GAAAA,MAAAA,QAAAA,CAAKQ,EAAAA,EAAAA,OAAUF,EAAAA,CAAAA;QAAAA,CAAAA;MAAAA;AAAAA,UAG5DwB,IAAkBC,IAChBrB,KAASL,GAAKE,QAAQP,IAAMC,IAAQ,SAACwB,IAAYC,IAAAA;AACtDI,QAAAA,KAAUL,IACVM,KAAiBL;MAAAA,CAAAA;AAAAA,aAGK,eAAA,OAAZT,WAA2BP,cAAkBO,UAChDP,GAAOQ,KAAK,SAAAc,IAAAA;AAAAA,eAAa,CAACA,IAAWF,IAAUC,EAAAA;MAAAA,CAAAA,IAEhD,CAACrB,IAAQoB,IAAUC,EAAAA;IAAAA,GAzGQ,aAAA,QAAvBjC,QAAAA,KAAAA,SAAAA,GAAQmC,eAClBvC,KAAKwC,cAAcpC,GAAQmC,UAAAA,GACM,aAAA,QAAvBnC,QAAAA,KAAAA,SAAAA,GAAQqC,eAClBzC,KAAK0C,cAActC,GAAQqC,UAAAA;EAAAA;AAAAA,MAAAA,KAAAA,GAAAA;AAAAA,SAAAA,GAyG7BE,cAAA,SAAiCrC,IAAAA;AAC3BtC,MAAYsC,EAAAA,KAAOd,EAAI,CAAA,GACxBoD,EAAQtC,EAAAA,MAAOA,KAAO5B,EAAQ4B,EAAAA;AAAAA,QAC5BW,KAAQC,EAAWlB,IAAAA,GACnBmB,KAAQ9C,EAAY2B,MAAMM,IAAAA,MAAMxC;AAAAA,WACtCqD,GAAM9D,CAAAA,EAAawF,IAAAA,MACnBvB,EAAWL,EAAAA,GACJE;EAAAA,GAAAA,GAGR2B,cAAA,SACChC,IACAN,IAAAA;AAAAA,QAEMrD,KAAoB2D,MAAUA,GAAczD,CAAAA;AAAAA,IAE5CF,MAAUA,GAAM0F,KAAWrD,EAAI,CAAA,GAChCrC,GAAMY,KAAYyB,EAAI,EAAA;AAAA,QAEZyB,KAAS9D,GAAjBmB;AAAAA,WACPmD,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAAA,QAAyBT,EAAAA;EAAAA,GAAAA,GAQjCyB,gBAAA,SAAcjH,IAAAA;AAAAA,SACRoG,IAAcpG;EAAAA,GAAAA,GASpB+G,gBAAA,SAAc/G,IAAAA;AACTA,IAAAA,MAAAA,CAAU4E,KACbb,EAAI,EAAA,GAAA,KAEAuD,IAActH;EAAAA,GAAAA,GAGpBuH,eAAA,SAAkC1C,IAAS8B,IAAAA;AAAAA,QAGtCa;AAAAA,SACCA,KAAIb,GAAQc,SAAS,GAAGD,MAAK,GAAGA,MAAK;AAAA,UACnCE,KAAQf,GAAQa,EAAAA;AAAAA,UACI,MAAtBE,GAAMvH,KAAKsH,UAA6B,cAAbC,GAAMtH,IAAkB;AACtDyE,QAAAA,KAAO6C,GAAM1H;AAAAA;MAAAA;IAAAA;AAMXwH,IAAAA,KAAAA,OACHb,KAAUA,GAAQgB,MAAMH,KAAI,CAAA;AAAA,QAGvBI,KAAmBpB,EAAU,SAAA,EAAWqB;AAAAA,WAC1CV,EAAQtC,EAAAA,IAEJ+C,GAAiB/C,IAAM8B,EAAAA,IAGxBpC,KAAKa,QAAQP,IAAM,SAACQ,IAAAA;AAAAA,aAC1BuC,GAAiBvC,IAAOsB,EAAAA;IAAAA,CAAAA;EAAAA,GAAAA;AAAAA,EAxL3B;ADoMiE,IOhN3DmB,KAAQ,IAAIrD;APgN+C,IO3LpDW,KAAoB0C,GAAM1C;AP2L0B,IOpLpDsB,KAA0CoB,GAAMpB,mBAAmBqB,KAC/ED,EAAAA;APmLgE,IO3KpDb,KAAgBa,GAAMb,cAAcc,KAAKD,EAAAA;AP2KW,IOnKpDf,KAAgBe,GAAMf,cAAcgB,KAAKD,EAAAA;APmKW,IO5JpDP,KAAeO,GAAMP,aAAaQ,KAAKD,EAAAA;AP4Ja,IOtJpDZ,KAAcY,GAAMZ,YAAYa,KAAKD,EAAAA;APsJe,IO5IpDT,KAAcS,GAAMT,YAAYU,KAAKD,EAAAA;AAAAA,IAAAA,oBAAAA;;;AExFlD,IAAI,YAAY;AAEhB,SAAS,qBAAqB,QAAQ;AACpC,MAAI;AACJ,SAAO;AAAA,IACL,KAAK,SAAS,IAAI,KAAK;AACrB,UAAI,SAAS,OAAO,MAAM,KAAK,GAAG,GAAG;AACnC,eAAO,MAAM;AAAA,MACf;AAEA,aAAO;AAAA,IACT;AAAA,IACA,KAAK,SAAS,IAAI,KAAK,OAAO;AAC5B,cAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY,SAAS,aAAa;AAChC,aAAO,QAAQ,CAAC,KAAK,IAAI,CAAC;AAAA,IAC5B;AAAA,IACA,OAAO,SAAS,QAAQ;AACtB,cAAQ;AAAA,IACV;AAAA,EACF;AACF;AAEA,SAAS,eAAe,SAAS,QAAQ;AACvC,MAAI,UAAU,CAAC;AAEf,WAAS,IAAI,KAAK;AAChB,QAAI,aAAa,QAAQ,UAAU,SAAUE,QAAO;AAClD,aAAO,OAAO,KAAKA,OAAM,GAAG;AAAA,IAC9B,CAAC;AAED,QAAI,aAAa,IAAI;AACnB,UAAI,QAAQ,QAAQ,UAAU;AAE9B,UAAI,aAAa,GAAG;AAClB,gBAAQ,OAAO,YAAY,CAAC;AAC5B,gBAAQ,QAAQ,KAAK;AAAA,MACvB;AAEA,aAAO,MAAM;AAAA,IACf;AAGA,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,KAAK,OAAO;AACvB,QAAI,IAAI,GAAG,MAAM,WAAW;AAE1B,cAAQ,QAAQ;AAAA,QACd;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAI,QAAQ,SAAS,SAAS;AAC5B,gBAAQ,IAAI;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ;AACf,cAAU,CAAC;AAAA,EACb;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,IAAI,uBAAuB,SAASC,sBAAqBC,IAAGC,IAAG;AACpE,SAAOD,OAAMC;AACf;AACO,SAAS,yBAAyB,eAAe;AACtD,SAAO,SAAS,2BAA2B,MAAM,MAAM;AACrD,QAAI,SAAS,QAAQ,SAAS,QAAQ,KAAK,WAAW,KAAK,QAAQ;AACjE,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,KAAK;AAElB,aAASC,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC/B,UAAI,CAAC,cAAc,KAAKA,EAAC,GAAG,KAAKA,EAAC,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAGO,SAAS,eAAe,MAAM,wBAAwB;AAC3D,MAAI,kBAAkB,OAAO,2BAA2B,WAAW,yBAAyB;AAAA,IAC1F,eAAe;AAAA,EACjB;AACA,MAAI,wBAAwB,gBAAgB,eACxC,gBAAgB,0BAA0B,SAAS,uBAAuB,uBAC1E,wBAAwB,gBAAgB,SACxC,UAAU,0BAA0B,SAAS,IAAI,uBACjD,sBAAsB,gBAAgB;AAC1C,MAAI,aAAa,yBAAyB,aAAa;AACvD,MAAI,QAAQ,YAAY,IAAI,qBAAqB,UAAU,IAAI,eAAe,SAAS,UAAU;AAEjG,WAAS,WAAW;AAClB,QAAI,QAAQ,MAAM,IAAI,SAAS;AAE/B,QAAI,UAAU,WAAW;AAEvB,cAAQ,KAAK,MAAM,MAAM,SAAS;AAElC,UAAI,qBAAqB;AACvB,YAAI,UAAU,MAAM,WAAW;AAC/B,YAAI,gBAAgB,QAAQ,KAAK,SAAU,OAAO;AAChD,iBAAO,oBAAoB,MAAM,OAAO,KAAK;AAAA,QAC/C,CAAC;AAED,YAAI,eAAe;AACjB,kBAAQ,cAAc;AAAA,QACxB;AAAA,MACF;AAEA,YAAM,IAAI,WAAW,KAAK;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,aAAa,WAAY;AAChC,WAAO,MAAM,MAAM;AAAA,EACrB;AAEA,SAAO;AACT;;;AC/IA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,eAAe,MAAM,QAAQ,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI;AAExD,MAAI,CAAC,aAAa,MAAM,SAAU,KAAK;AACrC,WAAO,OAAO,QAAQ;AAAA,EACxB,CAAC,GAAG;AACF,QAAI,kBAAkB,aAAa,IAAI,SAAU,KAAK;AACpD,aAAO,OAAO,QAAQ,aAAa,eAAe,IAAI,QAAQ,aAAa,OAAO,OAAO;AAAA,IAC3F,CAAC,EAAE,KAAK,IAAI;AACZ,UAAM,IAAI,MAAM,oGAAoG,kBAAkB,GAAG;AAAA,EAC3I;AAEA,SAAO;AACT;AAEO,SAAS,sBAAsB,SAAS;AAC7C,WAAS,OAAO,UAAU,QAAQ,yBAAyB,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC5H,2BAAuB,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACnD;AAEA,MAAIC,kBAAiB,SAASA,kBAAiB;AAC7C,aAAS,QAAQ,UAAU,QAAQ,QAAQ,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC9F,YAAM,KAAK,IAAI,UAAU,KAAK;AAAA,IAChC;AAEA,QAAI,kBAAkB;AAEtB,QAAI;AAKJ,QAAI,wBAAwB;AAAA,MAC1B,gBAAgB;AAAA,IAClB;AAEA,QAAI,aAAa,MAAM,IAAI;AAE3B,QAAI,OAAO,eAAe,UAAU;AAClC,8BAAwB;AAExB,mBAAa,MAAM,IAAI;AAAA,IACzB;AAEA,QAAI,OAAO,eAAe,YAAY;AACpC,YAAM,IAAI,MAAM,gFAAgF,OAAO,aAAa,GAAG;AAAA,IACzH;AAIA,QAAI,wBAAwB,uBACxB,yBAAyB,sBAAsB,gBAC/C,iBAAiB,2BAA2B,SAAS,yBAAyB;AAMlF,QAAI,sBAAsB,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc;AAC1F,QAAI,eAAe,gBAAgB,KAAK;AACxC,QAAI,qBAAqB,QAAQ,MAAM,QAAQ,CAAC,SAAS,uBAAuB;AAC9E;AAEA,aAAO,WAAW,MAAM,MAAM,SAAS;AAAA,IACzC,CAAC,EAAE,OAAO,mBAAmB,CAAC;AAE9B,QAAI,WAAW,QAAQ,SAAS,sBAAsB;AACpD,UAAI,SAAS,CAAC;AACd,UAAI,SAAS,aAAa;AAE1B,eAASC,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAG/B,eAAO,KAAK,aAAaA,EAAC,EAAE,MAAM,MAAM,SAAS,CAAC;AAAA,MACpD;AAGA,oBAAc,mBAAmB,MAAM,MAAM,MAAM;AACnD,aAAO;AAAA,IACT,CAAC;AACD,WAAO,OAAO,UAAU;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,SAAS,aAAa;AAChC,eAAO;AAAA,MACT;AAAA,MACA,gBAAgB,SAAS,iBAAiB;AACxC,eAAO;AAAA,MACT;AAAA,MACA,qBAAqB,SAAS,sBAAsB;AAClD,eAAO,kBAAkB;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAGA,SAAOD;AACT;AACO,IAAI,iBAAgC,sBAAsB,cAAc;;;ACpG/E,SAAS,sBAAsB,eAAe;AAG5C,MAAI,aAAa,SAASE,YAAW,MAAM;AACzC,QAAI,WAAW,KAAK,UAChB,WAAW,KAAK;AACpB,WAAO,SAAU,MAAM;AACrB,aAAO,SAAU,QAAQ;AAGvB,YAAI,OAAO,WAAW,YAAY;AAEhC,iBAAO,OAAO,UAAU,UAAU,aAAa;AAAA,QACjD;AAGA,eAAO,KAAK,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,QAAQ,sBAAsB;AAGlC,MAAM,oBAAoB;AAC1B,IAAO,aAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AErBR,IAAM,0BAAiD,WAAA;AAAA,MAAA,OAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MACzD;AADyD,SAAA,EAAA,IAAA,UAAA,EAAA;;AAG5D,MAAM,WAAY,eAAA,MAAA,QAA0B,IAAA;AAC5C,MAAM,kBAAkB,SAAC,OAAA;AAAA,QAAA,OAAA,CAAA;aAAAC,MAAA,GAAAA,MAAA,UAAA,QAAAA,OAAmB;AAAnB,WAAAA,MAAA,CAAA,IAAA,UAAAA,GAAA;;AACvB,WAAA,SAAA,MAAA,QAAA,cAAA,CAAS,EAAQ,KAAA,IAAS,EAAQ,KAAA,IAAS,KAAA,GAAU,IAAA,CAAA;EAArD;AACF,SAAO;AAAA;AEoNF,IAAM,sBACX,OAAO,WAAW,eACjB,OAAe,uCACX,OAAe,uCAChB,WAAA;AACE,MAAI,UAAU,WAAW;AAAG,WAAO;AACnC,MAAI,OAAO,UAAU,CAAA,MAAO;AAAU,WAAO;AAC7C,SAAO,QAAQ,MAAM,MAAM,SAAA;AAAA;AAM5B,IAAM,mBAGX,OAAO,WAAW,eAAgB,OAAe,+BAC5C,OAAe,+BAChB,WAAA;AACE,SAAO,SAAU,OAAA;AACf,WAAO;EAAA;AAAA;AC9OF,SAAA,cAAuB,OAAA;AACpC,MAAI,OAAO,UAAU,YAAY,UAAU;AAAM,WAAO;AAExD,MAAI,QAAQ,OAAO,eAAe,KAAA;AAClC,MAAI,UAAU;AAAM,WAAO;AAE3B,MAAI,YAAY;AAChB,SAAO,OAAO,eAAe,SAAA,MAAe,MAAM;AAChD,gBAAY,OAAO,eAAe,SAAA;;AAGpC,SAAO,UAAU;AAAA;AEwJZ,IAAM,mBAAmB,SAC9BC,IAAA;AAEA,SAAOA,MAAK,OAAQA,GAA0B,UAAU;AAAA;ACqFnD,SAAA,aAAsB,MAAc,eAAA;AACzC,WAAA,gBAAA;AAAA,QAAA,OAAA,CAAA;aAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAA0B;AAA1B,WAAA,EAAA,IAAA,UAAA,EAAA;;AACE,QAAI,eAAe;AACjB,UAAI,WAAW,cAAA,MAAA,QAAiB,IAAA;AAChC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,wCAAA;;AAGlB,aAAO,eAAA,eAAA;QACL;QACA,SAAS,SAAS;SACd,UAAU,YAAY,EAAE,MAAM,SAAS,KAAA,CAAA,GACvC,WAAW,YAAY,EAAE,OAAO,SAAS,MAAA,CAAA;;AAGjD,WAAO,EAAE,MAAM,SAAS,KAAK,CAAA,EAAA;EAAA;AAG/B,gBAAc,WAAW,WAAA;AAAM,WAAA,KAAG;EAAH;AAE/B,gBAAc,OAAO;AAErB,gBAAc,QAAQ,SAAC,QAAA;AACrB,WAAA,OAAO,SAAS;EAAhB;AAEF,SAAO;AAAA;AAMF,SAAA,SAAkB,QAAA;AACvB,SAAO,cAAc,MAAA,KAAW,UAAU;AAAA;AAMrC,SAAA,gBACL,QAAA;AAEA,SACE,OAAO,WAAW,cAClB,UAAU,UAEV,iBAAiB,MAAA;AAAA;AAOd,SAAA,MAAe,QAAA;AAMpB,SACE,SAAS,MAAA,KACT,OAAO,OAAO,SAAS,YACvB,OAAO,KAAK,MAAA,EAAQ,MAAM,UAAA;AAAA;AAI9B,SAAA,WAAoB,KAAA;AAClB,SAAO,CAAC,QAAQ,WAAW,SAAS,MAAA,EAAQ,QAAQ,GAAA,IAAO;AAAA;AAatD,SAAA,QACL,eAAA;AAEA,SAAO,KAAG;AAAA;AC5UL,SAAA,WAAoB,MAAA;AACzB,MAAM,YAAY,QAAO,KAAG,MAAO,MAAM,GAAA,IAAO,CAAA;AAChD,MAAM,aAAa,UAAU,UAAU,SAAS,CAAA,KAAM;AACtD,SAAO,4CACL,QAAQ,aAAA,2GAEsE,aAAA,+BAAyC,aAAA;AAAA;AAGpH,SAAA,uCACL,SAAmD;AAAnD,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAmD;AAEnD,MAAI,OAAuC;AACzC,WAAO,WAAA;AAAM,aAAA,SAAC,MAAA;AAAS,eAAA,SAAC,QAAA;AAAW,iBAAA,KAAK,MAAA;QAAL;MAAZ;IAAV;;AAEP,MAAA,KAAkC,QAAA,iBAAlC,mBAAA,OAAA,SAAkB,kBAAA;AAC1B,SAAO,WAAA;AAAM,WAAA,SAAC,MAAA;AAAS,aAAA,SAAC,QAAA;AACtB,YAAI,iBAAgB,MAAA,GAAS;AAC3B,kBAAQ,KAAK,WAAW,OAAO,IAAA,CAAA;;AAEjC,eAAO,KAAK,MAAA;MAAA;IAJS;EAAV;AAIC;AC5BT,SAAA,oBAA6B,UAAkB,QAAA;AACpD,MAAI,UAAU;AACd,SAAO;IACL,aAAA,SAAeC,KAAA;AACb,UAAM,UAAU,KAAK,IAAA;AACrB,UAAI;AACF,eAAOA,IAAA;;AAEP,YAAM,WAAW,KAAK,IAAA;AACtB,mBAAW,WAAW;;IAAA;IAG1B,gBAAA,WAAA;AACE,UAAI,UAAU,UAAU;AACtB,gBAAQ,KAAQ,SAAA,WAAe,UAAA,qDAA0D,WAAA,8SAAA;;IAAA;;AAAA;AAe1F,IAAA;;EAAA,SAAA,QAAA;AAEG,cAAAC,kBAAA,MAAA;AAER,aAAAA,mBAAA;AAAA,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAe;AAAf,aAAA,EAAA,IAAA,UAAA,EAAA;;AAAA,UAAA,QAAA,OAAA,MAAA,MACW,IAAA,KAAA;AACT,aAAO,eAAe,OAAMA,iBAAgB,SAAA;;IAAA;AAAA,WAAA,eAAAA,kBAGlC,OAAO,SAAA;WAH2B,WAAA;AAI5C,eAAOA;MAAA;;;;AAUT,IAAAA,iBAAA,UAAA,SAAA,WAAA;AAAA,UAAA,MAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAU;AAAV,YAAA,EAAA,IAAA,UAAA,EAAA;;AACE,aAAO,OAAA,UAAM,OAAO,MAAM,MAAM,GAAA;IAAA;AAWlC,IAAAA,iBAAA,UAAA,UAAA,WAAA;AAAA,UAAA,MAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAW;AAAX,YAAA,EAAA,IAAA,UAAA,EAAA;;AACE,UAAI,IAAI,WAAW,KAAK,MAAM,QAAQ,IAAI,CAAA,CAAA,GAAK;AAC7C,eAAA,KAAWA,iBAAA,KAAA,MAAAA,kBAAA,cAAA,CAAA,MAAA,GAAmB,IAAI,CAAA,EAAG,OAAO,IAAA,CAAA,CAAA,GAAA;;AAE9C,aAAA,KAAWA,iBAAA,KAAA,MAAAA,kBAAA,cAAA,CAAA,MAAA,GAAmB,IAAI,OAAO,IAAA,CAAA,CAAA,GAAA;IAAA;AAAA,WAAAA;EAAA,EAlCnC,KAAA;;AAyCH,IAAA;;EAAA,SAAA,QAAA;AAEG,cAAAC,gBAAA,MAAA;AAER,aAAAA,iBAAA;AAAA,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAe;AAAf,aAAA,EAAA,IAAA,UAAA,EAAA;;AAAA,UAAA,QAAA,OAAA,MAAA,MACW,IAAA,KAAA;AACT,aAAO,eAAe,OAAMA,eAAc,SAAA;;IAAA;AAAA,WAAA,eAAAA,gBAGhC,OAAO,SAAA;WAHyB,WAAA;AAI1C,eAAOA;MAAA;;;;AAUT,IAAAA,eAAA,UAAA,SAAA,WAAA;AAAA,UAAA,MAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAU;AAAV,YAAA,EAAA,IAAA,UAAA,EAAA;;AACE,aAAO,OAAA,UAAM,OAAO,MAAM,MAAM,GAAA;IAAA;AAWlC,IAAAA,eAAA,UAAA,UAAA,WAAA;AAAA,UAAA,MAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAW;AAAX,YAAA,EAAA,IAAA,UAAA,EAAA;;AACE,UAAI,IAAI,WAAW,KAAK,MAAM,QAAQ,IAAI,CAAA,CAAA,GAAK;AAC7C,eAAA,KAAWA,eAAA,KAAA,MAAAA,gBAAA,cAAA,CAAA,MAAA,GAAiB,IAAI,CAAA,EAAG,OAAO,IAAA,CAAA,CAAA,GAAA;;AAE5C,aAAA,KAAWA,eAAA,KAAA,MAAAA,gBAAA,cAAA,CAAA,MAAA,GAAiB,IAAI,OAAO,IAAA,CAAA,CAAA,GAAA;IAAA;AAAA,WAAAA;EAAA,EAlCjC,KAAA;;AAsCH,SAAA,gBAA4B,KAAA;AACjC,SAAO,EAAY,GAAA,IAAO,kBAAgB,KAAK,WAAA;EAAM,CAAA,IAAM;AAAA;AC/G7D,IAAM,eAAwB;AAC9B,IAAM,SAAiB;AAKvB,SAAA,UAAmB,WAAgB,SAAA;AACjC,MAAI,WAAW;AACb;;AAKF,MAAI,cAAc;AAChB,UAAM,IAAI,MAAM,MAAA;;AAKlB,QAAM,IAAI,MAAS,SAAA,QAAW,WAAW,GAAA;AAAA;AAG3C,SAAA,UACE,KACA,YACA,QACA,UAAA;AAEA,SAAO,KAAK,UAAU,KAAK,aAAa,YAAY,QAAA,GAAW,MAAA;AAAA;AAGjE,SAAA,aACE,YACA,UAAA;AAEA,MAAI,QAAe,CAAA,GACjB,OAAc,CAAA;AAEhB,MAAI,CAAC;AACH,eAAW,SAAUC,IAAW,OAAA;AAC9B,UAAI,MAAM,CAAA,MAAO;AAAO,eAAO;AAC/B,aACE,iBAAiB,KAAK,MAAM,GAAG,MAAM,QAAQ,KAAA,CAAA,EAAQ,KAAK,GAAA,IAAO;IAAA;AAIvE,SAAO,SAAqB,KAAa,OAAA;AACvC,QAAI,MAAM,SAAS,GAAG;AACpB,UAAI,UAAU,MAAM,QAAQ,IAAA;AAC5B,OAAC,UAAU,MAAM,OAAO,UAAU,CAAA,IAAK,MAAM,KAAK,IAAA;AAClD,OAAC,UAAU,KAAK,OAAO,SAAS,UAAU,GAAA,IAAO,KAAK,KAAK,GAAA;AAC3D,UAAI,CAAC,MAAM,QAAQ,KAAA;AAAQ,gBAAQ,SAAU,KAAK,MAAM,KAAK,KAAA;;AACxD,YAAM,KAAK,KAAA;AAElB,WAAO,cAAc,OAAO,QAAQ,WAAW,KAAK,MAAM,KAAK,KAAA;EAAA;AAAA;AAS5D,SAAA,mBAA4B,OAAA;AACjC,SAAO,OAAO,UAAU,YAAY,SAAS,QAAQ,OAAO,SAAS,KAAA;AAAA;AAGhE,SAAA,kBACL,aACA,aACA,KAAA;AAEA,MAAM,oBAAoB,gBAAgB,aAAa,aAAa,GAAA;AACpE,SAAO;IACL,iBAAA,WAAA;AACE,aAAO,gBAAgB,aAAa,aAAa,mBAAmB,GAAA;IAAA;;AAAA;AAU1E,SAAA,gBACE,aACA,aACA,KACA,MACA,gBAA+C;AAH/C,MAAA,gBAAA,QAAA;AAAA,kBAAA,CAAA;EAA2B;AAE3B,MAAA,SAAA,QAAA;AAAA,WAAA;EAAe;AACf,MAAA,mBAAA,QAAA;AAAA,qBAAA,oBAA+C,IAAA;EAAA;AAE/C,MAAM,UAAoC,EAAE,OAAO,IAAA;AAEnD,MAAI,CAAC,YAAY,GAAA,KAAQ,CAAC,eAAe,IAAI,GAAA,GAAM;AACjD,mBAAe,IAAI,GAAA;AACnB,YAAQ,WAAW,CAAA;AAEnB,aAAW,OAAO,KAAK;AACrB,UAAM,YAAY,OAAO,OAAO,MAAM,MAAM;AAC5C,UAAI,YAAY,UAAU,YAAY,QAAQ,SAAA,MAAe,IAAI;AAC/D;;AAGF,cAAQ,SAAS,GAAA,IAAO,gBACtB,aACA,aACA,IAAI,GAAA,GACJ,SAAA;;;AAIN,SAAO;AAAA;AAKT,SAAA,gBACE,aACA,cACA,iBACA,KACA,eACA,MAAe;AAJf,MAAA,iBAAA,QAAA;AAAA,mBAAA,CAAA;EAA4B;AAG5B,MAAA,kBAAA,QAAA;AAAA,oBAAA;EAAyB;AACzB,MAAA,SAAA,QAAA;AAAA,WAAA;EAAe;AAEf,MAAM,UAAU,kBAAkB,gBAAgB,QAAQ;AAE1D,MAAM,UAAU,YAAY;AAE5B,MAAI,iBAAiB,CAAC,WAAW,CAAC,OAAO,MAAM,GAAA,GAAM;AACnD,WAAO,EAAE,YAAY,MAAM,KAAA;;AAG7B,MAAI,YAAY,OAAA,KAAY,YAAY,GAAA,GAAM;AAC5C,WAAO,EAAE,YAAY,MAAA;;AAIvB,MAAM,eAAwC,CAAA;AAC9C,WAAS,OAAO,gBAAgB,UAAU;AACxC,iBAAa,GAAA,IAAO;;AAEtB,WAAS,OAAO,KAAK;AACnB,iBAAa,GAAA,IAAO;;AAGtB,MAAM,kBAAkB,aAAa,SAAS;yBAErCC,MAAA;AACP,QAAM,aAAa,OAAO,OAAO,MAAMA,OAAMA;AAE7C,QAAI,iBAAiB;AACnB,UAAM,aAAa,aAAa,KAAK,SAAC,SAAA;AACpC,YAAI,mBAAmB,QAAQ;AAC7B,iBAAO,QAAQ,KAAK,UAAA;;AAEtB,eAAO,eAAe;MAAA,CAAA;AAExB,UAAI,YAAY;;;;AAKlB,QAAM,SAAS,gBACb,aACA,cACA,gBAAgB,SAASA,IAAA,GACzB,IAAIA,IAAA,GACJ,SACA,UAAA;AAGF,QAAI,OAAO,YAAY;sBACd,OAAA;;;AAzBX,WAAS,OAAO,cAAA;0BAAP,GAAA;;;;AA4BT,SAAO,EAAE,YAAY,MAAA;AAAA;AAuChB,SAAA,wCACL,SAAoD;AAApD,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAoD;AAEpD,MAAI,OAAuC;AACzC,WAAO,WAAA;AAAM,aAAA,SAAC,MAAA;AAAS,eAAA,SAAC,QAAA;AAAW,iBAAA,KAAK,MAAA;QAAL;MAAZ;IAAV;;AAIb,MAAA,KAIE,QAAA,aAJF,cAAA,OAAA,SAAc,qBAAA,IACd,eAGE,QAAA,cAFF,KAEE,QAAA,WAFF,YAAA,OAAA,SAAY,KAAA,IACZ,SACE,QAAA;AAGJ,iBAAe,gBAAgB;AAE/B,MAAM,QAAQ,kBAAkB,KAAK,MAAM,aAAa,YAAA;AAExD,SAAO,SAACC,KAAE;QAAA,WAAAA,IAAA;AACR,QAAI,QAAQ,SAAA;AACZ,QAAI,UAAU,MAAM,KAAA;AAEpB,QAAI;AACJ,WAAO,SAAC,MAAA;AAAS,aAAA,SAAC,QAAA;AAChB,YAAM,eAAe,oBACnB,WACA,mCAAA;AAGF,qBAAa,YAAY,WAAA;AACvB,kBAAQ,SAAA;AAER,mBAAS,QAAQ,gBAAA;AAEjB,oBAAU,MAAM,KAAA;AAEhB,oBACE,CAAC,OAAO,YACR,qEACE,OAAO,QAAQ,MAAA,2GAAA;QAAA,CAAA;AAKrB,YAAM,mBAAmB,KAAK,MAAA;AAE9B,qBAAa,YAAY,WAAA;AACvB,kBAAQ,SAAA;AAER,mBAAS,QAAQ,gBAAA;AAEjB,oBAAU,MAAM,KAAA;AAEhB,iBAAO,cACL,UACE,CAAC,OAAO,YACR,oEACE,OAAO,QAAQ,MAAA,yDACsC,UACrD,MAAA,IAAA,sEAAA;QAAA,CAAA;AAKR,qBAAa,eAAA;AAEb,eAAO;MAAA;IA3CQ;EA2CR;AAAA;AClRN,SAAA,QAAiB,KAAA;AACtB,MAAM,OAAO,OAAO;AACpB,SACE,OAAO,QACP,SAAS,YACT,SAAS,aACT,SAAS,YACT,MAAM,QAAQ,GAAA,KACd,cAAc,GAAA;AAAA;AAcX,SAAA,yBACL,OACA,MACA,gBACA,YACA,cACA,OAAA;AAJA,MAAA,SAAA,QAAA;AAAA,WAAA;EAAe;AACf,MAAA,mBAAA,QAAA;AAAA,qBAAA;EAA8C;AAE9C,MAAA,iBAAA,QAAA;AAAA,mBAAA,CAAA;EAA4B;AAG5B,MAAI;AAEJ,MAAI,CAAC,eAAe,KAAA,GAAQ;AAC1B,WAAO;MACL,SAAS,QAAQ;MACjB;;;AAIJ,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,WAAO;;AAGT,MAAI,SAAA,OAAA,SAAA,MAAO,IAAI,KAAA;AAAQ,WAAO;AAE9B,MAAM,UAAU,cAAc,OAAO,WAAW,KAAA,IAAS,OAAO,QAAQ,KAAA;AAExE,MAAM,kBAAkB,aAAa,SAAS;yBAElCD,MAAKE,cAAA;AACf,QAAM,aAAa,OAAO,OAAO,MAAMF,OAAMA;AAE7C,QAAI,iBAAiB;AACnB,UAAM,aAAa,aAAa,KAAK,SAAC,SAAA;AACpC,YAAI,mBAAmB,QAAQ;AAC7B,iBAAO,QAAQ,KAAK,UAAA;;AAEtB,eAAO,eAAe;MAAA,CAAA;AAExB,UAAI,YAAY;;;;AAKlB,QAAI,CAAC,eAAeE,YAAA,GAAc;sBACzB;QACL,SAAS;QACT,OAAOA;QAAA;;AAIX,QAAI,OAAOA,iBAAgB,UAAU;AACnC,gCAA0B,yBACxBA,cACA,YACA,gBACA,YACA,cACA,KAAA;AAGF,UAAI,yBAAyB;wBACpB,wBAAA;;;;AAjCb,WAAiC,KAAA,GAAA,YAAA,SAAA,KAAA,UAAA,QAAA,MAAA;AAAtB,QAAA,KAAA,UAAA,EAAA,GAAC,MAAA,GAAA,CAAA,GAAK,cAAA,GAAA,CAAA;0BAAL,KAAK,WAAA;;;;AAsCjB,MAAI,SAAS,eAAe,KAAA;AAAQ,UAAM,IAAI,KAAA;AAE9C,SAAO;AAAA;AAGF,SAAA,eAAwB,OAAA;AAC7B,MAAI,CAAC,OAAO,SAAS,KAAA;AAAQ,WAAO;AAEpC,WAA0B,KAAA,GAAA,KAAA,OAAO,OAAO,KAAA,GAAd,KAAA,GAAA,QAAA,MAAsB;AAAhD,QAAW,cAAA,GAAA,EAAA;AACT,QAAI,OAAO,gBAAgB,YAAY,gBAAgB;AAAM;AAE7D,QAAI,CAAC,eAAe,WAAA;AAAc,aAAO;;AAG3C,SAAO;AAAA;AAyEF,SAAA,2CACL,SAAuD;AAAvD,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAuD;AAEvD,MAAI,OAAuC;AACzC,WAAO,WAAA;AAAM,aAAA,SAAC,MAAA;AAAS,eAAA,SAAC,QAAA;AAAW,iBAAA,KAAK,MAAA;QAAL;MAAZ;IAAV;;AAGb,MAAA,KASE,QAAA,gBATF,iBAAA,OAAA,SAAiB,UAAA,IACjB,aAQE,QAAA,YAPF,KAOE,QAAA,gBAPF,iBAAA,OAAA,SAAiB,CAAA,IAAA,IACjB,KAME,QAAA,oBANF,qBAAA,OAAA,SAAqB,CAAC,YAAY,oBAAA,IAAA,IAClC,KAKE,QAAA,cALF,eAAA,OAAA,SAAe,CAAA,IAAA,IACf,KAIE,QAAA,WAJF,YAAA,OAAA,SAAY,KAAA,IACZ,KAGE,QAAA,aAHF,cAAA,OAAA,SAAc,QAAA,IACd,KAEE,QAAA,eAFF,gBAAA,OAAA,SAAgB,QAAA,IAChB,KACE,QAAA,cADF,eAAA,OAAA,SAAe,QAAA;AAGjB,MAAM,QACJ,CAAC,gBAAgB,UAAU,oBAAI,QAAA,IAAY;AAE7C,SAAO,SAAC,UAAA;AAAa,WAAA,SAAC,MAAA;AAAS,aAAA,SAAC,QAAA;AAC9B,YAAM,SAAS,KAAK,MAAA;AAEpB,YAAM,eAAe,oBACnB,WACA,sCAAA;AAGF,YACE,CAAC,iBACD,EAAE,eAAe,UAAU,eAAe,QAAQ,OAAO,IAAA,MAAU,KACnE;AACA,uBAAa,YAAY,WAAA;AACvB,gBAAM,kCAAkC,yBACtC,QACA,IACA,gBACA,YACA,oBACA,KAAA;AAGF,gBAAI,iCAAiC;AAC3B,kBAAA,UAAmB,gCAAA,SAAV,QAAU,gCAAA;AAE3B,sBAAQ,MACN,uEAAsE,UAAA,aACtE,OACA,4DACA,QACA,yIACA,6HAAA;;UAAA,CAAA;;AAMR,YAAI,CAAC,aAAa;AAChB,uBAAa,YAAY,WAAA;AACvB,gBAAM,QAAQ,SAAS,SAAA;AAEvB,gBAAM,iCAAiC,yBACrC,OACA,IACA,gBACA,YACA,cACA,KAAA;AAGF,gBAAI,gCAAgC;AAC1B,kBAAA,UAAmB,+BAAA,SAAV,QAAU,+BAAA;AAE3B,sBAAQ,MACN,uEAAsE,UAAA,aACtE,OACA,gEAC+C,OAAO,OAAA,mIAAA;;UAAA,CAAA;AAM5D,uBAAa,eAAA;;AAGf,eAAO;MAAA;IAlEsB;EAAV;AAkEZ;ANnQX,SAAA,UAAmBC,IAAA;AACjB,SAAO,OAAOA,OAAM;AAAA;AAoCf,SAAA,4BAAA;AAGL,SAAO,SAAA,4BAAqC,SAAA;AAC1C,WAAO,qBAAqB,OAAA;EAAA;AAAA;AAgBzB,SAAA,qBASL,SAAa;AAAb,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAa;AAGX,MAAA,KAIE,QAAA,OAJFC,SAAA,OAAA,SAAQ,OAAA,IACR,KAGE,QAAA,gBAHF,iBAAA,OAAA,SAAiB,OAAA,IACjB,KAEE,QAAA,mBAFF,oBAAA,OAAA,SAAoB,OAAA,IACpB,KACE,QAAA,oBADF,qBAAA,OAAA,SAAqB,OAAA;AAGvB,MAAI,kBAAkB,IAAI,gBAAA;AAE1B,MAAIA,QAAO;AACT,QAAI,UAAUA,MAAA,GAAQ;AACpB,sBAAgB,KAAK,UAAA;WAChB;AACL,sBAAgB,KACd,WAAgB,kBAAkBA,OAAM,aAAA,CAAA;;;AAK9C,MAAI,MAAuC;AACzC,QAAI,gBAAgB;AAElB,UAAI,mBAA6D,CAAA;AAEjE,UAAI,CAAC,UAAU,cAAA,GAAiB;AAC9B,2BAAmB;;AAGrB,sBAAgB,QACd,wCAAwC,gBAAA,CAAA;;AAK5C,QAAI,mBAAmB;AACrB,UAAI,sBAAmE,CAAA;AAEvE,UAAI,CAAC,UAAU,iBAAA,GAAoB;AACjC,8BAAsB;;AAGxB,sBAAgB,KACd,2CAA2C,mBAAA,CAAA;;AAG/C,QAAI,oBAAoB;AACtB,UAAI,uBAAgE,CAAA;AAEpE,UAAI,CAAC,UAAU,kBAAA,GAAqB;AAClC,+BAAuB;;AAGzB,sBAAgB,QACd,uCAAuC,oBAAA,CAAA;;;AAK7C,SAAO;AAAA;AH/GT,IAAM,gBAAgB;AAiHf,SAAA,eAKL,SAAA;AACA,MAAM,8BAA8B,0BAAA;AAE9B,MAAA,KAMF,WAAW,CAAA,GALb,KAAA,GAAA,SAAA,UAAA,OAAA,SAAU,SAAA,IACV,KAAA,GAAA,YAAA,aAAA,OAAA,SAAa,4BAAA,IAAA,IACb,KAAA,GAAA,UAAA,WAAA,OAAA,SAAW,OAAA,IACX,KAAA,GAAA,gBAAA,iBAAA,OAAA,SAAiB,SAAA,IACjB,KAAA,GAAA,WAAA,YAAA,OAAA,SAAY,SAAA;AAGd,MAAI;AAEJ,MAAI,OAAO,YAAY,YAAY;AACjC,kBAAc;aACL,cAAc,OAAA,GAAU;AACjC,kBAAc,gBAAgB,OAAA;SACzB;AACL,UAAM,IAAI,MACR,0HAAA;;AAIJ,MAAI,kBAAkB;AACtB,MAAI,OAAO,oBAAoB,YAAY;AACzC,sBAAkB,gBAAgB,2BAAA;AAElC,QAAI,CAAC,iBAAiB,CAAC,MAAM,QAAQ,eAAA,GAAkB;AACrD,YAAM,IAAI,MACR,mFAAA;;;AAIN,MACE,CAAC,iBACD,gBAAgB,KAAK,SAAC,MAAA;AAAc,WAAA,OAAO,SAAS;EAAhB,CAAgB,GACpD;AACA,UAAM,IAAI,MACR,+DAAA;;AAIJ,MAAM,qBAAoC,gBAAA,MAAA,QAAmB,eAAA;AAE7D,MAAI,eAAe;AAEnB,MAAI,UAAU;AACZ,mBAAe,oBAAoB,eAAA;MAEjC,OAAO,CAAC;OACJ,OAAO,aAAa,YAAY,QAAA,CAAA;;AAIxC,MAAM,mBAAmB,IAAI,cAAc,kBAAA;AAC3C,MAAI,iBAA4B;AAEhC,MAAI,MAAM,QAAQ,SAAA,GAAY;AAC5B,qBAAA,cAAA,CAAkB,kBAAA,GAAuB,SAAA;aAChC,OAAO,cAAc,YAAY;AAC1C,qBAAiB,UAAU,gBAAA;;AAG7B,MAAM,mBAAmB,aAAA,MAAA,QAAgB,cAAA;AAEzC,SAAO,YAAY,aAAa,gBAAgB,gBAAA;AAAA;AWxF3C,SAAA,8BACL,iBAAA;AAMA,MAAM,aAAmC,CAAA;AACzC,MAAM,iBAAwD,CAAA;AAC9D,MAAI;AACJ,MAAM,UAAU;IACd,SAAA,SACE,qBACA,SAAA;AAEA,UAAI,MAAuC;AAMzC,YAAI,eAAe,SAAS,GAAG;AAC7B,gBAAM,IAAI,MACR,6EAAA;;AAGJ,YAAI,oBAAoB;AACtB,gBAAM,IAAI,MACR,iFAAA;;;AAIN,UAAM,OACJ,OAAO,wBAAwB,WAC3B,sBACA,oBAAoB;AAC1B,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MACR,8DAAA;;AAGJ,UAAI,QAAQ,YAAY;AACtB,cAAM,IAAI,MACR,+EAAA;;AAGJ,iBAAW,IAAA,IAAQ;AACnB,aAAO;IAAA;IAET,YAAA,SACE,SACA,SAAA;AAEA,UAAI,MAAuC;AACzC,YAAI,oBAAoB;AACtB,gBAAM,IAAI,MACR,oFAAA;;;AAIN,qBAAe,KAAK,EAAE,SAAS,QAAA,CAAA;AAC/B,aAAO;IAAA;IAET,gBAAA,SAAe,SAAA;AACb,UAAI,MAAuC;AACzC,YAAI,oBAAoB;AACtB,gBAAM,IAAI,MAAM,kDAAA;;;AAGpB,2BAAqB;AACrB,aAAO;IAAA;;AAGX,kBAAgB,OAAA;AAChB,SAAO,CAAC,YAAY,gBAAgB,kBAAA;AAAA;AD7HtC,SAAA,gBAA4BD,IAAA;AAC1B,SAAO,OAAOA,OAAM;AAAA;AAOtB,IAAI,+BAA+B;AAqI5B,SAAA,cACL,cACA,sBAGA,gBACA,oBAAA;AADA,MAAA,mBAAA,QAAA;AAAA,qBAAA,CAAA;EAAgE;AAGhE,MAAI,MAAuC;AACzC,QAAI,OAAO,yBAAyB,UAAU;AAC5C,UAAI,CAAC,8BAA8B;AACjC,uCAA+B;AAC/B,gBAAQ,KACN,2LAAA;;;;AAMJ,MAAA,KACF,OAAO,yBAAyB,aAC5B,8BAA8B,oBAAA,IAC9B,CAAC,sBAAsB,gBAAgB,kBAAA,GAHxC,aAAA,GAAA,CAAA,GAAY,sBAAA,GAAA,CAAA,GAAqB,0BAAA,GAAA,CAAA;AAMtC,MAAI;AACJ,MAAI,gBAAgB,YAAA,GAAe;AACjC,sBAAkB,WAAA;AAAM,aAAA,gBAAgB,aAAA,CAAA;IAAhB;SACnB;AACL,QAAM,uBAAqB,gBAAgB,YAAA;AAC3C,sBAAkB,WAAA;AAAM,aAAA;IAAA;;AAG1B,WAAA,QAAiB,OAA2B,QAAA;AAA3B,QAAA,UAAA,QAAA;AAAA,cAAQ,gBAAA;IAAA;AACvB,QAAI,eAAA,cAAA;MACF,WAAW,OAAO,IAAA;OACf,oBACA,OAAO,SAACF,KAAE;UAAA,UAAAA,IAAA;AAAc,aAAA,QAAQ,MAAA;IAAR,CAAQ,EAChC,IAAI,SAACA,KAAE;UAAA,WAAAA,IAAA;AAAc,aAAA;IAAA,CAAA,CAAA;AAE1B,QAAI,aAAa,OAAO,SAAC,IAAA;AAAO,aAAA,CAAC,CAAC;IAAF,CAAE,EAAI,WAAW,GAAG;AAClD,qBAAe,CAAC,uBAAA;;AAGlB,WAAO,aAAa,OAAO,SAAC,eAAe,aAAA;AACzC,UAAI,aAAa;AACf,YAAI,EAAQ,aAAA,GAAgB;AAI1B,cAAM,QAAQ;AACd,cAAM,SAAS,YAAY,OAAO,MAAA;AAElC,cAAI,WAAW,QAAW;AACxB,mBAAO;;AAGT,iBAAO;mBACE,CAAC,EAAY,aAAA,GAAgB;AAGtC,cAAM,SAAS,YAAY,eAAsB,MAAA;AAEjD,cAAI,WAAW,QAAW;AACxB,gBAAI,kBAAkB,MAAM;AAC1B,qBAAO;;AAET,kBAAM,MACJ,mEAAA;;AAIJ,iBAAO;eACF;AAIL,iBAAO,kBAAgB,eAAe,SAACI,QAAA;AACrC,mBAAO,YAAYA,QAAO,MAAA;UAAA,CAAA;;;AAKhC,aAAO;IAAA,GACN,KAAA;EAAA;AAGL,UAAQ,kBAAkB;AAE1B,SAAO;AAAA;AE3RT,IAAI,gCAA+B;AA6OnC,SAAA,SAAiB,OAAe,WAAA;AAC9B,SAAU,QAAA,MAAS;AAAA;AAad,SAAA,YAKL,SAAA;AAEQ,MAAA,OAAS,QAAA;AACjB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,6CAAA;;AAGlB,MACE,OAAO,YAAY,eACnB,MACA;AACA,QAAI,QAAQ,iBAAiB,QAAW;AACtC,cAAQ,MACN,0GAAA;;;AAKN,MAAM,eACJ,OAAO,QAAQ,gBAAgB,aAC3B,QAAQ,eACR,gBAAgB,QAAQ,YAAA;AAE9B,MAAM,WAAW,QAAQ,YAAY,CAAA;AAErC,MAAM,eAAe,OAAO,KAAK,QAAA;AAEjC,MAAM,0BAAuD,CAAA;AAC7D,MAAM,0BAAuD,CAAA;AAC7D,MAAM,iBAA2C,CAAA;AAEjD,eAAa,QAAQ,SAAC,aAAA;AACpB,QAAM,0BAA0B,SAAS,WAAA;AACzC,QAAM,OAAO,SAAQ,MAAM,WAAA;AAE3B,QAAI;AACJ,QAAI;AAEJ,QAAI,aAAa,yBAAyB;AACxC,oBAAc,wBAAwB;AACtC,wBAAkB,wBAAwB;WACrC;AACL,oBAAc;;AAGhB,4BAAwB,WAAA,IAAe;AACvC,4BAAwB,IAAA,IAAQ;AAChC,mBAAe,WAAA,IAAe,kBAC1B,aAAa,MAAM,eAAA,IACnB,aAAa,IAAA;EAAA,CAAA;AAGnB,WAAA,eAAA;AACE,QAAI,MAAuC;AACzC,UAAI,OAAO,QAAQ,kBAAkB,UAAU;AAC7C,YAAI,CAAC,+BAA8B;AACjC,0CAA+B;AAC/B,kBAAQ,KACN,qMAAA;;;;AAKF,QAAA,KAKJ,OAAO,QAAQ,kBAAkB,aAC7B,8BAA8B,QAAQ,aAAA,IACtC,CAAC,QAAQ,aAAA,GANb,KAAA,GAAA,CAAA,GAAA,gBAAA,OAAA,SAAgB,CAAA,IAAA,IAChB,KAAA,GAAA,CAAA,GAAA,iBAAA,OAAA,SAAiB,CAAA,IAAA,IACjB,KAAA,GAAA,CAAA,GAAA,qBAAA,OAAA,SAAqB,SAAA;AAMvB,QAAM,oBAAoB,eAAA,eAAA,CAAA,GAAK,aAAA,GAAkB,uBAAA;AAEjD,WAAO,cAAc,cAAc,SAAC,SAAA;AAClC,eAAS,OAAO,mBAAmB;AACjC,gBAAQ,QAAQ,KAAK,kBAAkB,GAAA,CAAA;;AAEzC,eAAc,KAAA,GAAA,mBAAA,gBAAA,KAAA,iBAAA,QAAA,MAAgB;AAA9B,YAASC,KAAA,iBAAA,EAAA;AACP,gBAAQ,WAAWA,GAAE,SAASA,GAAE,OAAA;;AAElC,UAAI,oBAAoB;AACtB,gBAAQ,eAAe,kBAAA;;IAAA,CAAA;EAAA;AAK7B,MAAI;AAEJ,SAAO;IACL;IACA,SAAA,SAAQ,OAAO,QAAA;AACb,UAAI,CAAC;AAAU,mBAAW,aAAA;AAE1B,aAAO,SAAS,OAAO,MAAA;IAAA;IAEzB,SAAS;IACT,cAAc;IACd,iBAAA,WAAA;AACE,UAAI,CAAC;AAAU,mBAAW,aAAA;AAE1B,aAAO,SAAS,gBAAA;IAAA;;AAAA;ACxXf,SAAA,wBAAA;AACL,SAAO;IACL,KAAK,CAAA;IACL,UAAU,CAAA;;AAAA;AAIP,SAAA,4BAAA;AAKL,WAAA,gBAAyB,iBAAuB;AAAvB,QAAA,oBAAA,QAAA;AAAA,wBAAA,CAAA;IAAuB;AAC9C,WAAO,OAAO,OAAO,sBAAA,GAAyB,eAAA;EAAA;AAGhD,SAAO,EAAE,gBAAA;AAAA;ACTJ,SAAA,yBAAA;AAKL,WAAA,aACE,aAAA;AAEA,QAAM,YAAY,SAAC,OAAA;AAA0B,aAAA,MAAM;IAAN;AAE7C,QAAM,iBAAiB,SAAC,OAAA;AAA0B,aAAA,MAAM;IAAN;AAElD,QAAM,YAAY,wBAChB,WACA,gBACA,SAAC,KAAK,UAAA;AAAkB,aAAA,IAAI,IAAI,SAAC,IAAA;AAAO,eAAA,SAAS,EAAA;MAAT,CAAS;IAAzB,CAAyB;AAGnD,QAAM,WAAW,SAACP,IAAY,IAAA;AAAiB,aAAA;IAAA;AAE/C,QAAM,aAAa,SAAC,UAAyB,IAAA;AAAiB,aAAA,SAAS,EAAA;IAAT;AAE9D,QAAM,cAAc,wBAAwB,WAAW,SAAC,KAAA;AAAQ,aAAA,IAAI;IAAJ,CAAI;AAEpE,QAAI,CAAC,aAAa;AAChB,aAAO;QACL;QACA;QACA;QACA;QACA,YAAY,wBACV,gBACA,UACA,UAAA;;;AAKN,QAAM,2BAA2B,wBAC/B,aACA,cAAA;AAGF,WAAO;MACL,WAAW,wBAAwB,aAAa,SAAA;MAChD,gBAAgB;MAChB,WAAW,wBAAwB,aAAa,SAAA;MAChD,aAAa,wBAAwB,aAAa,WAAA;MAClD,YAAY,wBACV,0BACA,UACA,UAAA;;EAAA;AAKN,SAAO,EAAE,aAAA;AAAA;AC3DJ,SAAA,kCACL,SAAA;AAEA,MAAM,WAAW,oBAAoB,SAACA,IAAc,OAAA;AAClD,WAAA,QAAQ,KAAA;EAAR,CAAQ;AAGV,SAAO,SAAA,UACL,OAAA;AAEA,WAAO,SAAS,OAAY,MAAA;EAAA;AAAA;AAIzB,SAAA,oBACL,SAAA;AAEA,SAAO,SAAA,UACL,OACA,KAAA;AAEA,aAAA,wBACE,MAAA;AAEA,aAAO,MAAM,IAAA;IAAA;AAGf,QAAM,aAAa,SAAC,OAAA;AAClB,UAAI,wBAAwB,GAAA,GAAM;AAChC,gBAAQ,IAAI,SAAS,KAAA;aAChB;AACL,gBAAQ,KAAK,KAAA;;IAAA;AAIjB,QAAI,EAAQ,KAAA,GAAQ;AAIlB,iBAAW,KAAA;AAGX,aAAO;WACF;AAIL,aAAO,kBAAgB,OAAO,UAAA;;EAAA;AAAA;ACnD7B,SAAA,cAA0B,QAAW,UAAA;AAC1C,MAAM,MAAM,SAAS,MAAA;AAErB,MAA6C,QAAQ,QAAW;AAC9D,YAAQ,KACN,0EACA,mEACA,+BACA,QACA,kCACA,SAAS,SAAA,CAAA;;AAIb,SAAO;AAAA;AAGF,SAAA,oBACL,UAAA;AAEA,MAAI,CAAC,MAAM,QAAQ,QAAA,GAAW;AAC5B,eAAW,OAAO,OAAO,QAAA;;AAG3B,SAAO;AAAA;AAGF,SAAA,0BACL,aACA,UACA,OAAA;AAEA,gBAAc,oBAAoB,WAAA;AAElC,MAAM,QAAa,CAAA;AACnB,MAAM,UAAuB,CAAA;AAE7B,WAAqB,KAAA,GAAA,gBAAA,aAAA,KAAA,cAAA,QAAA,MAAa;AAAlC,QAAW,SAAA,cAAA,EAAA;AACT,QAAM,KAAK,cAAc,QAAQ,QAAA;AACjC,QAAI,MAAM,MAAM,UAAU;AACxB,cAAQ,KAAK,EAAE,IAAI,SAAS,OAAA,CAAA;WACvB;AACL,YAAM,KAAK,MAAA;;;AAGf,SAAO,CAAC,OAAO,OAAA;AAAA;AC9BV,SAAA,2BACL,UAAA;AAIA,WAAA,cAAuB,QAAW,OAAA;AAChC,QAAM,MAAM,cAAc,QAAQ,QAAA;AAElC,QAAI,OAAO,MAAM,UAAU;AACzB;;AAGF,UAAM,IAAI,KAAK,GAAA;AACf,UAAM,SAAS,GAAA,IAAO;EAAA;AAGxB,WAAA,eACE,aACA,OAAA;AAEA,kBAAc,oBAAoB,WAAA;AAElC,aAAqB,KAAA,GAAA,gBAAA,aAAA,KAAA,cAAA,QAAA,MAAa;AAAlC,UAAW,SAAA,cAAA,EAAA;AACT,oBAAc,QAAQ,KAAA;;EAAA;AAI1B,WAAA,cAAuB,QAAW,OAAA;AAChC,QAAM,MAAM,cAAc,QAAQ,QAAA;AAClC,QAAI,EAAE,OAAO,MAAM,WAAW;AAC5B,YAAM,IAAI,KAAK,GAAA;;AAEjB,UAAM,SAAS,GAAA,IAAO;EAAA;AAGxB,WAAA,eACE,aACA,OAAA;AAEA,kBAAc,oBAAoB,WAAA;AAClC,aAAqB,KAAA,GAAA,gBAAA,aAAA,KAAA,cAAA,QAAA,MAAa;AAAlC,UAAW,SAAA,cAAA,EAAA;AACT,oBAAc,QAAQ,KAAA;;EAAA;AAI1B,WAAA,cACE,aACA,OAAA;AAEA,kBAAc,oBAAoB,WAAA;AAElC,UAAM,MAAM,CAAA;AACZ,UAAM,WAAW,CAAA;AAEjB,mBAAe,aAAa,KAAA;EAAA;AAG9B,WAAA,iBAA0B,KAAe,OAAA;AACvC,WAAO,kBAAkB,CAAC,GAAA,GAAM,KAAA;EAAA;AAGlC,WAAA,kBAA2B,MAA2B,OAAA;AACpD,QAAI,YAAY;AAEhB,SAAK,QAAQ,SAAC,KAAA;AACZ,UAAI,OAAO,MAAM,UAAU;AACzB,eAAO,MAAM,SAAS,GAAA;AACtB,oBAAY;;IAAA,CAAA;AAIhB,QAAI,WAAW;AACb,YAAM,MAAM,MAAM,IAAI,OAAO,SAAC,IAAA;AAAO,eAAA,MAAM,MAAM;MAAZ,CAAY;;EAAA;AAIrD,WAAA,iBAA0B,OAAA;AACxB,WAAO,OAAO,OAAO;MACnB,KAAK,CAAA;MACL,UAAU,CAAA;KAAA;EAAA;AAId,WAAA,WACE,MACA,QACA,OAAA;AAEA,QAAM,YAAW,MAAM,SAAS,OAAO,EAAA;AACvC,QAAM,UAAa,OAAO,OAAO,CAAA,GAAI,WAAU,OAAO,OAAA;AACtD,QAAM,SAAS,cAAc,SAAS,QAAA;AACtC,QAAM,YAAY,WAAW,OAAO;AAEpC,QAAI,WAAW;AACb,WAAK,OAAO,EAAA,IAAM;AAClB,aAAO,MAAM,SAAS,OAAO,EAAA;;AAG/B,UAAM,SAAS,MAAA,IAAU;AAEzB,WAAO;EAAA;AAGT,WAAA,iBAA0B,QAAmB,OAAA;AAC3C,WAAO,kBAAkB,CAAC,MAAA,GAAS,KAAA;EAAA;AAGrC,WAAA,kBACE,SACA,OAAA;AAEA,QAAM,UAAsC,CAAA;AAE5C,QAAM,mBAAgD,CAAA;AAEtD,YAAQ,QAAQ,SAAC,QAAA;AAEf,UAAI,OAAO,MAAM,MAAM,UAAU;AAE/B,yBAAiB,OAAO,EAAA,IAAM;UAC5B,IAAI,OAAO;UAGX,SAAS,eAAA,eAAA,CAAA,GACH,iBAAiB,OAAO,EAAA,IACxB,iBAAiB,OAAO,EAAA,EAAI,UAC5B,IAAA,GACD,OAAO,OAAA;;;IAAA,CAAA;AAMlB,cAAU,OAAO,OAAO,gBAAA;AAExB,QAAM,oBAAoB,QAAQ,SAAS;AAE3C,QAAI,mBAAmB;AACrB,UAAM,eACJ,QAAQ,OAAO,SAAC,QAAA;AAAW,eAAA,WAAW,SAAS,QAAQ,KAAA;MAA5B,CAA4B,EAAQ,SAC/D;AAEF,UAAI,cAAc;AAChB,cAAM,MAAM,OAAO,KAAK,MAAM,QAAA;;;EAAA;AAKpC,WAAA,iBAA0B,QAAW,OAAA;AACnC,WAAO,kBAAkB,CAAC,MAAA,GAAS,KAAA;EAAA;AAGrC,WAAA,kBACE,aACA,OAAA;AAEM,QAAA,KAAmB,0BACvB,aACA,UACA,KAAA,GAHK,QAAA,GAAA,CAAA,GAAO,UAAA,GAAA,CAAA;AAMd,sBAAkB,SAAS,KAAA;AAC3B,mBAAe,OAAO,KAAA;EAAA;AAGxB,SAAO;IACL,WAAW,kCAAkC,gBAAA;IAC7C,QAAQ,oBAAoB,aAAA;IAC5B,SAAS,oBAAoB,cAAA;IAC7B,QAAQ,oBAAoB,aAAA;IAC5B,SAAS,oBAAoB,cAAA;IAC7B,QAAQ,oBAAoB,aAAA;IAC5B,WAAW,oBAAoB,gBAAA;IAC/B,YAAY,oBAAoB,iBAAA;IAChC,WAAW,oBAAoB,gBAAA;IAC/B,YAAY,oBAAoB,iBAAA;IAChC,WAAW,oBAAoB,gBAAA;IAC/B,YAAY,oBAAoB,iBAAA;;AAAA;ACnL7B,SAAA,yBACL,UACA,MAAA;AAIM,MAAA,KACJ,2BAA2B,QAAA,GADrB,YAAA,GAAA,WAAW,aAAA,GAAA,YAAY,YAAA,GAAA;AAG/B,WAAA,cAAuB,QAAW,OAAA;AAChC,WAAO,eAAe,CAAC,MAAA,GAAS,KAAA;EAAA;AAGlC,WAAA,eACE,aACA,OAAA;AAEA,kBAAc,oBAAoB,WAAA;AAElC,QAAM,SAAS,YAAY,OACzB,SAAC,OAAA;AAAU,aAAA,EAAE,cAAc,OAAO,QAAA,KAAa,MAAM;IAA1C,CAA0C;AAGvD,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,QAAQ,KAAA;;EAAA;AAIlB,WAAA,cAAuB,QAAW,OAAA;AAChC,WAAO,eAAe,CAAC,MAAA,GAAS,KAAA;EAAA;AAGlC,WAAA,eACE,aACA,OAAA;AAEA,kBAAc,oBAAoB,WAAA;AAClC,QAAI,YAAY,WAAW,GAAG;AAC5B,YAAM,aAAa,KAAA;;EAAA;AAIvB,WAAA,cACE,aACA,OAAA;AAEA,kBAAc,oBAAoB,WAAA;AAClC,UAAM,WAAW,CAAA;AACjB,UAAM,MAAM,CAAA;AAEZ,mBAAe,aAAa,KAAA;EAAA;AAG9B,WAAA,iBAA0B,QAAmB,OAAA;AAC3C,WAAO,kBAAkB,CAAC,MAAA,GAAS,KAAA;EAAA;AAGrC,WAAA,kBACE,SACA,OAAA;AAEA,QAAI,iBAAiB;AAErB,aAAmB,KAAA,GAAA,YAAA,SAAA,KAAA,UAAA,QAAA,MAAS;AAA5B,UAAS,SAAA,UAAA,EAAA;AACP,UAAM,SAAS,MAAM,SAAS,OAAO,EAAA;AACrC,UAAI,CAAC,QAAQ;AACX;;AAGF,uBAAiB;AAEjB,aAAO,OAAO,QAAQ,OAAO,OAAA;AAC7B,UAAM,QAAQ,SAAS,MAAA;AACvB,UAAI,OAAO,OAAO,OAAO;AACvB,eAAO,MAAM,SAAS,OAAO,EAAA;AAC7B,cAAM,SAAS,KAAA,IAAS;;;AAI5B,QAAI,gBAAgB;AAClB,qBAAe,KAAA;;EAAA;AAInB,WAAA,iBAA0B,QAAW,OAAA;AACnC,WAAO,kBAAkB,CAAC,MAAA,GAAS,KAAA;EAAA;AAGrC,WAAA,kBACE,aACA,OAAA;AAEM,QAAAE,MAAmB,0BACvB,aACA,UACA,KAAA,GAHK,QAAAA,IAAA,CAAA,GAAO,UAAAA,IAAA,CAAA;AAMd,sBAAkB,SAAS,KAAA;AAC3B,mBAAe,OAAO,KAAA;EAAA;AAGxB,WAAA,eAAwBM,IAAuBC,IAAA;AAC7C,QAAID,GAAE,WAAWC,GAAE,QAAQ;AACzB,aAAO;;AAGT,aAASC,KAAI,GAAGA,KAAIF,GAAE,UAAUE,KAAID,GAAE,QAAQC,MAAK;AACjD,UAAIF,GAAEE,EAAA,MAAOD,GAAEC,EAAA,GAAI;AACjB;;AAEF,aAAO;;AAET,WAAO;EAAA;AAGT,WAAA,MAAe,QAAsB,OAAA;AAEnC,WAAO,QAAQ,SAAC,OAAA;AACd,YAAM,SAAS,SAAS,KAAA,CAAA,IAAU;IAAA,CAAA;AAGpC,mBAAe,KAAA;EAAA;AAGjB,WAAA,eAAwB,OAAA;AACtB,QAAM,cAAc,OAAO,OAAO,MAAM,QAAA;AACxC,gBAAY,KAAK,IAAA;AAEjB,QAAM,eAAe,YAAY,IAAI,QAAA;AAC7B,QAAA,MAAQ,MAAA;AAEhB,QAAI,CAAC,eAAe,KAAK,YAAA,GAAe;AACtC,YAAM,MAAM;;EAAA;AAIhB,SAAO;IACL;IACA;IACA;IACA,QAAQ,oBAAoB,aAAA;IAC5B,WAAW,oBAAoB,gBAAA;IAC/B,WAAW,oBAAoB,gBAAA;IAC/B,QAAQ,oBAAoB,aAAA;IAC5B,SAAS,oBAAoB,cAAA;IAC7B,QAAQ,oBAAoB,aAAA;IAC5B,SAAS,oBAAoB,cAAA;IAC7B,YAAY,oBAAoB,iBAAA;IAChC,YAAY,oBAAoB,iBAAA;;AAAA;ACpJ7B,SAAA,oBACL,SAGI;AAHJ,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAGI;AAEE,MAAA,KAAkD,eAAA;IACtD,cAAc;IACd,UAAU,SAAC,UAAA;AAAkB,aAAA,SAAS;IAAT;KAC1B,OAAA,GAHG,WAAA,GAAA,UAAU,eAAA,GAAA;AAMlB,MAAM,eAAe,0BAAA;AACrB,MAAM,mBAAmB,uBAAA;AACzB,MAAM,eAAe,eACjB,yBAAyB,UAAU,YAAA,IACnC,2BAA2B,QAAA;AAE/B,SAAO,eAAA,eAAA,eAAA;IACL;IACA;KACG,YAAA,GACA,gBAAA,GACA,YAAA;AAAA;ACrCP,IAAI,cACF;AAMK,IAAI,SAAS,SAAC,MAAO;AAAP,MAAA,SAAA,QAAA;AAAA,WAAA;EAAO;AAC1B,MAAI,KAAK;AAET,MAAIA,KAAI;AACR,SAAOA,MAAK;AAEV,UAAM,YAAa,KAAK,OAAA,IAAW,KAAM,CAAA;;AAE3C,SAAO;AAAA;ACqCT,IAAM,mBAAiD;EACrD;EACA;EACA;EACA;;AAGF,IAAA;;EAAA,2BAAA;AAME,aAAAC,iBACkB,SACA,MAAA;AADA,WAAA,UAAA;AACA,WAAA,OAAA;IAAA;AAAA,WAAAA;EAAA,EAAA;;AAIpB,IAAA;;EAAA,2BAAA;AAME,aAAAC,iBACkB,SACA,MAAA;AADA,WAAA,UAAA;AACA,WAAA,OAAA;IAAA;AAAA,WAAAA;EAAA,EAAA;;AAUb,IAAM,qBAAqB,SAAC,OAAA;AACjC,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,QAAM,cAA+B,CAAA;AACrC,aAAuB,KAAA,GAAA,qBAAA,kBAAA,KAAA,mBAAA,QAAA,MAAkB;AAAzC,UAAW,WAAA,mBAAA,EAAA;AACT,UAAI,OAAO,MAAM,QAAA,MAAc,UAAU;AACvC,oBAAY,QAAA,IAAY,MAAM,QAAA;;;AAIlC,WAAO;;AAGT,SAAO,EAAE,SAAS,OAAO,KAAA,EAAA;AAAA;AA8WpB,IAAM,mBAAoB,WAAA;AAC/B,WAAA,kBAKE,YACA,gBAKA,SAAA;AAOA,QAAM,YAIF,aACF,aAAa,cACb,SACE,SACA,WACA,KACA,MAAA;AACI,aAAA;QACJ;QACA,MAAM,cAAA,eAAA,CAAA,GACC,QAAgB,CAAA,CAAA,GADjB;UAEJ;UACA;UACA,eAAe;SAAA;;IANb,CAMa;AAKrB,QAAM,UACJ,aACE,aAAa,YACb,SAAC,WAAmB,KAAe,MAAA;AAAwB,aAAA;QACzD,SAAS;QACT,MAAM,cAAA,eAAA,CAAA,GACC,QAAgB,CAAA,CAAA,GADjB;UAEJ;UACA;UACA,eAAe;SAAA;;IANwC,CAMxC;AAKvB,QAAM,WACJ,aACE,aAAa,aACb,SACE,OACA,WACA,KACA,SACA,MAAA;AACI,aAAA;QACJ;QACA,QAAS,WAAW,QAAQ,kBAAmB,oBAC7C,SAAS,UAAA;QAEX,MAAM,cAAA,eAAA,CAAA,GACC,QAAgB,CAAA,CAAA,GADjB;UAEJ;UACA;UACA,mBAAmB,CAAC,CAAC;UACrB,eAAe;UACf,UAAS,SAAA,OAAA,SAAA,MAAO,UAAS;UACzB,YAAW,SAAA,OAAA,SAAA,MAAO,UAAS;SAAA;;IAZzB,CAYyB;AAKnC,QAAI,mBAAmB;AAEvB,QAAM,KACJ,OAAO,oBAAoB,cACvB;;MAAA,WAAA;AACA,iBAAA,UAAA;AACE,eAAA,SAAS;YACP,SAAS;YACT,kBAAA,WAAA;YAAmB;YACnB,eAAA,WAAA;AACE,qBAAO;YAAA;YAET,SAAA,WAAA;YAAU;YACV,qBAAA,WAAA;YAAsB;YACtB,QAAQ;YACR,gBAAA,WAAA;YAAiB;;QAAA;AAEnB,gBAAA,UAAA,QAAA,WAAA;AACE,cAAI,MAAuC;AACzC,gBAAI,CAAC,kBAAkB;AACrB,iCAAmB;AACnB,sBAAQ,KACN,iOAAA;;;QAAA;AAAA,eAAA;MAAA,EAAA;;AAQhB,aAAA,cACE,KAAA;AAEA,aAAO,SAAC,UAAU,UAAU,OAAA;AAC1B,YAAM,aAAY,WAAA,OAAA,SAAA,QAAS,eACvB,QAAQ,YAAY,GAAA,IACpB,OAAA;AAEJ,YAAM,kBAAkB,IAAI,GAAA;AAC5B,YAAI;AAEJ,YAAI,UAAU;AACd,iBAAA,MAAe,QAAA;AACb,wBAAc;AACd,0BAAgB,MAAA;QAAA;AAGlB,YAAM,WAAW,WAAA;AAAkB,iBAAA,QAAA,MAAA,MAAA,WAAA;;;;;;AAG3B,qCAAkB,KAAA,WAAA,OAAA,SAAA,QAAS,cAAT,OAAA,SAAA,GAAA,KAAA,SAAqB,KAAK,EAAE,UAAU,MAAA,CAAA;uBACxD,WAAW,eAAA,EAAX,QAAA,CAAA,GAAA,CAAA;AACgB,yBAAA,CAAA,GAAM,eAAA;;AAAxB,oCAAkB,GAAA,KAAA;;;AAGpB,sBAAI,oBAAoB,SAAS,gBAAgB,OAAO,SAAS;AAE/D,0BAAM;sBACJ,MAAM;sBACN,SAAS;;;AAGb,4BAAU;AAEJ,mCAAiB,IAAI,QAAe,SAACZ,IAAG,QAAA;AAC5C,2BAAA,gBAAgB,OAAO,iBAAiB,SAAS,WAAA;AAC/C,6BAAA,OAAO;wBACL,MAAM;wBACN,SAAS,eAAe;uBAAA;oBAF1B,CAE0B;kBAH5B,CAG4B;AAI9B,2BACE,QACE,WACA,MACA,KAAA,WAAA,OAAA,SAAA,QAAS,mBAAT,OAAA,SAAA,GAAA,KAAA,SACE,EAAE,WAAW,IAAA,GACb,EAAE,UAAU,MAAA,CAAA,CAAA,CAAA;AAIJ,yBAAA,CAAA,GAAM,QAAQ,KAAK;oBAC/B;oBACA,QAAQ,QACN,eAAe,KAAK;sBAClB;sBACA;sBACA;sBACA;sBACA,QAAQ,gBAAgB;sBACxB;sBACA,iBAAkB,SAChB,OACA,MAAA;AAEA,+BAAO,IAAI,gBAAgB,OAAO,IAAA;sBAAA;sBAEpC,kBAAmB,SAAC,OAAgB,MAAA;AAClC,+BAAO,IAAI,gBAAgB,OAAO,IAAA;sBAAA;qBAAA,CAAA,EAGtC,KAAK,SAAC,QAAA;AACN,0BAAI,kBAAkB,iBAAiB;AACrC,8BAAM;;AAER,0BAAI,kBAAkB,iBAAiB;AACrC,+BAAO,UAAU,OAAO,SAAS,WAAW,KAAK,OAAO,IAAA;;AAE1D,6BAAO,UAAU,QAAe,WAAW,GAAA;oBAAA,CAAA;mBAAA,CAAA;;AA3B/C,gCAAc,GAAA,KAAA;;;;AA+Bd,gCACE,iBAAe,kBACX,SAAS,MAAM,WAAW,KAAK,MAAI,SAAS,MAAI,IAAA,IAChD,SAAS,OAAY,WAAW,GAAA;;;AAOlC,iCACJ,WACA,CAAC,QAAQ,8BACT,SAAS,MAAM,WAAA,KACd,YAAoB,KAAK;AAE5B,sBAAI,CAAC,cAAc;AACjB,6BAAS,WAAA;;AAEX,yBAAA,CAAA,GAAO,WAAA;;;WAAA;QAAA,EAAA;AAET,eAAO,OAAO,OAAO,UAAyB;UAC5C;UACA;UACA;UACA,QAAA,WAAA;AACE,mBAAO,SAAQ,KAAU,YAAA;UAAA;SAAA;MAAA;IAAA;AAMjC,WAAO,OAAO,OACZ,eAKA;MACE;MACA;MACA;MACA;KAAA;EAAA;AAIN,oBAAiB,YAAY,WAAA;AAAM,WAAA;EAAA;AAEnC,SAAO;AAAA,EAAA;AAiBF,SAAA,aACL,QAAA;AAEA,MAAI,OAAO,QAAQ,OAAO,KAAK,mBAAmB;AAChD,UAAM,OAAO;;AAEf,MAAI,OAAO,OAAO;AAChB,UAAM,OAAO;;AAEf,SAAO,OAAO;AAAA;AAOhB,SAAA,WAAoB,OAAA;AAClB,SACE,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,MAAM,SAAS;AAAA;ACxtB1B,IAAM,UAAU,SAAC,SAAuB,QAAA;AACtC,MAAI,iBAAiB,OAAA,GAAU;AAC7B,WAAO,QAAQ,MAAM,MAAA;SAChB;AACL,WAAO,QAAQ,MAAA;;AAAA;AAaZ,SAAA,UAAA;AAAA,MAAA,WAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MACF;AADE,aAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,SAAO,SAAC,QAAA;AACN,WAAO,SAAS,KAAK,SAAC,SAAA;AAAY,aAAA,QAAQ,SAAS,MAAA;IAAjB,CAAiB;EAAA;AAAA;AAahD,SAAA,UAAA;AAAA,MAAA,WAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MACF;AADE,aAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,SAAO,SAAC,QAAA;AACN,WAAO,SAAS,MAAM,SAAC,SAAA;AAAY,aAAA,QAAQ,SAAS,MAAA;IAAjB,CAAiB;EAAA;AAAA;AAUjD,SAAA,2BACL,QACA,aAAA;AAEA,MAAI,CAAC,UAAU,CAAC,OAAO;AAAM,WAAO;AAEpC,MAAM,oBAAoB,OAAO,OAAO,KAAK,cAAc;AAC3D,MAAM,wBACJ,YAAY,QAAQ,OAAO,KAAK,aAAA,IAAiB;AAEnD,SAAO,qBAAqB;AAAA;AAG9B,SAAA,kBAA2BQ,IAAA;AACzB,SACE,OAAOA,GAAE,CAAA,MAAO,cAChB,aAAaA,GAAE,CAAA,KACf,eAAeA,GAAE,CAAA,KACjB,cAAcA,GAAE,CAAA;AAAA;AAwCb,SAAA,YAAA;AAAA,MAAA,cAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAEF;AAFE,gBAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,SAAC,QAAA;AAAgB,aAAA,2BAA2B,QAAQ,CAAC,SAAA,CAAA;IAApC;;AAG1B,MAAI,CAAC,kBAAkB,WAAA,GAAc;AACnC,WAAO,UAAA,EAAY,YAAY,CAAA,CAAA;;AAGjC,SAAO,SACL,QAAA;AAGA,QAAM,WAA8C,YAAY,IAC9D,SAAC,YAAA;AAAe,aAAA,WAAW;IAAX,CAAW;AAG7B,QAAM,kBAAkB,QAAA,MAAA,QAAW,QAAA;AAEnC,WAAO,gBAAgB,MAAA;EAAA;AAAA;AA0CpB,SAAA,aAAA;AAAA,MAAA,cAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAEF;AAFE,gBAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,SAAC,QAAA;AAAgB,aAAA,2BAA2B,QAAQ,CAAC,UAAA,CAAA;IAApC;;AAG1B,MAAI,CAAC,kBAAkB,WAAA,GAAc;AACnC,WAAO,WAAA,EAAa,YAAY,CAAA,CAAA;;AAGlC,SAAO,SACL,QAAA;AAGA,QAAM,WAA8C,YAAY,IAC9D,SAAC,YAAA;AAAe,aAAA,WAAW;IAAX,CAAW;AAG7B,QAAM,kBAAkB,QAAA,MAAA,QAAW,QAAA;AAEnC,WAAO,gBAAgB,MAAA;EAAA;AAAA;AA+CpB,SAAA,sBAAA;AAAA,MAAA,cAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAEF;AAFE,gBAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,MAAM,UAAU,SAAC,QAAA;AACf,WAAO,UAAU,OAAO,QAAQ,OAAO,KAAK;EAAA;AAG9C,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,SAAC,QAAA;AACN,UAAM,kBAAkB,QAAQ,WAAA,MAAA,QAAc,WAAA,GAAc,OAAA;AAE5D,aAAO,gBAAgB,MAAA;IAAA;;AAI3B,MAAI,CAAC,kBAAkB,WAAA,GAAc;AACnC,WAAO,oBAAA,EAAsB,YAAY,CAAA,CAAA;;AAG3C,SAAO,SACL,QAAA;AAEA,QAAM,kBAAkB,QAAQ,WAAA,MAAA,QAAc,WAAA,GAAc,OAAA;AAE5D,WAAO,gBAAgB,MAAA;EAAA;AAAA;AA0CpB,SAAA,cAAA;AAAA,MAAA,cAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAEF;AAFE,gBAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,SAAC,QAAA;AAAgB,aAAA,2BAA2B,QAAQ,CAAC,WAAA,CAAA;IAApC;;AAG1B,MAAI,CAAC,kBAAkB,WAAA,GAAc;AACnC,WAAO,YAAA,EAAc,YAAY,CAAA,CAAA;;AAGnC,SAAO,SACL,QAAA;AAGA,QAAM,WAA8C,YAAY,IAC9D,SAAC,YAAA;AAAe,aAAA,WAAW;IAAX,CAAW;AAG7B,QAAM,kBAAkB,QAAA,MAAA,QAAW,QAAA;AAEnC,WAAO,gBAAgB,MAAA;EAAA;AAAA;AAiDpB,SAAA,qBAAA;AAAA,MAAA,cAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAEF;AAFE,gBAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,SAAC,QAAA;AACN,aAAA,2BAA2B,QAAQ,CAAC,WAAW,aAAa,UAAA,CAAA;IAA5D;;AAGJ,MAAI,CAAC,kBAAkB,WAAA,GAAc;AACnC,WAAO,mBAAA,EAAqB,YAAY,CAAA,CAAA;;AAG1C,SAAO,SACL,QAAA;AAGA,QAAM,WAA8C,CAAA;AAEpD,aAAyBb,MAAA,GAAA,gBAAA,aAAAA,MAAA,cAAA,QAAAA,OAAa;AAAtC,UAAW,aAAA,cAAAA,GAAA;AACT,eAAS,KACP,WAAW,SACX,WAAW,UACX,WAAW,SAAA;;AAIf,QAAM,kBAAkB,QAAA,MAAA,QAAW,QAAA;AAEnC,WAAO,gBAAgB,MAAA;EAAA;AAAA;ACpapB,IAAM,iBAG0C,SACrD,MACA,UAAA;AAEA,MAAI,OAAO,SAAS,YAAY;AAC9B,UAAM,IAAI,UAAa,WAAA,oBAAA;;AAAA;AAIpB,IAAM,OAAO,WAAA;AAAM;AAEnB,IAAM,iBAAiB,SAC5B,UACA,SAAU;AAAV,MAAA,YAAA,QAAA;AAAA,cAAA;EAAU;AAEV,WAAQ,MAAM,OAAA;AAEd,SAAO;AAAA;AAGF,IAAM,yBAAyB,SACpC,aACA,UAAA;AAEA,cAAY,iBAAiB,SAAS,UAAU,EAAE,MAAM,KAAA,CAAA;AACxD,SAAO,WAAA;AAAM,WAAA,YAAY,oBAAoB,SAAS,QAAA;EAAzC;AAAyC;AAajD,IAAM,4BAA4B,SACvC,iBACA,QAAA;AAIA,MAAM,SAAS,gBAAgB;AAE/B,MAAI,OAAO,SAAS;AAClB;;AAOF,MAAI,EAAE,YAAY,SAAS;AACzB,WAAO,eAAe,QAAQ,UAAU;MACtC,YAAY;MACZ,OAAO;MACP,cAAc;MACd,UAAU;KAAA;;AAId;AAAE,kBAAgB,MAAkC,MAAA;AAAA;AClEtD,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,YAAY;AAGX,IAAM,gBAAgB,UAAQ;AAC9B,IAAM,gBAAgB,UAAQ;AAC9B,IAAM,oBAAuB,WAAA,MAAY;AACzC,IAAM,oBAAuB,WAAA,MAAY;AAEzC,IAAA;;EAAA,2BAAA;AAGL,aAAAkB,gBAAmB,MAAA;AAAA,WAAA,OAAA;AAFnB,WAAA,OAAO;AAGL,WAAK,UAAa,OAAA,MAAQ,YAAA,eAAsB,OAAA;IAAA;AAAA,WAAAA;EAAA,EAAA;;ACP7C,IAAM,iBAAiB,SAAC,QAAA;AAC7B,MAAI,OAAO,SAAS;AAClB,UAAM,IAAI,eAAgB,OAAyC,MAAA;;AAAA;AAShE,SAAA,eACL,QACA,UAAA;AAEA,MAAI,UAAU;AACd,SAAO,IAAI,QAAW,SAAC,SAAS,QAAA;AAC9B,QAAM,kBAAkB,WAAA;AAAM,aAAA,OAAO,IAAI,eAAe,OAAO,MAAA,CAAA;IAAjC;AAE9B,QAAI,OAAO,SAAS;AAClB,sBAAA;AACA;;AAGF,cAAU,uBAAuB,QAAQ,eAAA;AACzC,aAAQ,QAAQ,WAAA;AAAM,aAAA,QAAA;IAAA,CAAA,EAAW,KAAK,SAAS,MAAA;EAAA,CAAA,EAC9C,QAAQ,WAAA;AAET,cAAU;EAAA,CAAA;AAAA;AAWP,IAAM,UAAU,SACrB,OACA,SAAA;AAC2B,SAAA,QAAA,QAAA,MAAA,WAAA;;;;;;AAEzB,iBAAA,CAAA,GAAM,QAAQ,QAAA,CAAA;;AAAd,aAAA,KAAA;AACc,iBAAA,CAAA,GAAM,MAAA,CAAA;;AAAd,kBAAQ,GAAA,KAAA;AACd,iBAAA,CAAA,GAAO;YACL,QAAQ;YACR;WAAA;;;AAGF,iBAAA,CAAA,GAAO;YACL,QAAQ,mBAAiB,iBAAiB,cAAc;YACxD,OAAA;WAAA;;AAGF,qBAAA,OAAA,SAAA,QAAA;;;;;;;;;;;;GAAA;AAdyB;AAyBtB,IAAM,cAAc,SAAI,QAAA;AAC7B,SAAO,SAAC,UAAA;AACN,WAAO,eACL,eAAe,QAAQ,QAAA,EAAS,KAAK,SAAC,QAAA;AACpC,qBAAe,MAAA;AACf,aAAO;IAAA,CAAA,CAAA;EAAA;AAAA;AAYR,IAAM,cAAc,SAAC,QAAA;AAC1B,MAAM,QAAQ,YAAkB,MAAA;AAChC,SAAO,SAAC,WAAA;AACN,WAAO,MAAM,IAAI,QAAc,SAAC,SAAA;AAAY,aAAA,WAAW,SAAS,SAAA;IAApB,CAAoB,CAAA;EAAA;AAAA;ACxB5D,IAAA,SAAW,OAAA;AAInB,IAAM,qBAAqB,CAAA;AAE3B,IAAM,MAAM;AAEZ,IAAM,aAAa,SACjB,mBACA,wBAAA;AAEA,MAAM,kBAAkB,SAAC,YAAA;AACvB,WAAA,uBAAuB,mBAAmB,WAAA;AACxC,aAAA,0BAA0B,YAAY,kBAAkB,MAAA;IAAxD,CAAwD;EAD1D;AAIF,SAAO,SACL,cACA,MAAA;AAEA,mBAAe,cAAc,cAAA;AAC7B,QAAM,uBAAuB,IAAI,gBAAA;AAEjC,oBAAgB,oBAAA;AAEhB,QAAM,SAAS,QACb,WAAA;AAAwB,aAAA,QAAA,QAAA,MAAA,WAAA;;;;;AACtB,6BAAe,iBAAA;AACf,6BAAe,qBAAqB,MAAA;AACpB,qBAAA,CAAA,GAAM,aAAa;gBACjC,OAAO,YAAY,qBAAqB,MAAA;gBACxC,OAAO,YAAY,qBAAqB,MAAA;gBACxC,QAAQ,qBAAqB;eAAA,CAAA;;AAHzB,wBAAU,GAAA,KAAA;AAKhB,6BAAe,qBAAqB,MAAA;AACpC,qBAAA,CAAA,GAAO,OAAA;;;OAAA;IATe,GAWxB,WAAA;AAAM,aAAA,0BAA0B,sBAAsB,aAAA;IAAhD,CAAgD;AAGxD,QAAI,QAAA,OAAA,SAAA,KAAM,UAAU;AAClB,6BAAuB,KAAK,MAAA;;AAG9B,WAAO;MACL,QAAQ,YAA2B,iBAAA,EAAmB,MAAA;MACtD,QAAA,WAAA;AACE,kCAA0B,sBAAsB,aAAA;MAAA;;EAAA;AAAA;AAMxD,IAAM,oBAAoB,SACxB,gBAKA,QAAA;AASA,MAAM,OAAO,SACX,WACA,SAAA;AACG,WAAA,QAAA,QAAA,MAAA,WAAA;;;;;AACH,2BAAe,MAAA;AAGX,0BAAmC,WAAA;YAAM;AAEvC,2BAAe,IAAI,QAA2B,SAAC,SAAS,QAAA;AAE5D,kBAAI,gBAAgB,eAAe;gBACjC;gBACA,QAAQ,SAAC,QAAQ,aAAA;AAEf,8BAAY,YAAA;AAEZ,0BAAQ;oBACN;oBACA,YAAY,SAAA;oBACZ,YAAY,iBAAA;mBAAA;gBAAA;eAAA;AAIlB,4BAAc,WAAA;AACZ,8BAAA;AACA,uBAAA;cAAA;YAAA,CAAA;AAIE,uBAA2D;cAC/D;;AAGF,gBAAI,WAAW,MAAM;AACnB,uBAAS,KACP,IAAI,QAAc,SAAC,SAAA;AAAY,uBAAA,WAAW,SAAS,SAAS,IAAA;cAA7B,CAA6B,CAAA;;;;;AAK/C,mBAAA,CAAA,GAAM,eAAe,QAAQ,QAAQ,KAAK,QAAA,CAAA,CAAA;;AAAnD,qBAAS,GAAA,KAAA;AAEf,2BAAe,MAAA;AACf,mBAAA,CAAA,GAAO,MAAA;;AAGP,wBAAA;;;;;;;;;;;;KAAA;EA5CC;AAgDL,SAAQ,SAAC,WAAoC,SAAA;AAC3C,WAAA,eAAe,KAAK,WAAW,OAAA,CAAA;EAA/B;AAA+B;AAGnC,IAAM,4BAA4B,SAAC,SAAA;AAC3B,MAAA,OAAoD,QAAA,MAA9C,gBAA8C,QAAA,eAA/B,UAA+B,QAAA,SAAtB,YAAsB,QAAA,WAAX,SAAW,QAAA;AAE1D,MAAI,MAAM;AACR,gBAAY,aAAa,IAAA,EAAM;aACtB,eAAe;AACxB,WAAO,cAAe;AACtB,gBAAY,cAAc;aACjB,SAAS;AAClB,gBAAY;aACH,WAAW;SAEf;AACL,UAAM,IAAI,MACR,yFAAA;;AAIJ,iBAAe,QAAQ,kBAAA;AAEvB,SAAO,EAAE,WAAW,MAAM,OAAA;AAAA;AAIrB,IAAM,sBAAyD,SACpE,SAAA;AAEM,MAAA,KAA8B,0BAA0B,OAAA,GAAtD,OAAA,GAAA,MAAM,YAAA,GAAA,WAAW,SAAA,GAAA;AAEzB,MAAM,KAAK,OAAA;AACX,MAAM,QAAgC;IACpC;IACA;IACA;IACA;IACA,SAAS,oBAAI,IAAA;IACb,aAAa,WAAA;AACX,YAAM,IAAI,MAAM,6BAAA;IAAA;;AAIpB,SAAO;AAAA;AAGT,IAAM,wBAAwB,SAC5B,OAAA;AAEA,QAAM,QAAQ,QAAQ,SAAC,YAAA;AACrB,8BAA0B,YAAY,iBAAA;EAAA,CAAA;AAAA;AAI1C,IAAM,gCAAgC,SACpC,aAAA;AAEA,SAAO,WAAA;AACL,gBAAY,QAAQ,qBAAA;AAEpB,gBAAY,MAAA;EAAA;AAAA;AAWhB,IAAM,oBAAoB,SACxB,cACA,eACA,WAAA;AAEA,MAAI;AACF,iBAAa,eAAe,SAAA;WACrB,mBAAP;AAGA,eAAW,WAAA;AACT,YAAM;IAAA,GACL,CAAA;;AAAA;AAOA,IAAM,cAAc,aACtB,MAAA,MAAA;AAME,IAAM,oBAAoB,aAAgB,MAAA,YAAA;AAK1C,IAAM,iBAAiB,aACzB,MAAA,SAAA;AAGL,IAAM,sBAA4C,WAAA;AAAA,MAAA,OAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAI;AAAJ,SAAA,EAAA,IAAA,UAAA,EAAA;;AAChD,UAAQ,MAAA,MAAR,SAAA,cAAA,CAAiB,MAAA,QAAA,GAAgB,IAAA,CAAA;AAAA;AAM5B,SAAA,yBAIL,mBAAoE;AAJ/D,MAAA,QAAA;AAIL,MAAA,sBAAA,QAAA;AAAA,wBAAA,CAAA;EAAoE;AACpE,MAAM,cAAc,oBAAI,IAAA;AAChB,MAAA,QAAyC,kBAAA,OAAlC,KAAkC,kBAAA,SAAlC,UAAA,OAAA,SAAU,sBAAA;AAEzB,iBAAe,SAAS,SAAA;AAExB,MAAM,cAAc,SAAC,OAAA;AACnB,UAAM,cAAc,WAAA;AAAM,aAAA,YAAY,OAAO,MAAO,EAAA;IAA1B;AAE1B,gBAAY,IAAI,MAAM,IAAI,KAAA;AAC1B,WAAO,SAAC,eAAA;AACN,YAAM,YAAA;AACN,UAAI,iBAAA,OAAA,SAAA,cAAe,cAAc;AAC/B,8BAAsB,KAAA;;IAAA;EAAA;AAK5B,MAAM,oBAAoB,SACxB,YAAA;AAEA,aAAoB,KAAA,GAAAX,MAAA,MAAM,KAAK,YAAY,OAAA,CAAA,GAAvB,KAAAA,IAAA,QAAA,MAAkC;AAAtD,UAAW,QAAAA,IAAA,EAAA;AACT,UAAI,WAAW,KAAA,GAAQ;AACrB,eAAO;;;AAIX,WAAO;EAAA;AAGT,MAAM,iBAAiB,SAAC,SAAA;AACtB,QAAI,QAAQ,kBACV,SAAC,eAAA;AAAkB,aAAA,cAAc,WAAW,QAAQ;IAAjC,CAAiC;AAGtD,QAAI,CAAC,OAAO;AACV,cAAQ,oBAAoB,OAAA;;AAG9B,WAAO,YAAY,KAAA;EAAA;AAGrB,MAAM,gBAAgB,SACpB,SAAA;AAEM,QAAAA,MAA8B,0BAA0B,OAAA,GAAtD,OAAAA,IAAA,MAAM,SAAAA,IAAA,QAAQ,YAAAA,IAAA;AAEtB,QAAM,QAAQ,kBAAkB,SAAC,QAAA;AAC/B,UAAM,uBACJ,OAAO,SAAS,WACZ,OAAM,SAAS,OACf,OAAM,cAAc;AAE1B,aAAO,wBAAwB,OAAM,WAAW;IAAA,CAAA;AAGlD,QAAI,OAAO;AACT,YAAM,YAAA;AACN,UAAI,QAAQ,cAAc;AACxB,8BAAsB,KAAA;;;AAI1B,WAAO,CAAC,CAAC;EAAA;AAGX,MAAM,iBAAiB,SACrB,OACA,QACA,KACA,kBAAA;AACG,WAAA,QAAA,OAAA,MAAA,WAAA;;;;;AACG,qCAAyB,IAAI,gBAAA;AAC7B,mBAAO,kBACX,gBACA,uBAAuB,MAAA;AAEnB,+BAAmC,CAAA;;;;AAGvC,kBAAM,QAAQ,IAAI,sBAAA;AAClB,mBAAA,CAAA,GAAM,QAAQ,QACZ,MAAM,OACJ,QAEA,OAAO,CAAA,GAAI,KAAK;cACd;cACA,WAAW,SACT,WACA,SAAA;AACG,uBAAA,KAAK,WAAW,OAAA,EAAS,KAAK,OAAA;cAA9B;cACL;cACA,OAAO,YAAY,uBAAuB,MAAA;cAC1C,OAAO,YAAiB,uBAAuB,MAAA;cAC/C;cACA,QAAQ,uBAAuB;cAC/B,MAAM,WAAW,uBAAuB,QAAQ,gBAAA;cAChD,aAAa,MAAM;cACnB,WAAW,WAAA;AACT,4BAAY,IAAI,MAAM,IAAI,KAAA;cAAA;cAE5B,uBAAuB,WAAA;AACrB,sBAAM,QAAQ,QAAQ,SAAC,YAAYF,IAAG,KAAA;AACpC,sBAAI,eAAe,wBAAwB;AACzC,8CAA0B,YAAY,iBAAA;AACtC,wBAAI,OAAO,UAAA;;gBAAA,CAAA;cAAA;aAAA,CAAA,CAAA,CAAA;;AAxBvB,YAAAE,IAAA,KAAA;;;;AAgCA,gBAAI,EAAE,2BAAyB,iBAAiB;AAC9C,gCAAkB,SAAS,iBAAe;gBACxC,UAAU;eAAA;;;;AAId,mBAAA,CAAA,GAAM,QAAQ,WAAW,gBAAA,CAAA;;AAAzB,YAAAA,IAAA,KAAA;AAEA,sCAA0B,wBAAwB,iBAAA;AAClD,kBAAM,QAAQ,OAAO,sBAAA;;;;;;;;;;;;KAAA;EAnDpB;AAuDL,MAAM,0BAA0B,8BAA8B,WAAA;AAE9D,MAAM,aACJ,SAAC,KAAA;AAAQ,WAAA,SAAC,MAAA;AAAS,aAAA,SAAC,QAAA;AAClB,YAAI,CAAC,SAAS,MAAA,GAAS;AAErB,iBAAO,KAAK,MAAA;;AAGd,YAAI,YAAY,MAAM,MAAA,GAAS;AAC7B,iBAAO,eAAe,OAAO,OAAA;;AAG/B,YAAI,kBAAkB,MAAM,MAAA,GAAS;AACnC,kCAAA;AACA;;AAGF,YAAI,eAAe,MAAM,MAAA,GAAS;AAChC,iBAAO,cAAc,OAAO,OAAA;;AAI9B,YAAI,gBAA+C,IAAI,SAAA;AAIvD,YAAM,mBAAmB,WAAA;AACvB,cAAI,kBAAkB,oBAAoB;AACxC,kBAAM,IAAI,MACL,MAAA,qDAAA;;AAIP,iBAAO;QAAA;AAGT,YAAI;AAEJ,YAAI;AAEF,mBAAS,KAAK,MAAA;AAEd,cAAI,YAAY,OAAO,GAAG;AACxB,gBAAI,eAAe,IAAI,SAAA;AAEvB,gBAAM,kBAAkB,MAAM,KAAK,YAAY,OAAA,CAAA;AAC/C,qBAAkB,KAAA,GAAA,oBAAA,iBAAA,KAAA,kBAAA,QAAA,MAAiB;AAAnC,kBAAS,QAAA,kBAAA,EAAA;AACP,kBAAI,cAAc;AAElB,kBAAI;AACF,8BAAc,MAAM,UAAU,QAAQ,cAAc,aAAA;uBAC7C,gBAAP;AACA,8BAAc;AAEd,kCAAkB,SAAS,gBAAgB;kBACzC,UAAU;iBAAA;;AAId,kBAAI,CAAC,aAAa;AAChB;;AAGF,6BAAe,OAAO,QAAQ,KAAK,gBAAA;;;;AAKvC,0BAAgB;;AAGlB,eAAO;MAAA;IArEU;EAAV;AAwEX,SAAO;IACL;IACA;IACA;IACA,gBAAgB;;AAAA;ACngBb,IAAM,mBAAmB;AAEzB,IAAM,qBACX,WAAA;AACA,SAAA,SAAC,SAAA;;AAA+C,WAAA;MAC9C;MACA,OAAA,KAAA,CAAA,GAAM,GAAG,gBAAA,IAAmB,MAAA;;EAFkB;AAAhD;AAOF,IAAI;AACJ,IAAM,qBACJ,OAAO,mBAAmB,aACtB,eAAe,KACb,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cAClB,SACA,UAAA,IAGN,SAAC,IAAA;AACE,UAAA,YAAY,UAAU,QAAQ,QAAA,IAAY,KAAK,EAAA,EAAI,MAAM,SAAC,KAAA;AACzD,WAAA,WAAW,WAAA;AACT,YAAM;IAAA,GACL,CAAA;EAFH,CAEG;AAHJ;AAMT,IAAM,uBAAuB,SAAC,SAAA;AAC5B,SAAO,SAAC,QAAA;AACN,eAAW,QAAQ,OAAA;EAAA;AAAA;AAMvB,IAAM,MACJ,OAAO,WAAW,eAAe,OAAO,wBACpC,OAAO,wBACP,qBAAqB,EAAA;AA8BpB,IAAM,oBACX,SAAC,SAAoC;AAApC,MAAA,YAAA,QAAA;AAAA,cAAA,EAA8B,MAAM,MAAA;EAAA;AACrC,SAAA,SAAC,MAAA;AACD,WAAA,WAAA;AAAA,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAI;AAAJ,aAAA,EAAA,IAAA,UAAA,EAAA;;AACE,UAAM,QAAQ,KAAA,MAAA,QAAQ,IAAA;AAEtB,UAAI,YAAY;AAChB,UAAI,0BAA0B;AAC9B,UAAI,qBAAqB;AAEzB,UAAM,YAAY,oBAAI,IAAA;AAEtB,UAAM,gBACJ,QAAQ,SAAS,SACb,qBACA,QAAQ,SAAS,QACjB,MACA,QAAQ,SAAS,aACjB,QAAQ,oBACR,qBAAqB,QAAQ,OAAA;AAEnC,UAAM,kBAAkB,WAAA;AAGtB,6BAAqB;AACrB,YAAI,yBAAyB;AAC3B,oCAA0B;AAC1B,oBAAU,QAAQ,SAACY,IAAA;AAAM,mBAAAA,GAAA;UAAA,CAAA;;MAAA;AAI7B,aAAO,OAAO,OAAO,CAAA,GAAI,OAAO;QAG9B,WAAA,SAAU,WAAA;AAKR,cAAM,kBAAmC,WAAA;AAAM,mBAAA,aAAa,UAAA;UAAb;AAC/C,cAAM,cAAc,MAAM,UAAU,eAAA;AACpC,oBAAU,IAAI,SAAA;AACd,iBAAO,WAAA;AACL,wBAAA;AACA,sBAAU,OAAO,SAAA;UAAA;QAAA;QAKrB,UAAA,SAAS,QAAA;AAzHf,cAAA;AA0HQ,cAAI;AAGF,wBAAY,GAAC,KAAA,UAAA,OAAA,SAAA,OAAQ,SAAR,OAAA,SAAA,GAAe,gBAAA;AAG5B,sCAA0B,CAAC;AAC3B,gBAAI,yBAAyB;AAI3B,kBAAI,CAAC,oBAAoB;AACvB,qCAAqB;AACrB,8BAAc,eAAA;;;AASlB,mBAAO,MAAM,SAAS,MAAA;;AAGtB,wBAAY;;QAAA;OAAA;IAAA;EAxEpB;AADA;A7BlDF,EAAA;", "names": ["die", "error", "args", "e", "errors", "msg", "apply", "Error", "length", "map", "s", "join", "isDraft", "value", "DRAFT_STATE", "isDraftable", "proto", "Object", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "constructor", "Function", "toString", "objectCtorString", "Array", "isArray", "DRAFTABLE", "_value$constructor", "isMap", "isSet", "original", "base_", "each", "obj", "iter", "enumerableOnly", "getArchtype", "keys", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "thing", "state", "type_", "has", "prop", "prototype", "get", "set", "propOrOldValue", "t", "add", "is", "x", "y", "target", "hasMap", "Map", "hasSet", "Set", "latest", "copy_", "shallowCopy", "base", "slice", "descriptors", "getOwnPropertyDescriptors", "i", "desc", "writable", "configurable", "enumerable", "create", "freeze", "deep", "isFrozen", "clear", "delete", "dontMutateFrozenCollections", "getPlugin", "pluginKey", "plugin", "plugins", "loadPlugin", "implementation", "getCurrentScope", "currentScope", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "drafts_", "revokeDraft", "parent_", "enterScope", "immer", "immer_", "canAutoFreeze_", "unfinalizedDrafts_", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "useProxies_", "willFinalizeES5_", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "NOTHING", "undefined", "rootScope", "path", "childValue", "finalizeProperty", "scope_", "finalized_", "draft_", "resultEach", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "res", "assigned_", "concat", "autoFreeze_", "peek", "getDescriptorFromProto", "source", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "prepareCopy", "createProxy", "parent", "proxyMap_", "proxySet_", "isManual_", "traps", "objectTraps", "arrayTraps", "Proxy", "revocable", "revoke", "proxy", "createES5Proxy_", "push", "current", "currentImpl", "copy", "archType", "hasChanges_", "copyHelper", "from", "enableES5", "proxyProperty", "this", "assertUnrevoked", "mark<PERSON><PERSON>esSweep", "drafts", "hasArrayChanges", "hasObjectChanges", "baseValue", "baseIsDraft", "descriptor", "JSON", "stringify", "defineProperty", "mark<PERSON>hangesRecursively", "object", "min", "Math", "value", "currentScope", "hasSymbol", "Symbol", "hasMap", "Map", "hasSet", "Set", "hasProxies", "Proxy", "revocable", "Reflect", "NOTHING", "for", "DRAFTABLE", "DRAFT_STATE", "value", "errors", "data", "path", "op", "plugin", "thing", "objectCtorString", "Object", "prototype", "constructor", "ownKeys", "Reflect", "getOwnPropertySymbols", "obj", "getOwnPropertyNames", "concat", "getOwnPropertyDescriptors", "target", "res", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "plugins", "objectTraps", "get", "state", "prop", "DRAFT_STATE", "source", "latest", "has", "desc", "getDescriptorFromProto", "_desc$get", "call", "draft_", "undefined", "finalized_", "isDraftable", "peek", "base_", "prepareCopy", "copy_", "createProxy", "scope_", "immer_", "set", "modified_", "current", "currentState", "assigned_", "is", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "deleteProperty", "owner", "writable", "configurable", "type_", "enumerable", "defineProperty", "die", "getPrototypeOf", "setPrototypeOf", "arrayTraps", "each", "fn", "arguments", "apply", "this", "parseInt", "Immer", "e", "config", "hasProxies", "base", "recipe", "patchListener", "defaultBase", "self", "_this", "args", "produce", "draft", "_this2", "result", "scope", "enterScope", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "revokeScope", "leaveScope", "Promise", "then", "usePatchesInScope", "processResult", "error", "NOTHING", "autoFreeze_", "freeze", "p", "ip", "getPlugin", "generateReplacementPatches_", "produceWithPatches", "patches", "inversePatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "isDraft", "isManual_", "finishDraft", "useProxies_", "applyPatches", "i", "length", "patch", "slice", "applyPatchesImpl", "applyPatches_", "immer", "bind", "entry", "defaultEqualityCheck", "a", "b", "i", "createSelector", "i", "middleware", "_i", "v", "fn", "MiddlewareArray", "EnhancerArray", "_", "key", "_c", "nestedV<PERSON>ue", "x", "thunk", "draft", "m", "a", "b", "i", "RejectWithValue", "FulfillWithMeta", "TaskAbortError", "l"]}