import express from 'express';
import { body, query, param } from 'express-validator';
import { authenticate, checkFeature } from '../middleware/auth.js';
import { validateRequest } from '../middleware/validation.js';
import { uploadToMemory, handleUploadError } from '../middleware/fileUpload.js';
import ServiceCategory from '../models/ServiceCategory.js';
import {
  getServiceCategories,
  getActiveServiceCategories,
  getServiceCategoryById,
  createServiceCategory,
  updateServiceCategory,
  deleteServiceCategory,
  getServiceCategoriesStats,
  exportServiceCategories,
  importServiceCategories,
  downloadServiceCategoryTemplate
} from '../controllers/serviceCategoryController.js';

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * components:
 *   schemas:
 *     ServiceCategory:
 *       type: object
 *       required:
 *         - serviceCategory
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         serviceCategory:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *         serviceStatus:
 *           type: integer
 *           enum: [0, 1]
 *           default: 1
 *         styleView:
 *           type: integer
 *           default: 1
 *         form:
 *           type: object
 *           description: Dynamic form configuration
 *         description:
 *           type: string
 *         icon:
 *           type: string
 *         color:
 *           type: string
 *           default: "#3B82F6"
 *         sortOrder:
 *           type: integer
 *           default: 0
 *         isDefault:
 *           type: boolean
 *           default: false
 */

/**
 * @swagger
 * /api/service-categories:
 *   get:
 *     summary: Get all service categories with pagination
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *     responses:
 *       200:
 *         description: Service categories retrieved successfully
 */
router.get('/',
  checkFeature('services'),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional().isInt({ min: 0, max: 1 }).withMessage('Status must be 0 or 1'),
  ],
  validateRequest,
  getServiceCategories
);

/**
 * @swagger
 * /api/service-categories/active:
 *   get:
 *     summary: Get active service categories for dropdown
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active service categories retrieved successfully
 */
router.get('/active', checkFeature('services'), getActiveServiceCategories);

/**
 * @swagger
 * /api/service-categories/stats:
 *   get:
 *     summary: Get service category statistics
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Service category statistics retrieved successfully
 */
router.get('/stats',
  checkFeature('services'),
  getServiceCategoriesStats
);

/**
 * @swagger
 * /api/service-categories/export:
 *   get:
 *     summary: Export service categories
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, xlsx]
 *           default: xlsx
 *         description: Export format
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: Filter by status (0=inactive, 1=active)
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for category names
 *     responses:
 *       200:
 *         description: Service categories exported successfully
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/export',
  checkFeature('services'),
  [
    query('format').optional().isIn(['csv', 'xlsx']).withMessage('Format must be csv or xlsx'),
    query('status').optional().isInt({ min: 0, max: 1 }).withMessage('Status must be 0 or 1'),
    query('search').optional().isString().withMessage('Search must be a string'),
  ],
  validateRequest,
  exportServiceCategories
);

/**
 * @swagger
 * /api/service-categories/template:
 *   get:
 *     summary: Download service category import template
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Template downloaded successfully
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/template',
  checkFeature('services'),
  downloadServiceCategoryTemplate
);

/**
 * @swagger
 * /api/service-categories/{id}:
 *   get:
 *     summary: Get service category by ID
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Service category retrieved successfully
 *       404:
 *         description: Service category not found
 */
router.get('/:id',
  checkFeature('services'),
  [
    param('id').isUUID().withMessage('Service Category ID must be a valid UUID'),
  ],
  validateRequest,
  getServiceCategoryById
);

/**
 * @swagger
 * /api/service-categories:
 *   post:
 *     summary: Create a new service category
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceCategory
 *             properties:
 *               serviceCategory:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *               description:
 *                 type: string
 *               icon:
 *                 type: string
 *               color:
 *                 type: string
 *               sortOrder:
 *                 type: integer
 *               form:
 *                 type: object
 *               styleView:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Service category created successfully
 *       400:
 *         description: Validation error or category already exists
 */
router.post('/',
  checkFeature('services'),
  [
    body('serviceCategory')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Service category name must be between 2 and 100 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters'),
    body('icon')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Icon must not exceed 100 characters'),
    body('color')
      .optional()
      .matches(/^#[0-9A-Fa-f]{6}$/)
      .withMessage('Color must be a valid hex color'),
    body('sortOrder')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Sort order must be a non-negative integer'),
    body('styleView')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Style view must be a positive integer'),
  ],
  validateRequest,
  createServiceCategory
);

/**
 * @swagger
 * /api/service-categories/{id}:
 *   put:
 *     summary: Update service category
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceCategory'
 *     responses:
 *       200:
 *         description: Service category updated successfully
 *       404:
 *         description: Service category not found
 */
router.put('/:id',
  checkFeature('services'),
  [
    param('id').isUUID().withMessage('Service Category ID must be a valid UUID'),
    body('serviceCategory')
      .optional()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Service category name must be between 2 and 100 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters'),
    body('icon')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Icon must not exceed 100 characters'),
    body('color')
      .optional()
      .matches(/^#[0-9A-Fa-f]{6}$/)
      .withMessage('Color must be a valid hex color'),
    body('sortOrder')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Sort order must be a non-negative integer'),
    body('styleView')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Style view must be a positive integer'),
    body('serviceStatus')
      .optional()
      .isInt({ min: 0, max: 1 })
      .withMessage('Service status must be 0 or 1'),
  ],
  validateRequest,
  updateServiceCategory
);

/**
 * @swagger
 * /api/service-categories/{id}:
 *   delete:
 *     summary: Delete service category
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Service category deleted successfully
 *       400:
 *         description: Cannot delete category with associated services
 *       404:
 *         description: Service category not found
 */
router.delete('/:id',
  checkFeature('services'),
  [
    param('id').isUUID().withMessage('Service Category ID must be a valid UUID'),
  ],
  validateRequest,
  deleteServiceCategory
);

/**
 * @swagger
 * /api/service-categories/import:
 *   post:
 *     summary: Import service categories from file
 *     tags: [Service Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV or Excel file containing service categories
 *     responses:
 *       200:
 *         description: Service categories imported successfully
 *       400:
 *         description: Invalid file or validation errors
 */
router.post('/import',
  checkFeature('services'),
  uploadToMemory.single('file'),
  handleUploadError,
  importServiceCategories
);

export default router;
