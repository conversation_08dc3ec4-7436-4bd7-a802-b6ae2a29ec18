import React, { useEffect, useState } from 'react';
import {
  Squares2X2Icon,
  UserGroupIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChartBarIcon,
  CubeIcon
} from '@heroicons/react/24/outline';

/**
 * Category Statistics Component
 * 
 * Displays real-time counts for Service Categories and Customer Categories
 * matching the old project design and functionality
 */
const CategoryStats = ({ activeTab }) => {
  const [stats, setStats] = useState({
    serviceCategories: {
      totalCategories: 0,
      activeCategories: 0,
      inactiveCategories: 0,
      categoriesWithServices: 0,
      totalServices: 0,
      categoriesWithoutServices: 0
    },
    customerCategories: {
      totalCategories: 0,
      activeCategories: 0,
      inactiveCategories: 0,
      categoriesWithCustomers: 0,
      categoriesWithoutCustomers: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch service categories stats
  const fetchServiceCategoriesStats = async () => {
    try {
      const response = await fetch('/api/service_categories/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('tracknew_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch service categories stats');
      }

      const data = await response.json();
      if (data.success) {
        setStats(prev => ({
          ...prev,
          serviceCategories: data.data
        }));
      }
    } catch (err) {
      console.error('Error fetching service categories stats:', err);
      setError(err.message);
    }
  };

  // Fetch customer categories stats
  const fetchCustomerCategoriesStats = async () => {
    try {
      const response = await fetch('/api/customer-categories/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('tracknew_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch customer categories stats');
      }

      const data = await response.json();
      if (data.success) {
        setStats(prev => ({
          ...prev,
          customerCategories: data.data
        }));
      }
    } catch (err) {
      console.error('Error fetching customer categories stats:', err);
      setError(err.message);
    }
  };

  // Fetch stats on component mount and when activeTab changes
  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      setError(null);
      
      try {
        if (activeTab === 'service-categories' || activeTab === 'all') {
          await fetchServiceCategoriesStats();
        }
        if (activeTab === 'customer-categories' || activeTab === 'all') {
          await fetchCustomerCategoriesStats();
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [activeTab]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <p className="text-red-600 text-sm">Error loading statistics: {error}</p>
      </div>
    );
  }

  // Service Categories Stats Cards
  const serviceStatsCards = [
    {
      title: 'Total Categories',
      value: stats.serviceCategories.totalCategories,
      icon: Squares2X2Icon,
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Active Categories',
      value: stats.serviceCategories.activeCategories,
      icon: CheckCircleIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Inactive Categories',
      value: stats.serviceCategories.inactiveCategories,
      icon: XCircleIcon,
      color: 'bg-red-500',
      textColor: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      title: 'With Services',
      value: stats.serviceCategories.categoriesWithServices,
      icon: CubeIcon,
      color: 'bg-purple-500',
      textColor: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Total Services',
      value: stats.serviceCategories.totalServices,
      icon: ChartBarIcon,
      color: 'bg-indigo-500',
      textColor: 'text-indigo-600',
      bgColor: 'bg-indigo-50'
    },
    {
      title: 'Without Services',
      value: stats.serviceCategories.categoriesWithoutServices,
      icon: XCircleIcon,
      color: 'bg-gray-500',
      textColor: 'text-gray-600',
      bgColor: 'bg-gray-50'
    }
  ];

  // Customer Categories Stats Cards
  const customerStatsCards = [
    {
      title: 'Total Categories',
      value: stats.customerCategories.totalCategories,
      icon: UserGroupIcon,
      color: 'bg-teal-500',
      textColor: 'text-teal-600',
      bgColor: 'bg-teal-50'
    },
    {
      title: 'Active Categories',
      value: stats.customerCategories.activeCategories,
      icon: CheckCircleIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'Inactive Categories',
      value: stats.customerCategories.inactiveCategories,
      icon: XCircleIcon,
      color: 'bg-red-500',
      textColor: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      title: 'With Customers',
      value: stats.customerCategories.categoriesWithCustomers,
      icon: UserGroupIcon,
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      title: 'Without Customers',
      value: stats.customerCategories.categoriesWithoutCustomers,
      icon: XCircleIcon,
      color: 'bg-gray-500',
      textColor: 'text-gray-600',
      bgColor: 'bg-gray-50'
    }
  ];

  const getStatsCards = () => {
    if (activeTab === 'service-categories') {
      return serviceStatsCards;
    } else if (activeTab === 'customer-categories') {
      return customerStatsCards;
    }
    return [];
  };

  const statsCards = getStatsCards();

  if (statsCards.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow mb-6">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {activeTab === 'service-categories' ? 'Service Categories' : 'Customer Categories'} Statistics
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {statsCards.map((card, index) => {
            const Icon = card.icon;
            return (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 ${card.bgColor} rounded-md p-2`}>
                    <Icon className={`h-5 w-5 ${card.textColor}`} />
                  </div>
                  <div className="ml-3 flex-1 min-w-0">
                    <p className="text-xs font-medium text-gray-500 truncate">{card.title}</p>
                    <p className="text-lg font-semibold text-gray-900">{card.value}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default CategoryStats;
