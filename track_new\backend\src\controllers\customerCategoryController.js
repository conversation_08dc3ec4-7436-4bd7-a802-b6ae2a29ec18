import pkg from 'pg';
const { Pool } = pkg;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/tracknew?sslmode=disable'
});

/**
 * Customer Category Controller
 * 
 * Handles CRUD operations for customer categories
 */

// Get all customer categories with pagination and search
const getCustomerCategories = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      sortBy = 'sortOrder',
      sortOrder = 'ASC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // Build search condition
    let searchCondition = '';
    let queryParams = [];
    let paramIndex = 1;

    if (search) {
      searchCondition = `WHERE category_name ILIKE $${paramIndex} OR description ILIKE $${paramIndex + 1}`;
      queryParams.push(`%${search}%`, `%${search}%`);
      paramIndex += 2;
    }

    // Add limit and offset parameters
    queryParams.push(limit, offset);

    // Build sort condition
    const validSortFields = ['category_name', 'sort_order', 'created_at', 'is_active'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'sort_order';
    const sortDirection = sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM customer_categories 
      ${searchCondition}
    `;
    
    const countParams = search ? [`%${search}%`, `%${search}%`] : [];
    const countResult = await pool.query(countQuery, countParams);
    const totalItems = parseInt(countResult.rows[0].total);

    // Get categories
    const query = `
      SELECT 
        id,
        category_name,
        description,
        color,
        is_active,
        sort_order,
        discount_percentage,
        special_terms,
        created_at,
        updated_at
      FROM customer_categories 
      ${searchCondition}
      ORDER BY ${sortField} ${sortDirection}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    const result = await pool.query(query, queryParams);

    // Calculate pagination
    const totalPages = Math.ceil(totalItems / limit);

    res.json({
      success: true,
      data: {
        categories: result.rows.map(row => ({
          id: row.id,
          categoryName: row.category_name,
          description: row.description,
          color: row.color,
          isActive: row.is_active,
          sortOrder: row.sort_order,
          discountPercentage: row.discount_percentage,
          specialTerms: row.special_terms,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching customer categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer categories',
      error: error.message
    });
  }
};

// Get single customer category by ID
const getCustomerCategoryById = async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        id,
        category_name,
        description,
        color,
        is_active,
        sort_order,
        discount_percentage,
        special_terms,
        created_at,
        updated_at
      FROM customer_categories 
      WHERE id = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer category not found'
      });
    }

    const row = result.rows[0];
    res.json({
      success: true,
      data: {
        id: row.id,
        categoryName: row.category_name,
        description: row.description,
        color: row.color,
        isActive: row.is_active,
        sortOrder: row.sort_order,
        discountPercentage: row.discount_percentage,
        specialTerms: row.special_terms,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }
    });
  } catch (error) {
    console.error('Error fetching customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer category',
      error: error.message
    });
  }
};

// Create new customer category
const createCustomerCategory = async (req, res) => {
  try {
    const {
      categoryName,
      description = '',
      color = '#10B981',
      isActive = true,
      sortOrder = 0,
      discountPercentage = 0,
      specialTerms = ''
    } = req.body;

    // Validation
    if (!categoryName || categoryName.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Category name is required'
      });
    }

    if (discountPercentage < 0 || discountPercentage > 100) {
      return res.status(400).json({
        success: false,
        message: 'Discount percentage must be between 0 and 100'
      });
    }

    // Check if category name already exists
    const existingQuery = 'SELECT id FROM customer_categories WHERE category_name = $1';
    const existingResult = await pool.query(existingQuery, [categoryName.trim()]);

    if (existingResult.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Customer category with this name already exists'
      });
    }

    const query = `
      INSERT INTO customer_categories (
        category_name, 
        description, 
        color, 
        is_active, 
        sort_order, 
        discount_percentage, 
        special_terms,
        created_at,
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING id, category_name, description, color, is_active, sort_order, discount_percentage, special_terms, created_at, updated_at
    `;

    const values = [
      categoryName.trim(),
      description.trim(),
      color,
      isActive,
      parseInt(sortOrder) || 0,
      parseFloat(discountPercentage) || 0,
      specialTerms.trim()
    ];

    const result = await pool.query(query, values);
    const row = result.rows[0];

    res.status(201).json({
      success: true,
      message: 'Customer category created successfully',
      data: {
        id: row.id,
        categoryName: row.category_name,
        description: row.description,
        color: row.color,
        isActive: row.is_active,
        sortOrder: row.sort_order,
        discountPercentage: row.discount_percentage,
        specialTerms: row.special_terms,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create customer category',
      error: error.message
    });
  }
};

// Update customer category
const updateCustomerCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      categoryName,
      description,
      color,
      isActive,
      sortOrder,
      discountPercentage,
      specialTerms
    } = req.body;

    // Validation
    if (!categoryName || categoryName.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Category name is required'
      });
    }

    if (discountPercentage !== undefined && (discountPercentage < 0 || discountPercentage > 100)) {
      return res.status(400).json({
        success: false,
        message: 'Discount percentage must be between 0 and 100'
      });
    }

    // Check if category exists
    const existingQuery = 'SELECT id FROM customer_categories WHERE id = $1';
    const existingResult = await pool.query(existingQuery, [id]);

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer category not found'
      });
    }

    // Check if category name already exists (excluding current category)
    const duplicateQuery = 'SELECT id FROM customer_categories WHERE category_name = $1 AND id != $2';
    const duplicateResult = await pool.query(duplicateQuery, [categoryName.trim(), id]);

    if (duplicateResult.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Customer category with this name already exists'
      });
    }

    const query = `
      UPDATE customer_categories 
      SET 
        category_name = $1,
        description = $2,
        color = $3,
        is_active = $4,
        sort_order = $5,
        discount_percentage = $6,
        special_terms = $7,
        updated_at = NOW()
      WHERE id = $8
      RETURNING id, category_name, description, color, is_active, sort_order, discount_percentage, special_terms, created_at, updated_at
    `;

    const values = [
      categoryName.trim(),
      description?.trim() || '',
      color || '#10B981',
      isActive !== undefined ? isActive : true,
      parseInt(sortOrder) || 0,
      parseFloat(discountPercentage) || 0,
      specialTerms?.trim() || '',
      id
    ];

    const result = await pool.query(query, values);
    const row = result.rows[0];

    res.json({
      success: true,
      message: 'Customer category updated successfully',
      data: {
        id: row.id,
        categoryName: row.category_name,
        description: row.description,
        color: row.color,
        isActive: row.is_active,
        sortOrder: row.sort_order,
        discountPercentage: row.discount_percentage,
        specialTerms: row.special_terms,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer category',
      error: error.message
    });
  }
};

// Delete customer category
const deleteCustomerCategory = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category exists
    const existingQuery = 'SELECT id FROM customer_categories WHERE id = $1';
    const existingResult = await pool.query(existingQuery, [id]);

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer category not found'
      });
    }

    // Check if category is being used by customers
    const usageQuery = 'SELECT COUNT(*) as count FROM customers WHERE customer_category_id = $1';
    const usageResult = await pool.query(usageQuery, [id]);
    const usageCount = parseInt(usageResult.rows[0].count);

    if (usageCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete customer category. It is being used by ${usageCount} customer(s).`
      });
    }

    const deleteQuery = 'DELETE FROM customer_categories WHERE id = $1';
    await pool.query(deleteQuery, [id]);

    res.json({
      success: true,
      message: 'Customer category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete customer category',
      error: error.message
    });
  }
};

// Get customer categories statistics
const getCustomerCategoriesStats = async (req, res) => {
  try {
    const [
      totalCategoriesResult,
      activeCategoriesResult,
      inactiveCategoriesResult,
      categoriesWithCustomersResult
    ] = await Promise.all([
      // Total customer categories
      pool.query('SELECT COUNT(*) as total FROM customer_categories'),

      // Active customer categories
      pool.query('SELECT COUNT(*) as total FROM customer_categories WHERE is_active = true'),

      // Inactive customer categories
      pool.query('SELECT COUNT(*) as total FROM customer_categories WHERE is_active = false'),

      // Categories with customers (assuming customers table has customer_category field)
      pool.query(`
        SELECT COUNT(DISTINCT cc.id) as total
        FROM customer_categories cc
        INNER JOIN customers c ON c.customer_category = cc.category_name
      `).catch(() => ({ rows: [{ total: 0 }] }))
    ]);

    const totalCategories = parseInt(totalCategoriesResult.rows[0].total);
    const activeCategories = parseInt(activeCategoriesResult.rows[0].total);
    const inactiveCategories = parseInt(inactiveCategoriesResult.rows[0].total);
    const categoriesWithCustomers = parseInt(categoriesWithCustomersResult.rows[0].total);

    res.json({
      success: true,
      data: {
        totalCategories,
        activeCategories,
        inactiveCategories,
        categoriesWithCustomers,
        categoriesWithoutCustomers: totalCategories - categoriesWithCustomers
      }
    });
  } catch (error) {
    console.error('Error fetching customer categories stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching customer categories statistics',
      error: error.message,
    });
  }
};

// Export customer categories to CSV/Excel
const exportCustomerCategories = async (req, res) => {
  try {
    const { format = 'xlsx', status, search } = req.query;

    // Build where clause for filtering
    let whereClause = '';
    const values = [];
    let paramCount = 0;

    if (status !== undefined) {
      paramCount++;
      whereClause += `WHERE is_active = $${paramCount}`;
      values.push(status === '1' || status === 'true');
    }

    if (search) {
      paramCount++;
      if (whereClause) {
        whereClause += ` AND category_name ILIKE $${paramCount}`;
      } else {
        whereClause += `WHERE category_name ILIKE $${paramCount}`;
      }
      values.push(`%${search}%`);
    }

    const query = `
      SELECT
        id,
        category_name,
        description,
        color,
        is_active,
        sort_order,
        discount_percentage,
        special_terms,
        created_at,
        updated_at
      FROM customer_categories
      ${whereClause}
      ORDER BY sort_order ASC, category_name ASC
    `;

    const result = await pool.query(query, values);

    const { generateCustomerCategoryExport } = await import('../utils/excelProcessor.js');

    const exportData = result.rows.map(row => ({
      id: row.id,
      categoryName: row.category_name,
      description: row.description || '',
      color: row.color || '#10B981',
      status: row.is_active ? 'Active' : 'Inactive',
      sortOrder: row.sort_order || 0,
      discountPercentage: row.discount_percentage || 0,
      specialTerms: row.special_terms || '',
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    const exportBuffer = generateCustomerCategoryExport(exportData, format);

    const filename = `customer_categories_export_${new Date().toISOString().slice(0, 10)}.${format}`;

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

    res.send(exportBuffer);
  } catch (error) {
    console.error('Export error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export customer categories',
      error: error.message
    });
  }
};

// Import customer categories from CSV/Excel file
const importCustomerCategories = async (req, res) => {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'Please upload a CSV or Excel file'
    });
  }

  try {
    const { parseExcelFile, validateCustomerCategoryData, cleanupTempFile } = await import('../utils/excelProcessor.js');

    let tempFilePath = null;
    let parsedData = [];

    try {
      // Parse the uploaded file
      const parseResult = await parseExcelFile(req.file.buffer, req.file.originalname);
      parsedData = parseResult.data;
      tempFilePath = parseResult.tempFilePath;

      if (!parsedData || parsedData.length === 0) {
        throw new Error('No data found in the uploaded file');
      }

      // Validate and process each row
      const results = {
        total: parsedData.length,
        successful: 0,
        failed: 0,
        errors: [],
        created: []
      };

      for (let i = 0; i < parsedData.length; i++) {
        const rowData = parsedData[i];
        const rowNumber = i + 2; // +2 because Excel rows start at 1 and we skip header

        try {
          // Validate the data
          const validation = validateCustomerCategoryData(rowData);
          if (!validation.isValid) {
            results.failed++;
            results.errors.push({
              row: rowNumber,
              errors: validation.errors
            });
            continue;
          }

          // Check if category already exists
          const existingQuery = 'SELECT id FROM customer_categories WHERE category_name = $1';
          const existingResult = await pool.query(existingQuery, [rowData.categoryName.trim()]);

          if (existingResult.rows.length > 0) {
            results.failed++;
            results.errors.push({
              row: rowNumber,
              errors: [`Category '${rowData.categoryName}' already exists`]
            });
            continue;
          }

          // Insert the category
          const insertQuery = `
            INSERT INTO customer_categories (
              category_name,
              description,
              color,
              is_active,
              sort_order,
              discount_percentage,
              special_terms,
              created_at,
              updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING id, category_name
          `;

          const insertValues = [
            rowData.categoryName.trim(),
            rowData.description?.trim() || '',
            rowData.color || '#10B981',
            rowData.status === 'Active' || rowData.isActive === true,
            parseInt(rowData.sortOrder) || 0,
            parseFloat(rowData.discountPercentage) || 0,
            rowData.specialTerms?.trim() || ''
          ];

          const insertResult = await pool.query(insertQuery, insertValues);

          results.successful++;
          results.created.push({
            id: insertResult.rows[0].id,
            name: insertResult.rows[0].category_name
          });

        } catch (rowError) {
          results.failed++;
          results.errors.push({
            row: rowNumber,
            errors: [rowError.message]
          });
        }
      }

      res.json({
        success: true,
        message: `Import completed. ${results.successful} categories created, ${results.failed} failed.`,
        data: results
      });

    } finally {
      // Cleanup temp file if it exists
      if (tempFilePath) {
        await cleanupTempFile(tempFilePath);
      }
    }

  } catch (error) {
    console.error('Import error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import customer categories',
      error: error.message
    });
  }
};

// Download customer category import template
const downloadCustomerCategoryTemplate = async (req, res) => {
  try {
    const { generateCustomerCategoryTemplate } = await import('../utils/excelProcessor.js');

    const templateBuffer = generateCustomerCategoryTemplate();
    const filename = 'customer_category_import_template.xlsx';

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

    res.send(templateBuffer);
  } catch (error) {
    console.error('Template generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate template',
      error: error.message
    });
  }
};

export {
  getCustomerCategories,
  getCustomerCategoryById,
  createCustomerCategory,
  updateCustomerCategory,
  deleteCustomerCategory,
  getCustomerCategoriesStats,
  exportCustomerCategories,
  importCustomerCategories,
  downloadCustomerCategoryTemplate
};
