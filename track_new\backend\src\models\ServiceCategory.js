import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const ServiceCategory = sequelize.define('ServiceCategory', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  serviceCategory: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100],
    },
  },
  serviceStatus: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: '1=Active, 0=Inactive',
  },
  styleView: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: 'Style view type for category display',
  },
  form: {
    type: DataTypes.JSONB,
    defaultValue: {},
    comment: 'Dynamic form configuration for this category',
  },
  description: {
    type: DataTypes.TEXT,
  },
  icon: {
    type: DataTypes.STRING,
    comment: 'Icon class or URL for category',
  },
  color: {
    type: DataTypes.STRING,
    defaultValue: '#3B82F6',
    comment: 'Category color for UI display',
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Sort order for category display',
  },
  isDefault: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a default category',
  },
  companyId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id',
    },
  },
  createdBy: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  updatedBy: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'id',
    },
  },
}, {
  tableName: 'service_categories',
  indexes: [
    {
      fields: ['company_id'],
    },
    {
      fields: ['service_status'],
    },
    {
      fields: ['sort_order'],
    },
    {
      unique: true,
      fields: ['service_category', 'company_id'],
      name: 'unique_category_per_company',
    },
  ],
  scopes: {
    active: {
      where: {
        serviceStatus: 1,
      },
    },
    byCompany: (companyId) => ({
      where: {
        companyId,
      },
    }),
  },
});

// Instance methods
ServiceCategory.prototype.getFormFields = function() {
  return this.form?.fields || [];
};

ServiceCategory.prototype.validateFormData = function(formData) {
  const fields = this.getFormFields();
  const errors = [];

  // Field type validation patterns
  const fieldTypeValidations = {
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Please enter a valid email address'
    },
    phone: {
      pattern: /^[\+]?[1-9][\d]{0,15}$/,
      message: 'Please enter a valid phone number'
    },
    url: {
      pattern: /^https?:\/\/.+/,
      message: 'Please enter a valid URL starting with http:// or https://'
    },
    number: {
      pattern: /^-?\d*\.?\d+$/,
      message: 'Please enter a valid number'
    },
    password: {
      minLength: 8,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      message: 'Password must contain at least 8 characters with uppercase, lowercase, and number'
    }
  };

  fields.forEach(field => {
    const value = formData[field.name];

    // Required validation
    if (field.required && (!value || (Array.isArray(value) && value.length === 0) || value.toString().trim() === '')) {
      errors.push(`${field.label || field.name} is required`);
      return; // Skip other validations if required field is empty
    }

    // Skip other validations if field is empty and not required
    if (!value || value.toString().trim() === '') {
      return;
    }

    // Type-specific validation
    const typeValidation = fieldTypeValidations[field.type];
    if (typeValidation && typeValidation.pattern && !typeValidation.pattern.test(value)) {
      errors.push(typeValidation.message);
    }

    // Custom validation rules
    if (field.validation) {
      // Length validation
      if (field.validation.minLength && value.length < field.validation.minLength) {
        errors.push(`${field.label || field.name} must be at least ${field.validation.minLength} characters`);
      }
      if (field.validation.maxLength && value.length > field.validation.maxLength) {
        errors.push(`${field.label || field.name} must not exceed ${field.validation.maxLength} characters`);
      }

      // Pattern validation
      if (field.validation.pattern) {
        try {
          const regex = new RegExp(field.validation.pattern);
          if (!regex.test(value)) {
            errors.push(field.validation.patternMessage || `${field.label || field.name} format is invalid`);
          }
        } catch (e) {
          console.warn('Invalid regex pattern:', field.validation.pattern);
        }
      }
    }

    // Number field validation
    if (['number', 'range'].includes(field.type)) {
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        errors.push(`${field.label || field.name} must be a valid number`);
      } else {
        if (field.min !== undefined && numValue < field.min) {
          errors.push(`${field.label || field.name} must be at least ${field.min}`);
        }
        if (field.max !== undefined && numValue > field.max) {
          errors.push(`${field.label || field.name} must not exceed ${field.max}`);
        }
      }
    }

    // URL validation
    if (field.type === 'url' && value) {
      try {
        new URL(value);
      } catch {
        errors.push(`${field.label || field.name} must be a valid URL`);
      }
    }

    // Date validation
    if (['date', 'datetime', 'time'].includes(field.type) && value) {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        errors.push(`${field.label || field.name} must be a valid date`);
      }
    }

    // File validation (basic check)
    if (field.type === 'file' && value) {
      // This would typically be handled by file upload middleware
      // but we can add basic validation here
      if (typeof value === 'string' && value.length > 255) {
        errors.push(`${field.label || field.name} file path is too long`);
      }
    }

    // Color validation
    if (field.type === 'color' && value) {
      const colorRegex = /^#[0-9A-Fa-f]{6}$/;
      if (!colorRegex.test(value)) {
        errors.push(`${field.label || field.name} must be a valid hex color`);
      }
    }
  });

  return errors;
};

// Validate form configuration
ServiceCategory.prototype.validateFormConfiguration = function() {
  const errors = [];
  const fields = this.getFormFields();

  if (!Array.isArray(fields)) {
    errors.push('Form fields must be an array');
    return errors;
  }

  const fieldNames = [];

  fields.forEach((field, index) => {
    const fieldPrefix = `Field ${index + 1}`;

    // Required field properties
    if (!field.name || typeof field.name !== 'string' || field.name.trim() === '') {
      errors.push(`${fieldPrefix}: Field name is required`);
    } else {
      // Check for valid field name format
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(field.name)) {
        errors.push(`${fieldPrefix}: Field name must be a valid identifier (letters, numbers, underscore)`);
      }

      // Check for duplicate field names
      if (fieldNames.includes(field.name)) {
        errors.push(`${fieldPrefix}: Duplicate field name '${field.name}'`);
      } else {
        fieldNames.push(field.name);
      }
    }

    if (!field.label || typeof field.label !== 'string' || field.label.trim() === '') {
      errors.push(`${fieldPrefix}: Field label is required`);
    }

    if (!field.type || typeof field.type !== 'string') {
      errors.push(`${fieldPrefix}: Field type is required`);
    } else {
      // Validate field type
      const validTypes = ['text', 'textarea', 'number', 'email', 'phone', 'date', 'datetime', 'time',
                         'select', 'radio', 'checkbox', 'file', 'url', 'password', 'range', 'color'];
      if (!validTypes.includes(field.type)) {
        errors.push(`${fieldPrefix}: Invalid field type '${field.type}'`);
      }

      // Validate options for select/radio/checkbox fields
      if (['select', 'radio', 'checkbox'].includes(field.type)) {
        if (!field.options || !Array.isArray(field.options) || field.options.length === 0) {
          errors.push(`${fieldPrefix}: Options are required for ${field.type} fields`);
        } else {
          // Check for empty options
          const emptyOptions = field.options.filter(opt => !opt || opt.trim() === '');
          if (emptyOptions.length > 0) {
            errors.push(`${fieldPrefix}: All options must have values`);
          }
        }
      }
    }

    // Validate validation rules if present
    if (field.validation && typeof field.validation === 'object') {
      if (field.validation.pattern) {
        try {
          new RegExp(field.validation.pattern);
        } catch (e) {
          errors.push(`${fieldPrefix}: Invalid regex pattern in validation`);
        }
      }

      if (field.validation.minLength && (typeof field.validation.minLength !== 'number' || field.validation.minLength < 0)) {
        errors.push(`${fieldPrefix}: minLength must be a non-negative number`);
      }

      if (field.validation.maxLength && (typeof field.validation.maxLength !== 'number' || field.validation.maxLength < 0)) {
        errors.push(`${fieldPrefix}: maxLength must be a non-negative number`);
      }

      if (field.validation.minLength && field.validation.maxLength && field.validation.minLength > field.validation.maxLength) {
        errors.push(`${fieldPrefix}: minLength cannot be greater than maxLength`);
      }
    }

    // Validate min/max for number and range fields
    if (['number', 'range'].includes(field.type)) {
      if (field.min !== undefined && typeof field.min !== 'number') {
        errors.push(`${fieldPrefix}: min must be a number`);
      }
      if (field.max !== undefined && typeof field.max !== 'number') {
        errors.push(`${fieldPrefix}: max must be a number`);
      }
      if (field.min !== undefined && field.max !== undefined && field.min > field.max) {
        errors.push(`${fieldPrefix}: min cannot be greater than max`);
      }
    }
  });

  return errors;
};

ServiceCategory.prototype.isActive = function() {
  return this.serviceStatus === 1;
};

// Class methods
ServiceCategory.getActiveCategories = async function(companyId) {
  return await this.scope(['active', { method: ['byCompany', companyId] }]).findAll({
    order: [['sortOrder', 'ASC'], ['serviceCategory', 'ASC']],
  });
};

ServiceCategory.getCategoryWithServicesCount = async function(companyId) {
  const { Service } = sequelize.models;
  
  return await this.findAll({
    where: { companyId, serviceStatus: 1 },
    attributes: [
      'id',
      'serviceCategory',
      'color',
      'icon',
      [
        sequelize.fn('COUNT', sequelize.col('Services.id')),
        'servicesCount'
      ]
    ],
    include: [{
      model: Service,
      attributes: [],
      required: false,
    }],
    group: ['ServiceCategory.id'],
    order: [['sortOrder', 'ASC'], ['serviceCategory', 'ASC']],
  });
};

// Define associations
ServiceCategory.associate = (models) => {
  // ServiceCategory belongs to Company
  ServiceCategory.belongsTo(models.Company, {
    foreignKey: 'companyId',
    as: 'company',
  });

  // ServiceCategory has many Services
  ServiceCategory.hasMany(models.Service, {
    foreignKey: 'serviceCategoryId',
    as: 'services',
  });

  // ServiceCategory belongs to User (created by)
  ServiceCategory.belongsTo(models.User, {
    foreignKey: 'createdBy',
    as: 'creator',
  });

  // ServiceCategory belongs to User (updated by)
  ServiceCategory.belongsTo(models.User, {
    foreignKey: 'updatedBy',
    as: 'updater',
  });
};

export default ServiceCategory;
