import api from './api';

/**
 * Service Category API Service
 * Handles all API calls related to service categories
 */
const serviceCategoryService = {
  // Get all service categories with pagination and filters
  getServiceCategories: async (params = {}) => {
    const response = await api.get('/service-categories', { params });
    return response.data;
  },

  // Get active service categories for dropdowns
  getActiveServiceCategories: async () => {
    const response = await api.get('/service-categories/active');
    return response.data;
  },

  // Get service category by ID
  getServiceCategoryById: async (id) => {
    const response = await api.get(`/service-categories/${id}`);
    return response.data;
  },

  // Create new service category
  createServiceCategory: async (categoryData) => {
    const response = await api.post('/service-categories', categoryData);
    return response.data;
  },

  // Update service category
  updateServiceCategory: async (id, categoryData) => {
    const response = await api.put(`/service-categories/${id}`, categoryData);
    return response.data;
  },

  // Delete service category
  deleteServiceCategory: async (id) => {
    const response = await api.delete(`/service-categories/${id}`);
    return response.data;
  },

  // Get service category statistics
  getServiceCategoriesStats: async () => {
    const response = await api.get('/service-categories/stats');
    return response.data;
  },

  // Export service categories
  exportServiceCategories: async (filters = {}, format = 'xlsx') => {
    const response = await api.get('/service-categories/export', {
      params: { ...filters, format },
      responseType: 'blob',
    });
    return response;
  },

  // Import service categories
  importServiceCategories: async (file, options = {}) => {
    const formData = new FormData();
    formData.append('file', file);
    Object.keys(options).forEach(key => {
      formData.append(key, options[key]);
    });

    const response = await api.post('/service-categories/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Download import template
  downloadTemplate: async () => {
    const response = await api.get('/service-categories/template', {
      responseType: 'blob',
    });
    return response;
  },

  // Bulk operations
  bulkUpdateServiceCategories: async (categoryIds, updateData) => {
    const response = await api.patch('/service-categories/bulk-update', {
      categoryIds,
      updateData,
    });
    return response.data;
  },

  bulkDeleteServiceCategories: async (categoryIds) => {
    const response = await api.delete('/service-categories/bulk', {
      data: { categoryIds },
    });
    return response.data;
  },

  // Utility functions
  downloadFile: (blob, filename) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  },

  // Generate export filename
  generateExportFilename: (format = 'xlsx', prefix = 'service_categories') => {
    const timestamp = new Date().toISOString().split('T')[0];
    return `${prefix}_export_${timestamp}.${format}`;
  },

  // Validate import file
  validateImportFile: (file) => {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv'
    ];
    
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Please upload an Excel (.xlsx) or CSV file.');
    }
    
    if (file.size > maxSize) {
      throw new Error('File size too large. Maximum size is 10MB.');
    }
    
    return true;
  }
};

export default serviceCategoryService;
