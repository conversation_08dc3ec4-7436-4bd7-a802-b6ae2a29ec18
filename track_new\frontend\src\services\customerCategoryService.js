import api from './api';

/**
 * Customer Category API Service
 * Handles all API calls related to customer categories
 */
const customerCategoryService = {
  // Get all customer categories with pagination and filters
  getCustomerCategories: async (params = {}) => {
    const response = await api.get('/customer-categories', { params });
    return response.data;
  },

  // Get active customer categories for dropdowns
  getActiveCustomerCategories: async () => {
    const response = await api.get('/customer-categories', { 
      params: { status: 'active', limit: 1000 } 
    });
    return response.data;
  },

  // Get customer category by ID
  getCustomerCategoryById: async (id) => {
    const response = await api.get(`/customer-categories/${id}`);
    return response.data;
  },

  // Create new customer category
  createCustomerCategory: async (categoryData) => {
    const response = await api.post('/customer-categories', categoryData);
    return response.data;
  },

  // Update customer category
  updateCustomerCategory: async (id, categoryData) => {
    const response = await api.put(`/customer-categories/${id}`, categoryData);
    return response.data;
  },

  // Delete customer category
  deleteCustomerCategory: async (id) => {
    const response = await api.delete(`/customer-categories/${id}`);
    return response.data;
  },

  // Get customer category statistics
  getCustomerCategoriesStats: async () => {
    const response = await api.get('/customer-categories/stats');
    return response.data;
  },

  // Export customer categories
  exportCustomerCategories: async (filters = {}, format = 'xlsx') => {
    const response = await api.get('/customer-categories/export', {
      params: { ...filters, format },
      responseType: 'blob',
    });
    return response;
  },

  // Import customer categories
  importCustomerCategories: async (file, options = {}) => {
    const formData = new FormData();
    formData.append('file', file);
    
    // Add any additional options
    Object.keys(options).forEach(key => {
      formData.append(key, options[key]);
    });

    const response = await api.post('/customer-categories/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Download import template
  downloadTemplate: async () => {
    const response = await api.get('/customer-categories/template', {
      responseType: 'blob',
    });
    return response;
  },

  // Bulk operations
  bulkUpdateCustomerCategories: async (categoryIds, updateData) => {
    const response = await api.patch('/customer-categories/bulk-update', {
      categoryIds,
      updateData,
    });
    return response.data;
  },

  bulkDeleteCustomerCategories: async (categoryIds) => {
    const response = await api.delete('/customer-categories/bulk', {
      data: { categoryIds },
    });
    return response.data;
  },

  // Utility functions
  validateImportFile: (file) => {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
    ];

    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Please upload Excel (.xlsx, .xls) or CSV files only.');
    }

    if (file.size > maxSize) {
      throw new Error('File size too large. Maximum size allowed is 10MB.');
    }

    return true;
  },

  generateExportFilename: (format = 'xlsx') => {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    return `customer_categories_export_${timestamp}.${format}`;
  },

  downloadFile: (blob, filename) => {
    const url = window.URL.createObjectURL(new Blob([blob]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  },

  // Search and filtering helpers
  buildSearchParams: (filters) => {
    const params = new URLSearchParams();
    
    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });
    
    return params;
  },

  // Data transformation helpers
  transformCategoryForDisplay: (category) => {
    return {
      ...category,
      statusText: category.isActive ? 'Active' : 'Inactive',
      discountText: category.discountPercentage > 0 
        ? `${category.discountPercentage}% Discount` 
        : 'No Discount',
      colorStyle: {
        backgroundColor: category.color || '#10B981',
      },
    };
  },

  transformCategoriesForDisplay: (categories) => {
    return categories.map(category => 
      customerCategoryService.transformCategoryForDisplay(category)
    );
  },

  // Validation helpers
  validateCategoryData: (categoryData) => {
    const errors = {};

    if (!categoryData.categoryName || !categoryData.categoryName.trim()) {
      errors.categoryName = 'Category name is required';
    }

    if (categoryData.categoryName && categoryData.categoryName.length > 255) {
      errors.categoryName = 'Category name must be less than 255 characters';
    }

    if (categoryData.discountPercentage !== undefined) {
      const discount = parseFloat(categoryData.discountPercentage);
      if (isNaN(discount) || discount < 0 || discount > 100) {
        errors.discountPercentage = 'Discount percentage must be between 0 and 100';
      }
    }

    if (categoryData.color && !/^#[0-9A-F]{6}$/i.test(categoryData.color)) {
      errors.color = 'Color must be a valid hex color code';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  },

  // Category management helpers
  getDefaultCategoryData: () => {
    return {
      categoryName: '',
      description: '',
      color: '#10B981',
      isActive: true,
      sortOrder: 0,
      discountPercentage: 0,
      specialTerms: '',
    };
  },

  // Statistics helpers
  calculateCategoryStats: (categories) => {
    const total = categories.length;
    const active = categories.filter(cat => cat.isActive).length;
    const inactive = total - active;
    const withDiscount = categories.filter(cat => cat.discountPercentage > 0).length;
    const averageDiscount = categories.reduce((sum, cat) => sum + (cat.discountPercentage || 0), 0) / total;

    return {
      total,
      active,
      inactive,
      withDiscount,
      withoutDiscount: total - withDiscount,
      averageDiscount: Math.round(averageDiscount * 100) / 100,
    };
  },
};

export default customerCategoryService;
