import React, { useState, useEffect } from 'react';
import { UserGroupIcon } from '@heroicons/react/24/outline';
import customerCategoryService from '../../services/customerCategoryService';
import LoadingSpinner from '../common/LoadingSpinner';
import '../../styles/design-system.css';

/**
 * Customer Category Form Component
 * 
 * Form for creating and editing customer categories within Service Categories
 */
const CustomerCategoryForm = ({ customerCategory, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Form state
  const [formData, setFormData] = useState({
    categoryName: '',
    description: '',
    color: '#3B82F6',
    isActive: true,
    sortOrder: 0,
    discountPercentage: 0,
    specialTerms: ''
  });

  // Initialize form data
  useEffect(() => {
    if (customerCategory) {
      setFormData({
        categoryName: customerCategory.categoryName || '',
        description: customerCategory.description || '',
        color: customerCategory.color || '#3B82F6',
        isActive: customerCategory.isActive !== undefined ? customerCategory.isActive : true,
        sortOrder: customerCategory.sortOrder || 0,
        discountPercentage: customerCategory.discountPercentage || 0,
        specialTerms: customerCategory.specialTerms || ''
      });
    }
  }, [customerCategory]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
               type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.categoryName.trim()) {
      newErrors.categoryName = 'Category name is required';
    }

    if (formData.discountPercentage < 0 || formData.discountPercentage > 100) {
      newErrors.discountPercentage = 'Discount percentage must be between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      let response;

      if (customerCategory) {
        // Update existing category
        response = await customerCategoryService.updateCustomerCategory(customerCategory.id, formData);
      } else {
        // Create new category
        response = await customerCategoryService.createCustomerCategory(formData);
      }

      if (response.success) {
        onSuccess();
      } else {
        throw new Error(response.message || 'Failed to save customer category');
      }
    } catch (error) {
      console.error('Error saving customer category:', error);
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Category Name */}
      <div>
        <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700">
          Category Name *
        </label>
        <input
          type="text"
          id="categoryName"
          name="categoryName"
          value={formData.categoryName}
          onChange={handleInputChange}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter category name"
        />
        {errors.categoryName && <p className="mt-1 text-sm text-red-600">{errors.categoryName}</p>}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          rows={3}
          value={formData.description}
          onChange={handleInputChange}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter category description"
        />
      </div>

      {/* Color and Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="color" className="block text-sm font-medium text-gray-700">
            Color
          </label>
          <div className="mt-1 flex items-center space-x-3">
            <input
              type="color"
              id="color"
              name="color"
              value={formData.color}
              onChange={handleInputChange}
              className="h-10 w-20 border border-gray-300 rounded-md"
            />
            <input
              type="text"
              value={formData.color}
              onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
              className="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="#3B82F6"
            />
          </div>
        </div>

        <div>
          <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700">
            Sort Order
          </label>
          <input
            type="number"
            id="sortOrder"
            name="sortOrder"
            value={formData.sortOrder}
            onChange={handleInputChange}
            min="0"
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Discount and Terms */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="discountPercentage" className="block text-sm font-medium text-gray-700">
            Discount Percentage (%)
          </label>
          <input
            type="number"
            id="discountPercentage"
            name="discountPercentage"
            value={formData.discountPercentage}
            onChange={handleInputChange}
            min="0"
            max="100"
            step="0.01"
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {errors.discountPercentage && <p className="mt-1 text-sm text-red-600">{errors.discountPercentage}</p>}
        </div>

        <div className="flex items-center">
          <input
            id="isActive"
            name="isActive"
            type="checkbox"
            checked={formData.isActive}
            onChange={handleInputChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
            Active Category
          </label>
        </div>
      </div>

      {/* Special Terms */}
      <div>
        <label htmlFor="specialTerms" className="block text-sm font-medium text-gray-700">
          Special Terms & Conditions
        </label>
        <textarea
          id="specialTerms"
          name="specialTerms"
          rows={3}
          value={formData.specialTerms}
          onChange={handleInputChange}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter any special terms for this customer category"
        />
      </div>

      {/* Error Message */}
      {errors.submit && (
        <div className="text-red-600 text-sm">{errors.submit}</div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center">
              <LoadingSpinner size="sm" />
              <span className="ml-2">Saving...</span>
            </div>
          ) : (
            customerCategory ? 'Update Category' : 'Create Category'
          )}
        </button>
      </div>
    </form>
  );
};

export default CustomerCategoryForm;
