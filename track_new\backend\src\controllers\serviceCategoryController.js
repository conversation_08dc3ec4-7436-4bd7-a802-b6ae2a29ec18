import { ServiceCategory, Service } from '../models/index.js';
import { Op } from 'sequelize';
import asyncHandler from '../utils/asyncHandler.js';
import AppError from '../utils/appError.js';
import logger from '../utils/logger.js';

// @desc    Get all service categories
// @route   GET /api/service-categories
// @access  Private
export const getServiceCategories = async (req, res) => {
  try {
    const { companyId } = req.user;
    const { status, search, page = 1, limit = 10 } = req.query;

    const whereClause = { companyId };

    if (status !== undefined) {
      whereClause.serviceStatus = parseInt(status);
    }

    if (search) {
      whereClause.serviceCategory = {
        [Op.iLike]: `%${search}%`,
      };
    }

    const offset = (page - 1) * limit;

    const { count, rows: categories } = await ServiceCategory.findAndCountAll({
      where: whereClause,
      include: [{
        model: Service,
        as: 'services',
        attributes: [],
        required: false,
      }],
      attributes: [
        'id',
        'serviceCategory',
        'serviceStatus',
        'styleView',
        'form',
        'description',
        'icon',
        'color',
        'sortOrder',
        'isDefault',
        'createdAt',
        'updatedAt',
        [
          ServiceCategory.sequelize.fn('COUNT', ServiceCategory.sequelize.col('services.id')),
          'servicesCount'
        ]
      ],
      group: ['ServiceCategory.id'],
      order: [['sortOrder', 'ASC'], ['serviceCategory', 'ASC']],
      limit: parseInt(limit),
      offset,
      subQuery: false,
    });

    res.json({
      success: true,
      data: {
        categories,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count.length / limit),
          totalItems: count.length,
          itemsPerPage: parseInt(limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching service categories:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching service categories',
      error: error.message,
    });
  }
};

// @desc    Get active service categories for dropdown
// @route   GET /api/service-categories/active
// @access  Private
export const getActiveServiceCategories = async (req, res) => {
  try {
    const { companyId } = req.user;

    const categories = await ServiceCategory.getActiveCategories(companyId);

    res.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error('Error fetching active service categories:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching active service categories',
      error: error.message,
    });
  }
};

// @desc    Get service category by ID
// @route   GET /api/service-categories/:id
// @access  Private
export const getServiceCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    const { companyId } = req.user;

    const category = await ServiceCategory.findOne({
      where: { id, companyId },
      include: [{
        model: Service,
        as: 'services',
        attributes: ['id', 'serviceCode', 'title', 'status', 'createdAt'],
        limit: 10,
        order: [['createdAt', 'DESC']],
      }],
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Service category not found',
      });
    }

    res.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('Error fetching service category:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching service category',
      error: error.message,
    });
  }
};

// @desc    Create new service category
// @route   POST /api/service-categories
// @access  Private
export const createServiceCategory = async (req, res) => {
  try {
    const { companyId, id: userId } = req.user;
    const {
      serviceCategory,
      description,
      icon,
      color,
      sortOrder,
      form,
      styleView,
    } = req.body;

    // Comprehensive validation
    const validationErrors = [];

    // Validate required fields
    if (!serviceCategory || serviceCategory.trim() === '') {
      validationErrors.push('Service category name is required');
    } else {
      // Validate category name format
      if (serviceCategory.trim().length < 2) {
        validationErrors.push('Service category name must be at least 2 characters');
      }
      if (serviceCategory.trim().length > 100) {
        validationErrors.push('Service category name must not exceed 100 characters');
      }
      if (!/^[a-zA-Z0-9\s\-_&()]+$/.test(serviceCategory.trim())) {
        validationErrors.push('Service category name contains invalid characters');
      }
    }

    // Validate description
    if (description && description.length > 500) {
      validationErrors.push('Description must not exceed 500 characters');
    }

    // Validate color
    if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
      validationErrors.push('Color must be a valid hex color');
    }

    // Validate sort order
    if (sortOrder !== undefined && (isNaN(sortOrder) || sortOrder < 0 || sortOrder > 9999)) {
      validationErrors.push('Sort order must be a number between 0 and 9999');
    }

    // Validate icon
    if (icon && icon.length > 100) {
      validationErrors.push('Icon must not exceed 100 characters');
    }

    // Create temporary category instance for form validation
    if (form && form.fields) {
      const tempCategory = ServiceCategory.build({
        serviceCategory: serviceCategory?.trim(),
        form: form,
        companyId,
      });

      // Validate form configuration
      const formConfigErrors = tempCategory.validateFormConfiguration();
      if (formConfigErrors.length > 0) {
        validationErrors.push(...formConfigErrors);
      }
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    // Check if category already exists
    const existingCategory = await ServiceCategory.findOne({
      where: {
        serviceCategory: serviceCategory.trim(),
        companyId,
      },
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Service category with this name already exists',
      });
    }

    const category = await ServiceCategory.create({
      serviceCategory: serviceCategory.trim(),
      description: description?.trim() || null,
      icon: icon?.trim() || null,
      color: color || '#3B82F6',
      sortOrder: sortOrder || 0,
      form: form || { fields: [] },
      styleView: styleView || 1,
      serviceStatus: 1,
      companyId,
      createdBy: userId,
    });

    res.status(201).json({
      success: true,
      message: 'Service category created successfully',
      data: category,
    });
  } catch (error) {
    console.error('Error creating service category:', error);

    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    // Handle unique constraint errors
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Service category with this name already exists',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Error creating service category',
      error: error.message,
    });
  }
};

// @desc    Update service category
// @route   PUT /api/service-categories/:id
// @access  Private
export const updateServiceCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { companyId, id: userId } = req.user;
    const {
      serviceCategory,
      description,
      icon,
      color,
      sortOrder,
      form,
      styleView,
      serviceStatus,
    } = req.body;

    const category = await ServiceCategory.findOne({
      where: { id, companyId },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Service category not found',
      });
    }

    // Comprehensive validation
    const validationErrors = [];

    // Validate category name if provided
    if (serviceCategory !== undefined) {
      if (!serviceCategory || serviceCategory.trim() === '') {
        validationErrors.push('Service category name is required');
      } else {
        if (serviceCategory.trim().length < 2) {
          validationErrors.push('Service category name must be at least 2 characters');
        }
        if (serviceCategory.trim().length > 100) {
          validationErrors.push('Service category name must not exceed 100 characters');
        }
        if (!/^[a-zA-Z0-9\s\-_&()]+$/.test(serviceCategory.trim())) {
          validationErrors.push('Service category name contains invalid characters');
        }
      }
    }

    // Validate description
    if (description !== undefined && description && description.length > 500) {
      validationErrors.push('Description must not exceed 500 characters');
    }

    // Validate color
    if (color !== undefined && color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
      validationErrors.push('Color must be a valid hex color');
    }

    // Validate sort order
    if (sortOrder !== undefined && (isNaN(sortOrder) || sortOrder < 0 || sortOrder > 9999)) {
      validationErrors.push('Sort order must be a number between 0 and 9999');
    }

    // Validate icon
    if (icon !== undefined && icon && icon.length > 100) {
      validationErrors.push('Icon must not exceed 100 characters');
    }

    // Validate service status
    if (serviceStatus !== undefined && ![0, 1].includes(serviceStatus)) {
      validationErrors.push('Service status must be 0 (inactive) or 1 (active)');
    }

    // Create temporary category instance for form validation
    if (form && form.fields) {
      const tempCategory = ServiceCategory.build({
        serviceCategory: serviceCategory?.trim() || category.serviceCategory,
        form: form,
        companyId,
      });

      // Validate form configuration
      const formConfigErrors = tempCategory.validateFormConfiguration();
      if (formConfigErrors.length > 0) {
        validationErrors.push(...formConfigErrors);
      }
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    // Check if category name already exists (excluding current category)
    if (serviceCategory && serviceCategory.trim() !== category.serviceCategory) {
      const existingCategory = await ServiceCategory.findOne({
        where: {
          serviceCategory: serviceCategory.trim(),
          companyId,
          id: { [Op.ne]: id },
        },
      });

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: 'Service category with this name already exists',
        });
      }
    }

    await category.update({
      serviceCategory: serviceCategory?.trim() || category.serviceCategory,
      description: description !== undefined ? description : category.description,
      icon: icon !== undefined ? icon : category.icon,
      color: color || category.color,
      sortOrder: sortOrder !== undefined ? sortOrder : category.sortOrder,
      form: form !== undefined ? form : category.form,
      styleView: styleView !== undefined ? styleView : category.styleView,
      serviceStatus: serviceStatus !== undefined ? serviceStatus : category.serviceStatus,
      updatedBy: userId,
    });

    res.json({
      success: true,
      message: 'Service category updated successfully',
      data: category,
    });
  } catch (error) {
    console.error('Error updating service category:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating service category',
      error: error.message,
    });
  }
};

// @desc    Delete service category
// @route   DELETE /api/service-categories/:id
// @access  Private
export const deleteServiceCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { companyId } = req.user;

    const category = await ServiceCategory.findOne({
      where: { id, companyId },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Service category not found',
      });
    }

    // Check if category has services
    const servicesCount = await Service.count({
      where: { serviceCategoryId: id },
    });

    if (servicesCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete category. It has ${servicesCount} associated services.`,
      });
    }

    await category.destroy();

    res.json({
      success: true,
      message: 'Service category deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting service category:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting service category',
      error: error.message,
    });
  }
};

// @desc    Get service categories statistics
// @route   GET /api/service-categories/stats
// @access  Private
export const getServiceCategoriesStats = async (req, res) => {
  try {
    const { companyId } = req.user;

    const [
      totalCategories,
      activeCategories,
      inactiveCategories,
      categoriesWithServices,
      totalServices
    ] = await Promise.all([
      // Total service categories
      ServiceCategory.count({
        where: { companyId }
      }),

      // Active service categories
      ServiceCategory.count({
        where: { companyId, serviceStatus: 1 }
      }),

      // Inactive service categories
      ServiceCategory.count({
        where: { companyId, serviceStatus: 0 }
      }),

      // Categories with services
      ServiceCategory.count({
        where: { companyId },
        include: [{
          model: Service,
          as: 'services',
          required: true,
          attributes: []
        }]
      }),

      // Total services in all categories
      Service.count({
        include: [{
          model: ServiceCategory,
          as: 'category',
          where: { companyId },
          attributes: []
        }]
      })
    ]);

    res.json({
      success: true,
      data: {
        totalCategories,
        activeCategories,
        inactiveCategories,
        categoriesWithServices,
        totalServices,
        categoriesWithoutServices: totalCategories - categoriesWithServices
      }
    });
  } catch (error) {
    console.error('Error fetching service categories stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching service categories statistics',
      error: error.message,
    });
  }
};

// @desc    Export service categories to CSV/Excel
// @route   GET /api/service-categories/export
// @access  Private
export const exportServiceCategories = asyncHandler(async (req, res) => {
  const { companyId } = req.user;
  const { format = 'xlsx', status, search } = req.query;

  try {
    const whereClause = { companyId };

    if (status !== undefined) {
      whereClause.serviceStatus = parseInt(status);
    }

    if (search) {
      whereClause.serviceCategory = {
        [Op.iLike]: `%${search}%`,
      };
    }

    const categories = await ServiceCategory.findAll({
      where: whereClause,
      include: [{
        model: Service,
        as: 'services',
        attributes: [],
        required: false,
      }],
      attributes: [
        'id',
        'serviceCategory',
        'description',
        'icon',
        'color',
        'sortOrder',
        'serviceStatus',
        'styleView',
        'isDefault',
        'createdAt',
        'updatedAt',
        [
          ServiceCategory.sequelize.fn('COUNT', ServiceCategory.sequelize.col('services.id')),
          'servicesCount'
        ]
      ],
      group: ['ServiceCategory.id'],
      order: [['sortOrder', 'ASC'], ['serviceCategory', 'ASC']],
      raw: false,
    });

    const { generateServiceCategoryExport } = await import('../utils/excelProcessor.js');

    const exportData = categories.map(category => ({
      id: category.id,
      categoryName: category.serviceCategory,
      description: category.description || '',
      icon: category.icon || '',
      color: category.color || '#3B82F6',
      sortOrder: category.sortOrder || 0,
      status: category.serviceStatus === 1 ? 'Active' : 'Inactive',
      styleView: category.styleView || 1,
      isDefault: category.isDefault ? 'Yes' : 'No',
      servicesCount: category.dataValues?.servicesCount || 0,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt
    }));

    const exportBuffer = generateServiceCategoryExport(exportData, format);

    const filename = `service_categories_export_${new Date().toISOString().split('T')[0]}.${format}`;

    res.setHeader('Content-Type',
      format === 'csv'
        ? 'text/csv'
        : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    res.setHeader('Content-Length', exportBuffer.length);

    logger.info(`Service categories exported by user: ${req.user.id}, format: ${format}, count: ${exportData.length}`);

    res.send(exportBuffer);
  } catch (error) {
    logger.error('Error exporting service categories:', error);
    throw new AppError('Failed to export service categories', 500);
  }
});

// @desc    Import service categories from CSV/Excel file
// @route   POST /api/service-categories/import
// @access  Private
export const importServiceCategories = asyncHandler(async (req, res) => {
  const { companyId, id: userId } = req.user;

  // Check if file was uploaded
  if (!req.file) {
    throw new AppError('Please upload a CSV or Excel file', 400);
  }

  const { parseExcelFile, validateServiceCategoryData, cleanupTempFile } = await import('../utils/excelProcessor.js');

  let tempFilePath = null;

  try {
    // Parse the uploaded file
    let parsedData;
    if (req.file.buffer) {
      // File is in memory
      parsedData = parseExcelFile(req.file.buffer);
    } else {
      // File is on disk
      tempFilePath = req.file.path;
      parsedData = parseExcelFile(tempFilePath);
    }

    if (!parsedData || parsedData.length === 0) {
      throw new AppError('File is empty or contains no valid data', 400);
    }

    // Validate and process data
    const results = {
      success: 0,
      failed: 0,
      errors: [],
      duplicates: 0
    };

    for (let i = 0; i < parsedData.length; i++) {
      const row = parsedData[i];
      const rowNumber = i + 2; // Account for header row

      try {
        // Validate required fields
        const validation = validateServiceCategoryData(row);
        if (!validation.isValid) {
          results.failed++;
          results.errors.push({
            row: rowNumber,
            errors: validation.errors
          });
          continue;
        }

        // Check for existing category with same name
        const existingCategory = await ServiceCategory.findOne({
          where: {
            serviceCategory: row.categoryName?.trim(),
            companyId
          }
        });

        if (existingCategory) {
          results.duplicates++;
          results.errors.push({
            row: rowNumber,
            errors: [`Category '${row.categoryName}' already exists`]
          });
          continue;
        }

        // Create new category
        await ServiceCategory.create({
          serviceCategory: row.categoryName?.trim(),
          description: row.description?.trim() || null,
          icon: row.icon?.trim() || null,
          color: row.color || '#3B82F6',
          sortOrder: parseInt(row.sortOrder) || 0,
          serviceStatus: row.status?.toLowerCase() === 'active' ? 1 : 0,
          styleView: parseInt(row.styleView) || 1,
          isDefault: row.isDefault?.toLowerCase() === 'yes',
          form: { fields: [] }, // Default empty form
          companyId,
          createdBy: userId,
        });

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          row: rowNumber,
          errors: [error.message]
        });
      }
    }

    // Clean up temporary file
    if (tempFilePath) {
      cleanupTempFile(tempFilePath);
    }

    logger.info(`Service category import completed: ${results.success} success, ${results.failed} failed by user: ${userId}`);

    res.json({
      success: true,
      message: `Import completed: ${results.success} categories imported successfully${results.failed > 0 ? `, ${results.failed} failed` : ''}`,
      data: results
    });
  } catch (error) {
    // Clean up temporary file on error
    if (tempFilePath) {
      cleanupTempFile(tempFilePath);
    }

    logger.error('Error importing service categories:', error);
    throw new AppError(`Failed to import service categories: ${error.message}`, 500);
  }
});

// @desc    Download service category import template
// @route   GET /api/service-categories/template
// @access  Private
export const downloadServiceCategoryTemplate = asyncHandler(async (req, res) => {
  try {
    const { generateServiceCategoryTemplate } = await import('../utils/excelProcessor.js');

    const templateBuffer = generateServiceCategoryTemplate();

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=service_category_import_template.xlsx');
    res.setHeader('Content-Length', templateBuffer.length);

    logger.info(`Service category template downloaded by user: ${req.user.id}`);

    res.send(templateBuffer);
  } catch (error) {
    logger.error('Error generating service category template:', error);
    throw new AppError('Failed to generate template', 500);
  }
});
