import { ServiceCategory, Service } from '../models/index.js';
import { Op } from 'sequelize';

// @desc    Get all service categories
// @route   GET /api/service-categories
// @access  Private
export const getServiceCategories = async (req, res) => {
  try {
    const { companyId } = req.user;
    const { status, search, page = 1, limit = 10 } = req.query;

    const whereClause = { companyId };
    
    if (status !== undefined) {
      whereClause.serviceStatus = parseInt(status);
    }

    if (search) {
      whereClause.serviceCategory = {
        [Op.iLike]: `%${search}%`,
      };
    }

    const offset = (page - 1) * limit;

    const { count, rows: categories } = await ServiceCategory.findAndCountAll({
      where: whereClause,
      include: [{
        model: Service,
        as: 'services',
        attributes: [],
        required: false,
      }],
      attributes: [
        'id',
        'serviceCategory',
        'serviceStatus',
        'styleView',
        'form',
        'description',
        'icon',
        'color',
        'sortOrder',
        'isDefault',
        'createdAt',
        'updatedAt',
        [
          ServiceCategory.sequelize.fn('COUNT', ServiceCategory.sequelize.col('services.id')),
          'servicesCount'
        ]
      ],
      group: ['ServiceCategory.id'],
      order: [['sortOrder', 'ASC'], ['serviceCategory', 'ASC']],
      limit: parseInt(limit),
      offset,
      subQuery: false,
    });

    res.json({
      success: true,
      data: {
        categories,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count.length / limit),
          totalItems: count.length,
          itemsPerPage: parseInt(limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching service categories:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching service categories',
      error: error.message,
    });
  }
};

// @desc    Get active service categories for dropdown
// @route   GET /api/service-categories/active
// @access  Private
export const getActiveServiceCategories = async (req, res) => {
  try {
    const { companyId } = req.user;

    const categories = await ServiceCategory.getActiveCategories(companyId);

    res.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error('Error fetching active service categories:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching active service categories',
      error: error.message,
    });
  }
};

// @desc    Get service category by ID
// @route   GET /api/service-categories/:id
// @access  Private
export const getServiceCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    const { companyId } = req.user;

    const category = await ServiceCategory.findOne({
      where: { id, companyId },
      include: [{
        model: Service,
        as: 'services',
        attributes: ['id', 'serviceCode', 'title', 'status', 'createdAt'],
        limit: 10,
        order: [['createdAt', 'DESC']],
      }],
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Service category not found',
      });
    }

    res.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('Error fetching service category:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching service category',
      error: error.message,
    });
  }
};

// @desc    Create new service category
// @route   POST /api/service-categories
// @access  Private
export const createServiceCategory = async (req, res) => {
  try {
    const { companyId, id: userId } = req.user;
    const {
      serviceCategory,
      description,
      icon,
      color,
      sortOrder,
      form,
      styleView,
    } = req.body;

    // Check if category already exists
    const existingCategory = await ServiceCategory.findOne({
      where: {
        serviceCategory: serviceCategory.trim(),
        companyId,
      },
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Service category with this name already exists',
      });
    }

    const category = await ServiceCategory.create({
      serviceCategory: serviceCategory.trim(),
      description,
      icon,
      color: color || '#3B82F6',
      sortOrder: sortOrder || 0,
      form: form || {},
      styleView: styleView || 1,
      serviceStatus: 1,
      companyId,
      createdBy: userId,
    });

    res.status(201).json({
      success: true,
      message: 'Service category created successfully',
      data: category,
    });
  } catch (error) {
    console.error('Error creating service category:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating service category',
      error: error.message,
    });
  }
};

// @desc    Update service category
// @route   PUT /api/service-categories/:id
// @access  Private
export const updateServiceCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { companyId, id: userId } = req.user;
    const {
      serviceCategory,
      description,
      icon,
      color,
      sortOrder,
      form,
      styleView,
      serviceStatus,
    } = req.body;

    const category = await ServiceCategory.findOne({
      where: { id, companyId },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Service category not found',
      });
    }

    // Check if category name already exists (excluding current category)
    if (serviceCategory && serviceCategory.trim() !== category.serviceCategory) {
      const existingCategory = await ServiceCategory.findOne({
        where: {
          serviceCategory: serviceCategory.trim(),
          companyId,
          id: { [Op.ne]: id },
        },
      });

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: 'Service category with this name already exists',
        });
      }
    }

    await category.update({
      serviceCategory: serviceCategory?.trim() || category.serviceCategory,
      description: description !== undefined ? description : category.description,
      icon: icon !== undefined ? icon : category.icon,
      color: color || category.color,
      sortOrder: sortOrder !== undefined ? sortOrder : category.sortOrder,
      form: form !== undefined ? form : category.form,
      styleView: styleView !== undefined ? styleView : category.styleView,
      serviceStatus: serviceStatus !== undefined ? serviceStatus : category.serviceStatus,
      updatedBy: userId,
    });

    res.json({
      success: true,
      message: 'Service category updated successfully',
      data: category,
    });
  } catch (error) {
    console.error('Error updating service category:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating service category',
      error: error.message,
    });
  }
};

// @desc    Delete service category
// @route   DELETE /api/service-categories/:id
// @access  Private
export const deleteServiceCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { companyId } = req.user;

    const category = await ServiceCategory.findOne({
      where: { id, companyId },
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Service category not found',
      });
    }

    // Check if category has services
    const servicesCount = await Service.count({
      where: { serviceCategoryId: id },
    });

    if (servicesCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete category. It has ${servicesCount} associated services.`,
      });
    }

    await category.destroy();

    res.json({
      success: true,
      message: 'Service category deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting service category:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting service category',
      error: error.message,
    });
  }
};
