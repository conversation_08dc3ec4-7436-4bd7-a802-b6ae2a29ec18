/**
 * Test script for Service Category Export/Import functionality
 * This script tests the newly implemented export/import endpoints
 */

import fetch from 'node-fetch';
import fs from 'fs';
import FormData from 'form-data';

const BASE_URL = 'http://localhost:8080/api';
const TEST_TOKEN = 'your-jwt-token-here'; // Replace with actual token

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

/**
 * Test export functionality
 */
async function testExport() {
  console.log('🧪 Testing Service Category Export...');
  
  try {
    // Test XLSX export
    const xlsxResponse = await fetch(`${BASE_URL}/service-categories/export?format=xlsx`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    if (xlsxResponse.ok) {
      console.log('✅ XLSX Export: Success');
      const buffer = await xlsxResponse.buffer();
      fs.writeFileSync('test_export.xlsx', buffer);
      console.log('📁 XLSX file saved as test_export.xlsx');
    } else {
      console.log('❌ XLSX Export: Failed', xlsxResponse.status, xlsxResponse.statusText);
    }
    
    // Test CSV export
    const csvResponse = await fetch(`${BASE_URL}/service-categories/export?format=csv`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    if (csvResponse.ok) {
      console.log('✅ CSV Export: Success');
      const buffer = await csvResponse.buffer();
      fs.writeFileSync('test_export.csv', buffer);
      console.log('📁 CSV file saved as test_export.csv');
    } else {
      console.log('❌ CSV Export: Failed', csvResponse.status, csvResponse.statusText);
    }
    
  } catch (error) {
    console.error('❌ Export test failed:', error.message);
  }
}

/**
 * Test template download functionality
 */
async function testTemplateDownload() {
  console.log('🧪 Testing Template Download...');
  
  try {
    const response = await fetch(`${BASE_URL}/service-categories/template`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    if (response.ok) {
      console.log('✅ Template Download: Success');
      const buffer = await response.buffer();
      fs.writeFileSync('service_category_template.xlsx', buffer);
      console.log('📁 Template saved as service_category_template.xlsx');
    } else {
      console.log('❌ Template Download: Failed', response.status, response.statusText);
    }
    
  } catch (error) {
    console.error('❌ Template download test failed:', error.message);
  }
}

/**
 * Test import functionality
 */
async function testImport() {
  console.log('🧪 Testing Service Category Import...');
  
  try {
    // First, ensure we have a template file
    if (!fs.existsSync('service_category_template.xlsx')) {
      console.log('📥 Downloading template first...');
      await testTemplateDownload();
    }
    
    // Create form data for import
    const form = new FormData();
    form.append('file', fs.createReadStream('service_category_template.xlsx'));
    
    const response = await fetch(`${BASE_URL}/service-categories/import`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        ...form.getHeaders()
      },
      body: form
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Import: Success');
      console.log('📊 Import Results:', result);
    } else {
      console.log('❌ Import: Failed', response.status, response.statusText);
      console.log('📊 Error Details:', result);
    }
    
  } catch (error) {
    console.error('❌ Import test failed:', error.message);
  }
}

/**
 * Test service categories API endpoints
 */
async function testServiceCategoriesAPI() {
  console.log('🧪 Testing Service Categories API...');
  
  try {
    const response = await fetch(`${BASE_URL}/service-categories`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Service Categories API: Success');
      console.log(`📊 Found ${data.data?.categories?.length || 0} categories`);
    } else {
      console.log('❌ Service Categories API: Failed', response.status, response.statusText);
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting Service Category Export/Import Tests...\n');
  
  // Test basic API first
  await testServiceCategoriesAPI();
  console.log('');
  
  // Test export functionality
  await testExport();
  console.log('');
  
  // Test template download
  await testTemplateDownload();
  console.log('');
  
  // Test import functionality
  await testImport();
  console.log('');
  
  console.log('✨ All tests completed!');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { testExport, testImport, testTemplateDownload, testServiceCategoriesAPI };
