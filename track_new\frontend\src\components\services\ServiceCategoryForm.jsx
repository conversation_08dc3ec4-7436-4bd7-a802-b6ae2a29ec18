import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  SwatchIcon
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import LoadingSpinner from '../common/LoadingSpinner';
import '../../styles/design-system.css';

/**
 * Service Category Form Component
 * 
 * Form for creating/editing service categories with dynamic form builder
 */
const ServiceCategoryForm = ({ category, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Form data
  const [formData, setFormData] = useState({
    serviceCategory: '',
    description: '',
    icon: '',
    color: '#3B82F6',
    sortOrder: 0,
    serviceStatus: 1,
    styleView: 1,
    form: {
      fields: []
    }
  });

  // Dynamic form fields
  const [formFields, setFormFields] = useState([]);

  // Available field types
  const fieldTypes = [
    { value: 'text', label: 'Text Input' },
    { value: 'textarea', label: 'Textarea' },
    { value: 'number', label: 'Number' },
    { value: 'email', label: 'Email' },
    { value: 'phone', label: 'Phone' },
    { value: 'date', label: 'Date' },
    { value: 'datetime', label: 'Date & Time' },
    { value: 'time', label: 'Time' },
    { value: 'select', label: 'Dropdown' },
    { value: 'radio', label: 'Radio Buttons' },
    { value: 'checkbox', label: 'Checkboxes' },
    { value: 'file', label: 'File Upload' },
    { value: 'url', label: 'URL' },
    { value: 'password', label: 'Password' },
    { value: 'range', label: 'Range Slider' },
    { value: 'color', label: 'Color Picker' }
  ];

  // Form preview mode
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState({});

  // Field grouping
  const [fieldGroups, setFieldGroups] = useState([]);
  const [showGrouping, setShowGrouping] = useState(false);

  // Form templates
  const formTemplates = [
    {
      name: 'Basic Service Form',
      fields: [
        { name: 'customer_name', label: 'Customer Name', type: 'text', required: true },
        { name: 'service_type', label: 'Service Type', type: 'select', required: true, options: ['Repair', 'Maintenance', 'Installation'] },
        { name: 'description', label: 'Description', type: 'textarea', required: true }
      ]
    },
    {
      name: 'Detailed Service Form',
      fields: [
        { name: 'customer_name', label: 'Customer Name', type: 'text', required: true },
        { name: 'email', label: 'Email', type: 'email', required: true },
        { name: 'phone', label: 'Phone', type: 'phone', required: true },
        { name: 'service_type', label: 'Service Type', type: 'select', required: true, options: ['Repair', 'Maintenance', 'Installation', 'Consultation'] },
        { name: 'priority', label: 'Priority', type: 'radio', required: true, options: ['Low', 'Medium', 'High', 'Urgent'] },
        { name: 'scheduled_date', label: 'Scheduled Date', type: 'date', required: true },
        { name: 'estimated_cost', label: 'Estimated Cost', type: 'number', required: false },
        { name: 'description', label: 'Service Description', type: 'textarea', required: true }
      ]
    },
    {
      name: 'Customer Feedback Form',
      fields: [
        { name: 'customer_name', label: 'Customer Name', type: 'text', required: true },
        { name: 'service_rating', label: 'Service Rating', type: 'range', required: true, min: 1, max: 5 },
        { name: 'satisfaction', label: 'Overall Satisfaction', type: 'radio', required: true, options: ['Very Satisfied', 'Satisfied', 'Neutral', 'Dissatisfied', 'Very Dissatisfied'] },
        { name: 'recommend', label: 'Would Recommend', type: 'radio', required: true, options: ['Yes', 'No', 'Maybe'] },
        { name: 'feedback', label: 'Additional Feedback', type: 'textarea', required: false }
      ]
    }
  ];

  // Color presets
  const colorPresets = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
    '#F97316', '#6366F1', '#14B8A6', '#F43F5E'
  ];

  // Initialize form data
  useEffect(() => {
    if (category) {
      setFormData({
        serviceCategory: category.serviceCategory || '',
        description: category.description || '',
        icon: category.icon || '',
        color: category.color || '#3B82F6',
        sortOrder: category.sortOrder || 0,
        serviceStatus: category.serviceStatus || 1,
        styleView: category.styleView || 1,
        form: category.form || { fields: [] }
      });
      setFormFields(category.form?.fields || []);
    }
  }, [category]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }));
  };

  // Add new form field
  const addFormField = () => {
    const newField = {
      id: Date.now().toString(),
      name: '',
      label: '',
      type: 'text',
      required: false,
      placeholder: '',
      helpText: '',
      options: [],
      validation: {},
      conditional: {
        enabled: false,
        field: '',
        operator: 'equals',
        value: ''
      },
      permissions: {
        roles: [],
        hideForRoles: []
      },
      styling: {
        width: 'full',
        className: ''
      },
      group: ''
    };
    setFormFields(prev => [...prev, newField]);
  };

  // Add form field from template
  const addFieldsFromTemplate = (template) => {
    const newFields = template.fields.map(field => ({
      ...field,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      validation: field.validation || {},
      conditional: {
        enabled: false,
        field: '',
        operator: 'equals',
        value: ''
      },
      permissions: {
        roles: [],
        hideForRoles: []
      },
      styling: {
        width: field.width || 'full',
        className: ''
      },
      group: '',
      helpText: field.helpText || ''
    }));
    setFormFields(prev => [...prev, ...newFields]);
  };

  // Update form field
  const updateFormField = (fieldId, updates) => {
    setFormFields(prev => prev.map(field => 
      field.id === fieldId ? { ...field, ...updates } : field
    ));
  };

  // Remove form field
  const removeFormField = (fieldId) => {
    setFormFields(prev => prev.filter(field => field.id !== fieldId));
  };

  // Move field up/down
  const moveField = (fieldId, direction) => {
    setFormFields(prev => {
      const index = prev.findIndex(field => field.id === fieldId);
      if (index === -1) return prev;
      
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      if (newIndex < 0 || newIndex >= prev.length) return prev;
      
      const newFields = [...prev];
      [newFields[index], newFields[newIndex]] = [newFields[newIndex], newFields[index]];
      return newFields;
    });
  };

  // Add option to select/radio/checkbox field
  const addFieldOption = (fieldId) => {
    updateFormField(fieldId, {
      options: [...(formFields.find(f => f.id === fieldId)?.options || []), '']
    });
  };

  // Update field option
  const updateFieldOption = (fieldId, optionIndex, value) => {
    const field = formFields.find(f => f.id === fieldId);
    if (!field) return;
    
    const newOptions = [...field.options];
    newOptions[optionIndex] = value;
    updateFormField(fieldId, { options: newOptions });
  };

  // Remove field option
  const removeFieldOption = (fieldId, optionIndex) => {
    const field = formFields.find(f => f.id === fieldId);
    if (!field) return;

    const newOptions = field.options.filter((_, index) => index !== optionIndex);
    updateFormField(fieldId, { options: newOptions });
  };

  // Form preview functions
  const togglePreview = () => {
    setShowPreview(!showPreview);
    if (!showPreview) {
      // Initialize preview data with empty values
      const initialData = {};
      formFields.forEach(field => {
        if (field.type === 'checkbox') {
          initialData[field.name] = [];
        } else {
          initialData[field.name] = '';
        }
      });
      setPreviewData(initialData);
    }
  };

  const updatePreviewData = (fieldName, value) => {
    setPreviewData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Check if field should be visible based on conditions
  const isFieldVisible = (field) => {
    if (!field.conditional?.enabled) return true;

    const conditionField = formFields.find(f => f.name === field.conditional.field);
    if (!conditionField) return true;

    const conditionValue = previewData[field.conditional.field];
    const targetValue = field.conditional.value;

    switch (field.conditional.operator) {
      case 'equals':
        return conditionValue === targetValue;
      case 'not_equals':
        return conditionValue !== targetValue;
      case 'contains':
        return conditionValue && conditionValue.includes(targetValue);
      case 'greater_than':
        return parseFloat(conditionValue) > parseFloat(targetValue);
      case 'less_than':
        return parseFloat(conditionValue) < parseFloat(targetValue);
      default:
        return true;
    }
  };

  // Validate preview form
  const validatePreviewForm = () => {
    const errors = {};
    formFields.forEach(field => {
      if (field.required && isFieldVisible(field)) {
        const value = previewData[field.name];
        if (!value || (Array.isArray(value) && value.length === 0) || value.toString().trim() === '') {
          errors[field.name] = `${field.label} is required`;
        }
      }

      // Type-specific validation
      if (field.type === 'email' && previewData[field.name]) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(previewData[field.name])) {
          errors[field.name] = 'Please enter a valid email address';
        }
      }

      if (field.type === 'url' && previewData[field.name]) {
        try {
          new URL(previewData[field.name]);
        } catch {
          errors[field.name] = 'Please enter a valid URL';
        }
      }

      // Custom validation rules
      if (field.validation?.pattern && previewData[field.name]) {
        const regex = new RegExp(field.validation.pattern);
        if (!regex.test(previewData[field.name])) {
          errors[field.name] = field.validation.patternMessage || 'Invalid format';
        }
      }
    });

    return errors;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Prepare form data with dynamic fields
      const submitData = {
        ...formData,
        form: {
          fields: formFields.map(field => ({
            name: field.name,
            label: field.label,
            type: field.type,
            required: field.required,
            placeholder: field.placeholder,
            ...(field.options?.length > 0 && { options: field.options.filter(opt => opt.trim()) }),
            ...(field.validation && Object.keys(field.validation).length > 0 && { validation: field.validation })
          }))
        }
      };

      const url = category 
        ? `/api/service-categories/${category.id}`
        : '/api/service-categories';
      
      const method = category ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('tracknew_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      if (!response.ok) {
        throw new Error('Failed to save category');
      }

      const data = await response.json();
      
      if (data.success) {
        onSuccess();
      } else {
        throw new Error(data.message || 'Failed to save category');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error saving category:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Category Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Category Name *
            </label>
            <input
              type="text"
              name="serviceCategory"
              value={formData.serviceCategory}
              onChange={handleInputChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter category name"
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              name="serviceStatus"
              value={formData.serviceStatus}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={1}>Active</option>
              <option value={0}>Inactive</option>
            </select>
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Color
            </label>
            <div className="mt-1 flex items-center space-x-2">
              <input
                type="color"
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                className="h-10 w-16 border border-gray-300 rounded cursor-pointer"
              />
              <div className="flex flex-wrap gap-1">
                {colorPresets.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, color }))}
                    className="w-6 h-6 rounded border-2 border-gray-300 hover:border-gray-400"
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Sort Order */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Sort Order
            </label>
            <input
              type="number"
              name="sortOrder"
              value={formData.sortOrder}
              onChange={handleInputChange}
              min="0"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter category description"
          />
        </div>

        {/* Icon */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Icon (CSS class or emoji)
          </label>
          <input
            type="text"
            name="icon"
            value={formData.icon}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., 🔧 or fa-wrench"
          />
        </div>
      </div>

      {/* Dynamic Form Builder */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Dynamic Form Fields</h3>
          <div className="flex items-center space-x-2">
            {/* Form Templates Dropdown */}
            <div className="relative">
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    const template = formTemplates.find(t => t.name === e.target.value);
                    if (template) {
                      addFieldsFromTemplate(template);
                      e.target.value = '';
                    }
                  }
                }}
                className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="">Load Template...</option>
                {formTemplates.map((template) => (
                  <option key={template.name} value={template.name}>
                    {template.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Preview Toggle */}
            {formFields.length > 0 && (
              <button
                type="button"
                onClick={togglePreview}
                className={clsx(
                  "inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md",
                  showPreview
                    ? "border-orange-300 text-orange-700 bg-orange-100 hover:bg-orange-200"
                    : "border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
                )}
              >
                {showPreview ? 'Hide Preview' : 'Preview Form'}
              </button>
            )}

            {/* Add Field Button */}
            <button
              type="button"
              onClick={addFormField}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Field
            </button>
          </div>
        </div>

        {formFields.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <SwatchIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2">No form fields added yet</p>
            <p className="text-sm">Add fields to create a dynamic form for this category</p>
          </div>
        ) : (
          <div className="space-y-4">
            {formFields.map((field, index) => (
              <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-900">
                    Field {index + 1}
                  </h4>
                  <div className="flex items-center space-x-1">
                    <button
                      type="button"
                      onClick={() => moveField(field.id, 'up')}
                      disabled={index === 0}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    >
                      <ArrowUpIcon className="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => moveField(field.id, 'down')}
                      disabled={index === formFields.length - 1}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    >
                      <ArrowDownIcon className="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => removeFormField(field.id)}
                      className="p-1 text-red-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Field Name */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Field Name *
                    </label>
                    <input
                      type="text"
                      value={field.name}
                      onChange={(e) => updateFormField(field.id, { name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="field_name"
                    />
                  </div>

                  {/* Field Label */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Label *
                    </label>
                    <input
                      type="text"
                      value={field.label}
                      onChange={(e) => updateFormField(field.id, { label: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Field Label"
                    />
                  </div>

                  {/* Field Type */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Type
                    </label>
                    <select
                      value={field.type}
                      onChange={(e) => updateFormField(field.id, { type: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {fieldTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  {/* Placeholder */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Placeholder
                    </label>
                    <input
                      type="text"
                      value={field.placeholder}
                      onChange={(e) => updateFormField(field.id, { placeholder: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter placeholder text"
                    />
                  </div>

                  {/* Required */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`required-${field.id}`}
                      checked={field.required}
                      onChange={(e) => updateFormField(field.id, { required: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor={`required-${field.id}`} className="ml-2 block text-sm text-gray-700">
                      Required field
                    </label>
                  </div>
                </div>

                {/* Advanced Properties */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  {/* Help Text */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Help Text
                    </label>
                    <input
                      type="text"
                      value={field.helpText || ''}
                      onChange={(e) => updateFormField(field.id, { helpText: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Help text for users"
                    />
                  </div>

                  {/* Field Width */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Field Width
                    </label>
                    <select
                      value={field.styling?.width || 'full'}
                      onChange={(e) => updateFormField(field.id, {
                        styling: { ...field.styling, width: e.target.value }
                      })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="full">Full Width</option>
                      <option value="half">Half Width</option>
                      <option value="third">One Third</option>
                      <option value="quarter">One Quarter</option>
                    </select>
                  </div>
                </div>

                {/* Validation Rules */}
                {['text', 'textarea', 'email', 'url'].includes(field.type) && (
                  <div className="mt-4">
                    <label className="block text-xs font-medium text-gray-700 mb-2">
                      Validation Rules
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-xs text-gray-600">Min Length</label>
                        <input
                          type="number"
                          value={field.validation?.minLength || ''}
                          onChange={(e) => updateFormField(field.id, {
                            validation: { ...field.validation, minLength: parseInt(e.target.value) || undefined }
                          })}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                          placeholder="0"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600">Max Length</label>
                        <input
                          type="number"
                          value={field.validation?.maxLength || ''}
                          onChange={(e) => updateFormField(field.id, {
                            validation: { ...field.validation, maxLength: parseInt(e.target.value) || undefined }
                          })}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                          placeholder="100"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600">Pattern (Regex)</label>
                        <input
                          type="text"
                          value={field.validation?.pattern || ''}
                          onChange={(e) => updateFormField(field.id, {
                            validation: { ...field.validation, pattern: e.target.value }
                          })}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                          placeholder="^[A-Za-z]+$"
                        />
                      </div>
                    </div>
                    {field.validation?.pattern && (
                      <div className="mt-2">
                        <label className="block text-xs text-gray-600">Pattern Error Message</label>
                        <input
                          type="text"
                          value={field.validation?.patternMessage || ''}
                          onChange={(e) => updateFormField(field.id, {
                            validation: { ...field.validation, patternMessage: e.target.value }
                          })}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                          placeholder="Please enter a valid format"
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Number Field Validation */}
                {['number', 'range'].includes(field.type) && (
                  <div className="mt-4">
                    <label className="block text-xs font-medium text-gray-700 mb-2">
                      Number Validation
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-600">Minimum Value</label>
                        <input
                          type="number"
                          value={field.min || ''}
                          onChange={(e) => updateFormField(field.id, { min: parseFloat(e.target.value) || undefined })}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600">Maximum Value</label>
                        <input
                          type="number"
                          value={field.max || ''}
                          onChange={(e) => updateFormField(field.id, { max: parseFloat(e.target.value) || undefined })}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Conditional Logic */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-xs font-medium text-gray-700">
                      Conditional Visibility
                    </label>
                    <input
                      type="checkbox"
                      checked={field.conditional?.enabled || false}
                      onChange={(e) => updateFormField(field.id, {
                        conditional: { ...field.conditional, enabled: e.target.checked }
                      })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  {field.conditional?.enabled && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                      <select
                        value={field.conditional?.field || ''}
                        onChange={(e) => updateFormField(field.id, {
                          conditional: { ...field.conditional, field: e.target.value }
                        })}
                        className="block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                      >
                        <option value="">Select Field</option>
                        {formFields.filter(f => f.id !== field.id).map(f => (
                          <option key={f.id} value={f.name}>{f.label || f.name}</option>
                        ))}
                      </select>
                      <select
                        value={field.conditional?.operator || 'equals'}
                        onChange={(e) => updateFormField(field.id, {
                          conditional: { ...field.conditional, operator: e.target.value }
                        })}
                        className="block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                      >
                        <option value="equals">Equals</option>
                        <option value="not_equals">Not Equals</option>
                        <option value="contains">Contains</option>
                        <option value="greater_than">Greater Than</option>
                        <option value="less_than">Less Than</option>
                      </select>
                      <input
                        type="text"
                        value={field.conditional?.value || ''}
                        onChange={(e) => updateFormField(field.id, {
                          conditional: { ...field.conditional, value: e.target.value }
                        })}
                        className="block w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
                        placeholder="Value"
                      />
                    </div>
                  )}
                </div>

                {/* Options for select, radio, checkbox */}
                {['select', 'radio', 'checkbox'].includes(field.type) && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-xs font-medium text-gray-700">
                        Options
                      </label>
                      <button
                        type="button"
                        onClick={() => addFieldOption(field.id)}
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        + Add Option
                      </button>
                    </div>
                    <div className="space-y-2">
                      {(field.options || []).map((option, optionIndex) => (
                        <div key={optionIndex} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={option}
                            onChange={(e) => updateFieldOption(field.id, optionIndex, e.target.value)}
                            className="flex-1 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder={`Option ${optionIndex + 1}`}
                          />
                          <button
                            type="button"
                            onClick={() => removeFieldOption(field.id, optionIndex)}
                            className="text-red-400 hover:text-red-600"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Form Preview */}
      {showPreview && formFields.length > 0 && (
        <div className="space-y-4">
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Form Preview</h3>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <div className="space-y-4">
                {formFields.filter(field => isFieldVisible(field)).map((field) => (
                  <div key={field.id} className={clsx(
                    "space-y-2",
                    field.styling?.width === 'half' && "md:w-1/2",
                    field.styling?.width === 'third' && "md:w-1/3",
                    field.styling?.width === 'quarter' && "md:w-1/4"
                  )}>
                    <label className="block text-sm font-medium text-gray-700">
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </label>

                    {field.helpText && (
                      <p className="text-xs text-gray-500">{field.helpText}</p>
                    )}

                    {/* Render different field types */}
                    {field.type === 'text' && (
                      <input
                        type="text"
                        value={previewData[field.name] || ''}
                        onChange={(e) => updatePreviewData(field.name, e.target.value)}
                        placeholder={field.placeholder}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    )}

                    {field.type === 'textarea' && (
                      <textarea
                        value={previewData[field.name] || ''}
                        onChange={(e) => updatePreviewData(field.name, e.target.value)}
                        placeholder={field.placeholder}
                        rows={3}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    )}

                    {field.type === 'number' && (
                      <input
                        type="number"
                        value={previewData[field.name] || ''}
                        onChange={(e) => updatePreviewData(field.name, e.target.value)}
                        placeholder={field.placeholder}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    )}

                    {field.type === 'email' && (
                      <input
                        type="email"
                        value={previewData[field.name] || ''}
                        onChange={(e) => updatePreviewData(field.name, e.target.value)}
                        placeholder={field.placeholder}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    )}

                    {field.type === 'date' && (
                      <input
                        type="date"
                        value={previewData[field.name] || ''}
                        onChange={(e) => updatePreviewData(field.name, e.target.value)}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    )}

                    {field.type === 'select' && (
                      <select
                        value={previewData[field.name] || ''}
                        onChange={(e) => updatePreviewData(field.name, e.target.value)}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select {field.label}</option>
                        {(field.options || []).map((option, idx) => (
                          <option key={idx} value={option}>{option}</option>
                        ))}
                      </select>
                    )}

                    {field.type === 'radio' && (
                      <div className="space-y-2">
                        {(field.options || []).map((option, idx) => (
                          <label key={idx} className="flex items-center">
                            <input
                              type="radio"
                              name={field.name}
                              value={option}
                              checked={previewData[field.name] === option}
                              onChange={(e) => updatePreviewData(field.name, e.target.value)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span className="ml-2 text-sm text-gray-700">{option}</span>
                          </label>
                        ))}
                      </div>
                    )}

                    {field.type === 'checkbox' && (
                      <div className="space-y-2">
                        {(field.options || []).map((option, idx) => (
                          <label key={idx} className="flex items-center">
                            <input
                              type="checkbox"
                              value={option}
                              checked={(previewData[field.name] || []).includes(option)}
                              onChange={(e) => {
                                const currentValues = previewData[field.name] || [];
                                const newValues = e.target.checked
                                  ? [...currentValues, option]
                                  : currentValues.filter(v => v !== option);
                                updatePreviewData(field.name, newValues);
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{option}</span>
                          </label>
                        ))}
                      </div>
                    )}

                    {field.type === 'range' && (
                      <div>
                        <input
                          type="range"
                          min={field.min || 0}
                          max={field.max || 100}
                          value={previewData[field.name] || field.min || 0}
                          onChange={(e) => updatePreviewData(field.name, e.target.value)}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>{field.min || 0}</span>
                          <span className="font-medium">{previewData[field.name] || field.min || 0}</span>
                          <span>{field.max || 100}</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Preview Form Validation */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    const errors = validatePreviewForm();
                    if (Object.keys(errors).length === 0) {
                      alert('Form validation passed! ✅\n\nForm data:\n' + JSON.stringify(previewData, null, 2));
                    } else {
                      alert('Form validation failed! ❌\n\nErrors:\n' + Object.entries(errors).map(([field, error]) => `${field}: ${error}`).join('\n'));
                    }
                  }}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  Test Form Validation
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading && <LoadingSpinner size="sm" className="mr-2" />}
          {category ? 'Update Category' : 'Create Category'}
        </button>
      </div>
    </form>
  );
};

export default ServiceCategoryForm;
