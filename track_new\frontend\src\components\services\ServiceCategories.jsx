import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  UserGroupIcon,
  ChartBarIcon,
  CloudArrowUpIcon,
  CloudArrowDownIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorMessage from '../common/ErrorMessage';
import Modal from '../common/Modal';
import ConfirmDialog from '../common/ConfirmDialog';
import Pagination from '../common/Pagination';
import ServiceCategoryForm from './ServiceCategoryForm';
import ServiceCategoryCard from './ServiceCategoryCard';
import ServiceStatusTracker from './ServiceStatusTracker';
import CustomerCategoryForm from './CustomerCategoryForm';
import CategoryStats from './CategoryStats';
import ImportExport from '../common/ImportExport';
import serviceCategoryService from '../../services/serviceCategoryService';
import customerCategoryService from '../../services/customerCategoryService';
import { showSuccess, showError } from '../common/NotificationSystem';
import '../../styles/design-system.css';

/**
 * Service Categories Management Component
 * 
 * Manages service categories with CRUD operations, search, filtering, and different view modes
 */
const ServiceCategories = () => {
  const dispatch = useDispatch();
  
  // Local state
  const [categories, setCategories] = useState([]);
  const [customerCategories, setCustomerCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(12);
  const [activeTab, setActiveTab] = useState('service-categories'); // 'service-categories', 'customer-categories', 'service-status'
  const [selectedServiceStatus, setSelectedServiceStatus] = useState('all');

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showCustomerCategoryModal, setShowCustomerCategoryModal] = useState(false);
  const [selectedCustomerCategory, setSelectedCustomerCategory] = useState(null);

  // Import/Export states
  const [showImportExport, setShowImportExport] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit: itemsPerPage,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter === 'active' ? '1' : '0' })
      };

      const data = await serviceCategoryService.getServiceCategories(params);

      if (data.success) {
        setCategories(data.data.categories);
        setTotalPages(data.data.pagination.totalPages);
        setTotalItems(data.data.pagination.totalItems);
      } else {
        throw new Error(data.message || 'Failed to fetch categories');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching categories:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch customer categories
  const fetchCustomerCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit: itemsPerPage,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter === 'active' ? '1' : '0' })
      };

      const response = await customerCategoryService.getCustomerCategories(params);

      if (response.success) {
        setCustomerCategories(response.data.categories || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
        setTotalItems(response.data.pagination?.totalItems || 0);
      } else {
        throw new Error(response.message || 'Failed to fetch customer categories');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching customer categories:', err);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    if (activeTab === 'service-categories') {
      fetchCategories();
    } else if (activeTab === 'customer-categories') {
      fetchCustomerCategories();
    }
  }, [currentPage, searchTerm, statusFilter, activeTab]);

  // Handlers
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleStatusFilter = (status) => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleCreate = () => {
    setSelectedCategory(null);
    setShowCreateModal(true);
  };

  const handleEdit = (category) => {
    setSelectedCategory(category);
    setShowEditModal(true);
  };

  const handleView = (category) => {
    setSelectedCategory(category);
    setShowViewModal(true);
  };

  const handleDelete = (category) => {
    setSelectedCategory(category);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      const data = await serviceCategoryService.deleteServiceCategory(selectedCategory.id);

      if (data.success) {
        await fetchCategories(); // Refresh the list
        setShowDeleteDialog(false);
        setSelectedCategory(null);
      } else {
        throw new Error(data.message || 'Failed to delete category');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error deleting category:', err);
    }
  };

  const handleFormSuccess = async () => {
    if (activeTab === 'service-categories') {
      await fetchCategories(); // Refresh the list
    } else if (activeTab === 'customer-categories') {
      await fetchCustomerCategories(); // Refresh customer categories
    }
    setShowCreateModal(false);
    setShowEditModal(false);
    setShowCustomerCategoryModal(false);
    setSelectedCategory(null);
    setSelectedCustomerCategory(null);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Customer Category handlers
  const handleCreateCustomerCategory = () => {
    setSelectedCustomerCategory(null);
    setShowCustomerCategoryModal(true);
  };

  const handleEditCustomerCategory = (category) => {
    setSelectedCustomerCategory(category);
    setShowCustomerCategoryModal(true);
  };

  const handleDeleteCustomerCategory = async (category) => {
    try {
      const response = await customerCategoryService.deleteCustomerCategory(category.id);

      if (response.success) {
        await fetchCustomerCategories(); // Refresh the list
        showSuccess('Customer category deleted successfully');
      } else {
        throw new Error(response.message || 'Failed to delete customer category');
      }
    } catch (err) {
      setError(err.message);
      showError('Failed to delete customer category: ' + err.message);
      console.error('Error deleting customer category:', err);
    }
  };

  // Service Status handlers
  const handleServiceStatusSelect = (status) => {
    setSelectedServiceStatus(status);
  };

  // Tab handlers
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setCurrentPage(1);
    setSearchTerm('');
    setStatusFilter('all');
  };

  // Export/Import handlers
  const handleExport = async (format = 'xlsx') => {
    try {
      setExportLoading(true);

      const filters = {
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter === 'active' ? '1' : '0' })
      };

      if (activeTab === 'service-categories') {
        const response = await serviceCategoryService.exportServiceCategories(filters, format);
        const filename = serviceCategoryService.generateExportFilename(format);
        serviceCategoryService.downloadFile(response.data, filename);
        showSuccess(`Service categories exported successfully as ${format.toUpperCase()}`);
      } else if (activeTab === 'customer-categories') {
        const response = await customerCategoryService.exportCustomerCategories(filters, format);
        const filename = customerCategoryService.generateExportFilename(format);
        customerCategoryService.downloadFile(response.data, filename);
        showSuccess(`Customer categories exported successfully as ${format.toUpperCase()}`);
      }
    } catch (error) {
      console.error('Export error:', error);
      const categoryType = activeTab === 'service-categories' ? 'service' : 'customer';
      showError(`Failed to export ${categoryType} categories: ` + error.message);
    } finally {
      setExportLoading(false);
    }
  };

  const handleImport = async (file) => {
    try {
      setImportLoading(true);

      if (activeTab === 'service-categories') {
        // Validate file
        serviceCategoryService.validateImportFile(file);
        const result = await serviceCategoryService.importServiceCategories(file);

        if (result.success) {
          showSuccess(result.message);
          await fetchCategories(); // Refresh the list
          setShowImportExport(false);
        } else {
          showError(result.message || 'Import failed');
        }
      } else if (activeTab === 'customer-categories') {
        // Validate file
        customerCategoryService.validateImportFile(file);
        const result = await customerCategoryService.importCustomerCategories(file);

        if (result.success) {
          showSuccess(result.message);
          await fetchCustomerCategories(); // Refresh the list
          setShowImportExport(false);
        } else {
          showError(result.message || 'Import failed');
        }
      }

      return result;
    } catch (error) {
      console.error('Import error:', error);
      const categoryType = activeTab === 'service-categories' ? 'service' : 'customer';
      showError(`Failed to import ${categoryType} categories: ` + error.message);
      throw error;
    } finally {
      setImportLoading(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      if (activeTab === 'service-categories') {
        const response = await serviceCategoryService.downloadTemplate();
        const filename = 'service_category_import_template.xlsx';
        serviceCategoryService.downloadFile(response.data, filename);
        showSuccess('Service category template downloaded successfully');
      } else if (activeTab === 'customer-categories') {
        const response = await customerCategoryService.downloadTemplate();
        const filename = 'customer_category_import_template.xlsx';
        customerCategoryService.downloadFile(response.data, filename);
        showSuccess('Customer category template downloaded successfully');
      }
    } catch (error) {
      console.error('Template download error:', error);
      const categoryType = activeTab === 'service-categories' ? 'service' : 'customer';
      showError(`Failed to download ${categoryType} category template: ` + error.message);
    }
  };

  if (loading && categories.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Service Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage service categories, customer categories, and service status tracking
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {activeTab === 'service-categories' && (
            <>
              <button
                onClick={() => setShowImportExport(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
              >
                <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                Import/Export
              </button>
              <button
                onClick={handleCreate}
                className="btn-primary"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Service Category
              </button>
            </>
          )}
          {activeTab === 'customer-categories' && (
            <>
              <button
                onClick={() => setShowImportExport(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
              >
                <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                Import/Export
              </button>
              <button
                onClick={handleCreateCustomerCategory}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <UserGroupIcon className="h-4 w-4 mr-2" />
                Add Customer Category
              </button>
            </>
          )}
        </div>
      </div>

      {/* Category Statistics */}
      <CategoryStats activeTab={activeTab} />

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => handleTabChange('service-categories')}
            className={clsx(
              'py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200',
              activeTab === 'service-categories'
                ? 'border-teal-500 text-teal-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            )}
          >
            <span>Service Categories</span>
            {activeTab === 'service-categories' && totalItems > 0 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                {totalItems}
              </span>
            )}
          </button>
          <button
            onClick={() => handleTabChange('customer-categories')}
            className={clsx(
              'py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200',
              activeTab === 'customer-categories'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            )}
          >
            <span>Customer Categories</span>
            {activeTab === 'customer-categories' && totalItems > 0 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                {totalItems}
              </span>
            )}
          </button>
          <button
            onClick={() => handleTabChange('service-status')}
            className={clsx(
              'py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
              activeTab === 'service-status'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            )}
          >
            <ChartBarIcon className="h-4 w-4 mr-1 inline" />
            Service Status
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'service-status' ? (
        <ServiceStatusTracker
          onStatusSelect={handleServiceStatusSelect}
          selectedStatus={selectedServiceStatus}
        />
      ) : (
        <>
          {/* Filters and Search */}
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={activeTab === 'service-categories' ? "Search service categories..." : "Search customer categories..."}
                  value={searchTerm}
                  onChange={handleSearch}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="flex items-center space-x-4">
                {/* Status Filter - only for service categories */}
                {activeTab === 'service-categories' && (
                  <div className="flex items-center space-x-2">
                    <FunnelIcon className="h-5 w-5 text-gray-400" />
                    <select
                      value={statusFilter}
                      onChange={(e) => handleStatusFilter(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                )}

                {/* View Mode Toggle */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={clsx(
                      'p-2 text-sm font-medium rounded-l-md',
                      viewMode === 'grid'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    )}
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={clsx(
                      'p-2 text-sm font-medium rounded-r-md border-l',
                      viewMode === 'list'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    )}
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Error Message */}
      {activeTab !== 'service-status' && error && (
        <ErrorMessage
          message={error}
          onDismiss={() => setError(null)}
        />
      )}

      {/* Content based on active tab */}
      {activeTab !== 'service-status' && (
        <>
          {/* Categories Grid/List */}
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {activeTab === 'service-categories' ? (
                categories.map((category) => (
                  <ServiceCategoryCard
                    key={category.id}
                    category={category}
                    onEdit={handleEdit}
                    onView={handleView}
                    onDelete={handleDelete}
                  />
                ))
              ) : (
                customerCategories.map((category) => (
                  <div key={category.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
                    <div className="p-4 border-b border-gray-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {category.categoryName}
                          </h3>
                          <p className="text-sm text-gray-500">{category.description || 'No description'}</p>
                        </div>
                        <span className={clsx(
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        )}>
                          {category.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                    <div className="p-4 space-y-3">
                      {category.discountPercentage > 0 && (
                        <div className="text-sm text-green-600">
                          {category.discountPercentage}% Discount
                        </div>
                      )}
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-gray-500">
                          Sort Order: {category.sortOrder || 0}
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditCustomerCategory(category)}
                            className="text-blue-400 hover:text-blue-600"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteCustomerCategory(category)}
                            className="text-red-400 hover:text-red-600"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {activeTab === 'service-categories' ? (
                  categories.map((category) => (
                    <li key={category.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-4 h-4 rounded-full mr-3"
                            style={{ backgroundColor: category.color }}
                          />
                          <div>
                            <h3 className="text-sm font-medium text-gray-900">
                              {category.serviceCategory}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {category.description || 'No description'}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={clsx(
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            category.serviceStatus === 1
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          )}>
                            {category.serviceStatus === 1 ? 'Active' : 'Inactive'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {category.servicesCount || 0} services
                          </span>
                          <div className="action-button-group">
                            <button
                              onClick={() => handleView(category)}
                              className="action-button view"
                            >
                              <EyeIcon className="h-3.5 w-3.5" />
                            </button>
                            <button
                              onClick={() => handleEdit(category)}
                              className="action-button edit"
                            >
                              <PencilIcon className="h-3.5 w-3.5" />
                            </button>
                            <button
                              onClick={() => handleDelete(category)}
                              className="action-button delete"
                            >
                              <TrashIcon className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  customerCategories.map((category) => (
                    <li key={category.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-10 h-10 rounded-lg flex items-center justify-center mr-3"
                            style={{ backgroundColor: category.color || '#10B981' }}
                          >
                            <UserGroupIcon className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-900">
                              {category.categoryName}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {category.description || 'No description'}
                            </p>
                            {category.discountPercentage > 0 && (
                              <p className="text-xs text-green-600">
                                {category.discountPercentage}% Discount
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <span className={clsx(
                              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                              category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            )}>
                              {category.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          <div className="flex space-x-1">
                            <button
                              onClick={() => handleEditCustomerCategory(category)}
                              className="text-blue-400 hover:text-blue-600"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteCustomerCategory(category)}
                              className="text-red-400 hover:text-red-600"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))
                )}
              </ul>
            </div>
          )}
        </>
      )}

      {/* Empty State */}
      {activeTab !== 'service-status' && !loading && (
        (activeTab === 'service-categories' && categories.length === 0) ||
        (activeTab === 'customer-categories' && customerCategories.length === 0)
      ) && (
        <div className="text-center py-12">
          {activeTab === 'service-categories' ? (
            <Squares2X2Icon className="mx-auto h-12 w-12 text-gray-400" />
          ) : (
            <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
          )}
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No {activeTab === 'service-categories' ? 'service' : 'customer'} categories found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || (activeTab === 'service-categories' && statusFilter !== 'all')
              ? 'Try adjusting your search or filter criteria.'
              : `Get started by creating your first ${activeTab === 'service-categories' ? 'service' : 'customer'} category.`
            }
          </p>
          {(!searchTerm && (activeTab !== 'service-categories' || statusFilter === 'all')) && (
            <div className="mt-6">
              <button
                onClick={activeTab === 'service-categories' ? handleCreate : handleCreateCustomerCategory}
                className={clsx(
                  activeTab === 'service-categories'
                    ? 'btn-primary'
                    : 'btn-secondary'
                )}
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add {activeTab === 'service-categories' ? 'Service' : 'Customer'} Category
              </button>
            </div>
          )}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
        />
      )}

      {/* Modals */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create Service Category"
        size="lg"
      >
        <ServiceCategoryForm
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>

      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Service Category"
        size="lg"
      >
        <ServiceCategoryForm
          category={selectedCategory}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowEditModal(false)}
        />
      </Modal>

      <Modal
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
        title="Service Category Details"
        size="lg"
      >
        {selectedCategory && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <p className="mt-1 text-sm text-gray-900">{selectedCategory.serviceCategory}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <span className={clsx(
                  'mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  selectedCategory.serviceStatus === 1
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                )}>
                  {selectedCategory.serviceStatus === 1 ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Color</label>
                <div className="mt-1 flex items-center">
                  <div 
                    className="w-6 h-6 rounded-full mr-2"
                    style={{ backgroundColor: selectedCategory.color }}
                  />
                  <span className="text-sm text-gray-900">{selectedCategory.color}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Services Count</label>
                <p className="mt-1 text-sm text-gray-900">{selectedCategory.servicesCount || 0}</p>
              </div>
            </div>
            {selectedCategory.description && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <p className="mt-1 text-sm text-gray-900">{selectedCategory.description}</p>
              </div>
            )}
            {selectedCategory.form && Object.keys(selectedCategory.form).length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Dynamic Form Configuration</label>
                <pre className="mt-1 text-xs text-gray-600 bg-gray-50 p-3 rounded-md overflow-auto">
                  {JSON.stringify(selectedCategory.form, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </Modal>

      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title="Delete Service Category"
        message={`Are you sure you want to delete "${selectedCategory?.serviceCategory}"? This action cannot be undone.`}
        confirmText="Delete"
        confirmButtonClass="bg-red-600 hover:bg-red-700 focus:ring-red-500"
      />

      {/* Customer Category Modal */}
      <Modal
        isOpen={showCustomerCategoryModal}
        onClose={() => setShowCustomerCategoryModal(false)}
        title={selectedCustomerCategory ? "Edit Customer Category" : "Create Customer Category"}
        size="lg"
      >
        <CustomerCategoryForm
          customerCategory={selectedCustomerCategory}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCustomerCategoryModal(false)}
        />
      </Modal>

      {/* Import/Export Modal */}
      <ImportExport
        isOpen={showImportExport}
        onClose={() => setShowImportExport(false)}
        module={activeTab}
        onExport={handleExport}
        onImport={handleImport}
        onDownloadTemplate={handleDownloadTemplate}
        exportLoading={exportLoading}
        importLoading={importLoading}
        onImportComplete={async () => {
          if (activeTab === 'service-categories') {
            await fetchCategories();
          } else if (activeTab === 'customer-categories') {
            await fetchCustomerCategories();
          }
          setShowImportExport(false);
        }}
      />
    </div>
  );
};

export default ServiceCategories;
