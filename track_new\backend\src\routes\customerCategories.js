import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest } from '../middleware/validation.js';
import { uploadToMemory, handleUploadError } from '../middleware/fileUpload.js';
import {
  getCustomerCategories,
  getCustomerCategoryById,
  createCustomerCategory,
  updateCustomerCategory,
  deleteCustomerCategory,
  getCustomerCategoriesStats,
  exportCustomerCategories,
  importCustomerCategories,
  downloadCustomerCategoryTemplate
} from '../controllers/customerCategoryController.js';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();

/**
 * Customer Categories Routes
 * 
 * All routes require authentication
 */

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @route   GET /api/customer-categories/stats
 * @desc    Get customer categories statistics
 * @access  Private
 */
router.get('/stats', getCustomerCategoriesStats);

/**
 * @route   GET /api/customer-categories/export
 * @desc    Export customer categories to CSV/Excel
 * @access  Private
 * @query   format, status, search
 */
router.get('/export',
  [
    query('format').optional().isIn(['csv', 'xlsx']).withMessage('Format must be csv or xlsx'),
    query('status').optional().isIn(['0', '1']).withMessage('Status must be 0 or 1'),
    query('search').optional().isString().withMessage('Search must be a string')
  ],
  validateRequest,
  exportCustomerCategories
);

/**
 * @route   GET /api/customer-categories/template
 * @desc    Download customer category import template
 * @access  Private
 */
router.get('/template', downloadCustomerCategoryTemplate);

/**
 * @route   GET /api/customer-categories
 * @desc    Get all customer categories with pagination and search
 * @access  Private
 * @query   page, limit, search, sortBy, sortOrder
 */
router.get('/', getCustomerCategories);

/**
 * @route   GET /api/customer-categories/:id
 * @desc    Get single customer category by ID
 * @access  Private
 */
router.get('/:id', getCustomerCategoryById);

/**
 * @route   POST /api/customer-categories
 * @desc    Create new customer category
 * @access  Private
 * @body    categoryName, description, color, isActive, sortOrder, discountPercentage, specialTerms
 */
router.post('/', createCustomerCategory);

/**
 * @route   PUT /api/customer-categories/:id
 * @desc    Update customer category
 * @access  Private
 * @body    categoryName, description, color, isActive, sortOrder, discountPercentage, specialTerms
 */
router.put('/:id', updateCustomerCategory);

/**
 * @route   DELETE /api/customer-categories/:id
 * @desc    Delete customer category
 * @access  Private
 */
router.delete('/:id', deleteCustomerCategory);

/**
 * @route   POST /api/customer-categories/import
 * @desc    Import customer categories from CSV/Excel file
 * @access  Private
 * @body    file (multipart/form-data)
 */
router.post('/import',
  uploadToMemory.single('file'),
  handleUploadError,
  importCustomerCategories
);

export default router;
