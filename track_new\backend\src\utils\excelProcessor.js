import XLSX from 'xlsx';
import fs from 'fs';

/**
 * Parse Excel/CSV file and return data as JSON
 * @param {Buffer|string} fileData - File buffer or file path
 * @param {Object} options - Processing options
 * @returns {Array} Parsed data array
 */
export const parseExcelFile = (fileData, options = {}) => {
  try {
    let workbook;
    
    // Handle different input types
    if (Buffer.isBuffer(fileData)) {
      workbook = XLSX.read(fileData, { type: 'buffer' });
    } else if (typeof fileData === 'string') {
      workbook = XLSX.readFile(fileData);
    } else {
      throw new Error('Invalid file data type');
    }
    
    // Get the first worksheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1, // Use first row as header
      defval: '', // Default value for empty cells
      ...options
    });
    
    if (jsonData.length === 0) {
      throw new Error('File is empty or contains no data');
    }
    
    // Extract headers and data
    const headers = jsonData[0];
    const rows = jsonData.slice(1);
    
    // Convert to object array
    const result = rows.map((row, index) => {
      const obj = {};
      headers.forEach((header, colIndex) => {
        if (header) {
          obj[header.toString().trim()] = row[colIndex] || '';
        }
      });
      obj._rowNumber = index + 2; // +2 because we start from row 2 (after header)
      return obj;
    });
    
    return result;
  } catch (error) {
    throw new Error(`Failed to parse Excel file: ${error.message}`);
  }
};

/**
 * Validate customer data from Excel
 * @param {Array} data - Array of customer objects
 * @returns {Object} Validation result with valid data and errors
 */
export const validateCustomerData = (data) => {
  const validData = [];
  const errors = [];
  
  // Define field mappings (Excel column names to database fields) - Complete field set
  const fieldMappings = {
    'firstName': ['firstName', 'first_name', 'First Name', 'fname'],
    'lastName': ['lastName', 'last_name', 'Last Name', 'lname'],
    'email': ['email', 'Email', 'Email Address', 'emailAddress'],
    'phone': ['phone', 'Phone', 'contactNumber', 'contact_number', 'Contact Number', 'mobile'],
    'alternatePhone': ['alternatePhone', 'alternate_phone', 'Alternative Number', 'Alternate Phone', 'secondaryPhone'],
    'businessName': ['businessName', 'business_name', 'Business Name', 'companyName', 'company_name'],
    'customerType': ['customerType', 'customer_type', 'Customer Type', 'type'],
    'address': ['address', 'Address', 'street_address'],
    'city': ['city', 'City', 'City Name', 'city_name'],
    'state': ['state', 'State', 'State Name', 'state_name'],
    'districtName': ['districtName', 'district_name', 'District Name', 'district'],
    'pincode': ['pincode', 'Pincode', 'zip', 'postal_code'],
    'gstNumber': ['gstNumber', 'gst_number', 'GST Number', 'gst'],
    'panNumber': ['panNumber', 'pan_number', 'PAN Number', 'pan'],
    'notes': ['notes', 'Notes', 'remarks', 'comments'],
    'creditLimit': ['creditLimit', 'credit_limit', 'Credit Limit'],
    'openingBalance': ['openingBalance', 'opening_balance', 'Opening Balance'],
    'birthDate': ['birthDate', 'birth_date', 'Birth Date', 'dob', 'dateOfBirth'],
    'anniversaryDate': ['anniversaryDate', 'anniversary_date', 'Anniversary Date', 'anniversary'],
    'customerCategory': ['customerCategory', 'customer_category', 'Customer Category', 'category']
  };
  
  data.forEach((row, index) => {
    const rowNumber = row._rowNumber || index + 1;
    const customerData = {};
    const rowErrors = [];
    
    // Map fields from Excel columns to database fields
    Object.keys(fieldMappings).forEach(dbField => {
      const possibleColumns = fieldMappings[dbField];
      let value = '';
      
      // Find the first matching column that has a value
      for (const column of possibleColumns) {
        if (row[column] !== undefined && row[column] !== '') {
          value = row[column];
          break;
        }
      }
      
      customerData[dbField] = value;
    });
    
    // Validate required fields
    if (!customerData.firstName || customerData.firstName.trim().length < 2) {
      rowErrors.push('First name is required and must be at least 2 characters');
    }
    
    if (!customerData.phone || !/^[0-9]{10}$/.test(customerData.phone.toString().replace(/\D/g, ''))) {
      rowErrors.push('Phone number must be exactly 10 digits');
    } else {
      // Clean phone number
      customerData.phone = customerData.phone.toString().replace(/\D/g, '');
    }
    
    // Validate email if provided
    if (customerData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerData.email)) {
      rowErrors.push('Invalid email format');
    }
    
    // Validate GST number if provided - Allow empty GST numbers
    if (customerData.gstNumber && customerData.gstNumber.trim() !== '') {
      const gstNumber = customerData.gstNumber.trim().toUpperCase();
      // Indian GST format: 2 digits state code + 10 characters PAN + 1 digit entity number + 1 digit default 'Z' + 1 digit check code
      if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(gstNumber)) {
        rowErrors.push('Invalid GST number format. Expected format: 22AAAAA0000A1Z5');
      } else {
        // Store the normalized GST number
        customerData.gstNumber = gstNumber;
      }
    } else {
      // Set empty GST numbers to null to avoid database constraint issues
      customerData.gstNumber = null;
    }
    
    // Validate PAN number if provided
    if (customerData.panNumber && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(customerData.panNumber)) {
      rowErrors.push('Invalid PAN number format');
    }
    
    // Validate customer type
    if (customerData.customerType && !['individual', 'business', 'corporate', 'government'].includes(customerData.customerType.toLowerCase())) {
      customerData.customerType = 'individual'; // Default value
    } else if (customerData.customerType) {
      customerData.customerType = customerData.customerType.toLowerCase();
    } else {
      customerData.customerType = 'individual';
    }
    
    // Validate numeric fields
    if (customerData.creditLimit) {
      const creditLimit = parseFloat(customerData.creditLimit);
      if (isNaN(creditLimit) || creditLimit < 0) {
        rowErrors.push('Credit limit must be a valid positive number');
      } else {
        customerData.creditLimit = creditLimit;
      }
    } else {
      customerData.creditLimit = 0;
    }
    
    if (customerData.openingBalance) {
      const openingBalance = parseFloat(customerData.openingBalance);
      if (isNaN(openingBalance)) {
        rowErrors.push('Opening balance must be a valid number');
      } else {
        customerData.openingBalance = openingBalance;
      }
    } else {
      customerData.openingBalance = 0;
    }
    
    // Clean alternate phone if provided
    if (customerData.alternatePhone) {
      customerData.alternatePhone = customerData.alternatePhone.toString().replace(/\D/g, '');
      if (customerData.alternatePhone.length !== 10) {
        rowErrors.push('Alternate phone number must be exactly 10 digits');
      }
    }

    // Validate date fields
    if (customerData.birthDate) {
      const birthDate = new Date(customerData.birthDate);
      if (isNaN(birthDate.getTime())) {
        rowErrors.push('Birth date must be a valid date (YYYY-MM-DD format)');
      } else {
        customerData.birthDate = birthDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
      }
    }

    if (customerData.anniversaryDate) {
      const anniversaryDate = new Date(customerData.anniversaryDate);
      if (isNaN(anniversaryDate.getTime())) {
        rowErrors.push('Anniversary date must be a valid date (YYYY-MM-DD format)');
      } else {
        customerData.anniversaryDate = anniversaryDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
      }
    }

    // Validate customer category (if provided, should be a valid category name)
    if (customerData.customerCategory && customerData.customerCategory.trim() !== '') {
      customerData.customerCategory = customerData.customerCategory.trim();
    } else {
      customerData.customerCategory = null;
    }
    
    if (rowErrors.length > 0) {
      errors.push({
        row: rowNumber,
        errors: rowErrors,
        data: customerData
      });
    } else {
      validData.push(customerData);
    }
  });
  
  return {
    validData,
    errors,
    totalRows: data.length,
    validRows: validData.length,
    errorRows: errors.length
  };
};

/**
 * Generate customer import template
 * @returns {Buffer} Excel file buffer
 */
export const generateCustomerTemplate = () => {
  const templateData = [
    // Headers - Complete field set matching old project requirements
    [
      'First Name', 'Last Name', 'Contact Number', 'Alternative Number', 'Email',
      'Opening Balance', 'Birth Date', 'Anniversary Date', 'Business Name', 'GST Number',
      'Address', 'Pincode', 'State Name', 'District Name', 'City Name',
      'Customer Category', 'Notes', 'Customer Type', 'Credit Limit'
    ],
    // Sample data row 1
    [
      'John', 'Doe', '9876543210', '9876543211', '<EMAIL>',
      '1000', '1990-01-15', '2020-06-20', 'Doe Enterprises', '27ABCDE1234F1Z5',
      '123 Main Street, Sector 1', '400001', 'Maharashtra', 'Mumbai', 'Mumbai',
      'VIP Customer', 'Sample customer with all fields', 'business', '50000'
    ],
    // Sample data row 2
    [
      'Jane', 'Smith', '9876543212', '', '<EMAIL>',
      '500', '1985-05-10', '', 'Smith Corp', '',
      '456 Oak Avenue, Block B', '110001', 'Delhi', 'Central Delhi', 'Delhi',
      'Regular Customer', 'Another sample customer', 'individual', '25000'
    ]
  ];
  
  const worksheet = XLSX.utils.aoa_to_sheet(templateData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers');
  
  return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
};

/**
 * Clean up temporary files
 * @param {string} filePath - Path to file to clean up
 */
export const cleanupTempFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error('Error cleaning up temp file:', error);
  }
};

/**
 * Validate service category data
 * @param {Object} data - Service category data to validate
 * @returns {Object} Validation result
 */
export const validateServiceCategoryData = (data) => {
  const errors = [];

  // Required fields validation
  if (!data.categoryName || !data.categoryName.toString().trim()) {
    errors.push('Category name is required');
  } else if (data.categoryName.toString().trim().length < 2) {
    errors.push('Category name must be at least 2 characters long');
  } else if (data.categoryName.toString().trim().length > 100) {
    errors.push('Category name must not exceed 100 characters');
  }

  // Optional field validations
  if (data.description && data.description.toString().length > 500) {
    errors.push('Description must not exceed 500 characters');
  }

  if (data.icon && data.icon.toString().length > 100) {
    errors.push('Icon must not exceed 100 characters');
  }

  if (data.color && !/^#[0-9A-Fa-f]{6}$/.test(data.color.toString())) {
    errors.push('Color must be a valid hex color (e.g., #FF0000)');
  }

  if (data.sortOrder && (isNaN(data.sortOrder) || parseInt(data.sortOrder) < 0)) {
    errors.push('Sort order must be a non-negative number');
  }

  if (data.styleView && (isNaN(data.styleView) || parseInt(data.styleView) < 1)) {
    errors.push('Style view must be a positive number');
  }

  if (data.status && !['active', 'inactive'].includes(data.status.toString().toLowerCase())) {
    errors.push('Status must be either "Active" or "Inactive"');
  }

  if (data.isDefault && !['yes', 'no'].includes(data.isDefault.toString().toLowerCase())) {
    errors.push('Is Default must be either "Yes" or "No"');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Generate service category export file
 * @param {Array} categories - Array of service category data
 * @param {string} format - Export format (csv or xlsx)
 * @returns {Buffer} Export file buffer
 */
export const generateServiceCategoryExport = (categories, format = 'xlsx') => {
  const headers = [
    'ID',
    'Category Name',
    'Description',
    'Icon',
    'Color',
    'Sort Order',
    'Status',
    'Style View',
    'Is Default',
    'Services Count',
    'Created At',
    'Updated At'
  ];

  const data = [headers];

  categories.forEach(category => {
    data.push([
      category.id,
      category.categoryName,
      category.description,
      category.icon,
      category.color,
      category.sortOrder,
      category.status,
      category.styleView,
      category.isDefault,
      category.servicesCount,
      category.createdAt ? new Date(category.createdAt).toLocaleDateString() : '',
      category.updatedAt ? new Date(category.updatedAt).toLocaleDateString() : ''
    ]);
  });

  const worksheet = XLSX.utils.aoa_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Service Categories');

  return XLSX.write(workbook, {
    type: 'buffer',
    bookType: format === 'csv' ? 'csv' : 'xlsx'
  });
};

/**
 * Generate service category import template
 * @returns {Buffer} Template file buffer
 */
export const generateServiceCategoryTemplate = () => {
  const templateData = [
    // Headers
    [
      'categoryName',
      'description',
      'icon',
      'color',
      'sortOrder',
      'status',
      'styleView',
      'isDefault'
    ],
    // Instructions row
    [
      'REQUIRED: Category name (2-100 chars)',
      'OPTIONAL: Description (max 500 chars)',
      'OPTIONAL: Icon class or URL',
      'OPTIONAL: Hex color (e.g., #3B82F6)',
      'OPTIONAL: Sort order (number)',
      'OPTIONAL: Active or Inactive',
      'OPTIONAL: Style view (number)',
      'OPTIONAL: Yes or No'
    ],
    // Sample data row 1
    [
      'Computer Repair',
      'Desktop and laptop repair services',
      'fas fa-desktop',
      '#3B82F6',
      '1',
      'Active',
      '1',
      'No'
    ],
    // Sample data row 2
    [
      'Mobile Repair',
      'Smartphone and tablet repair services',
      'fas fa-mobile-alt',
      '#10B981',
      '2',
      'Active',
      '1',
      'No'
    ],
    // Sample data row 3
    [
      'Network Setup',
      'Network installation and configuration',
      'fas fa-network-wired',
      '#F59E0B',
      '3',
      'Inactive',
      '2',
      'Yes'
    ]
  ];

  const worksheet = XLSX.utils.aoa_to_sheet(templateData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Service Categories');

  return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
};

// Customer Category specific functions
export const validateCustomerCategoryData = (data) => {
  const errors = [];

  // Required fields validation
  if (!data.categoryName || !data.categoryName.toString().trim()) {
    errors.push('Category name is required');
  }

  // Category name length validation
  if (data.categoryName && data.categoryName.toString().length > 255) {
    errors.push('Category name must be less than 255 characters');
  }

  // Discount percentage validation
  if (data.discountPercentage !== undefined && data.discountPercentage !== null && data.discountPercentage !== '') {
    const discount = parseFloat(data.discountPercentage);
    if (isNaN(discount) || discount < 0 || discount > 100) {
      errors.push('Discount percentage must be between 0 and 100');
    }
  }

  // Color validation (if provided)
  if (data.color && !/^#[0-9A-F]{6}$/i.test(data.color.toString())) {
    errors.push('Color must be a valid hex color code (e.g., #10B981)');
  }

  // Sort order validation
  if (data.sortOrder !== undefined && data.sortOrder !== null && data.sortOrder !== '') {
    const sortOrder = parseInt(data.sortOrder);
    if (isNaN(sortOrder) || sortOrder < 0) {
      errors.push('Sort order must be a non-negative number');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const generateCustomerCategoryExport = (data, format = 'xlsx') => {
  const workbook = XLSX.utils.book_new();

  // Define headers
  const headers = [
    'ID',
    'Category Name',
    'Description',
    'Color',
    'Status',
    'Sort Order',
    'Discount Percentage',
    'Special Terms',
    'Created At',
    'Updated At'
  ];

  // Transform data for export
  const exportData = data.map(item => [
    item.id,
    item.categoryName,
    item.description || '',
    item.color || '#10B981',
    item.status,
    item.sortOrder || 0,
    item.discountPercentage || 0,
    item.specialTerms || '',
    item.createdAt ? new Date(item.createdAt).toLocaleDateString() : '',
    item.updatedAt ? new Date(item.updatedAt).toLocaleDateString() : ''
  ]);

  // Add headers to the beginning
  const worksheetData = [headers, ...exportData];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  worksheet['!cols'] = [
    { width: 10 }, // ID
    { width: 25 }, // Category Name
    { width: 30 }, // Description
    { width: 12 }, // Color
    { width: 10 }, // Status
    { width: 12 }, // Sort Order
    { width: 18 }, // Discount Percentage
    { width: 30 }, // Special Terms
    { width: 15 }, // Created At
    { width: 15 }  // Updated At
  ];

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Customer Categories');

  // Generate buffer based on format
  if (format === 'csv') {
    return XLSX.write(workbook, { type: 'buffer', bookType: 'csv' });
  } else {
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }
};

export const generateCustomerCategoryTemplate = () => {
  const workbook = XLSX.utils.book_new();

  // Define headers with descriptions
  const headers = [
    'categoryName',
    'description',
    'color',
    'status',
    'sortOrder',
    'discountPercentage',
    'specialTerms'
  ];

  // Sample data for template
  const sampleData = [
    [
      'VIP Customer',
      'Premium customers with special privileges',
      '#F59E0B',
      'Active',
      1,
      10.00,
      'Priority support and exclusive offers'
    ],
    [
      'Corporate Client',
      'Business customers with bulk discounts',
      '#10B981',
      'Active',
      2,
      15.00,
      'Net 30 payment terms'
    ],
    [
      'Regular Customer',
      'Standard customers',
      '#3B82F6',
      'Active',
      3,
      0.00,
      ''
    ]
  ];

  // Create worksheet data
  const worksheetData = [headers, ...sampleData];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  worksheet['!cols'] = [
    { width: 25 }, // Category Name
    { width: 35 }, // Description
    { width: 12 }, // Color
    { width: 10 }, // Status
    { width: 12 }, // Sort Order
    { width: 18 }, // Discount Percentage
    { width: 35 }  // Special Terms
  ];

  // Add instructions sheet
  const instructionsData = [
    ['Customer Category Import Template Instructions'],
    [''],
    ['Field Descriptions:'],
    ['categoryName', 'Required. Unique name for the customer category (max 255 characters)'],
    ['description', 'Optional. Description of the customer category'],
    ['color', 'Optional. Hex color code (e.g., #10B981). Default: #10B981'],
    ['status', 'Required. Either "Active" or "Inactive"'],
    ['sortOrder', 'Optional. Number for sorting categories. Default: 0'],
    ['discountPercentage', 'Optional. Discount percentage (0-100). Default: 0'],
    ['specialTerms', 'Optional. Special terms and conditions for this category'],
    [''],
    ['Important Notes:'],
    ['• Category Name must be unique'],
    ['• Discount Percentage must be between 0 and 100'],
    ['• Color must be a valid hex color code'],
    ['• Status must be either "Active" or "Inactive"'],
    ['• Sort Order must be a non-negative number'],
    ['• Remove sample data before importing your data'],
    ['• Maximum file size: 10MB'],
    ['• Supported formats: .xlsx, .xls, .csv']
  ];

  const instructionsWorksheet = XLSX.utils.aoa_to_sheet(instructionsData);
  instructionsWorksheet['!cols'] = [{ width: 30 }, { width: 60 }];

  // Add worksheets to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Customer Categories');
  XLSX.utils.book_append_sheet(workbook, instructionsWorksheet, 'Instructions');

  // Generate Excel buffer
  return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
};
